<?php

use Illuminate\Support\Facades\Route;
use Modules\Portfolios\App\Http\Controllers\PortfoliosController;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('portfolios', PortfoliosController::class)->names('portfolios');
    Route::get('portfolios-cards', [PortfoliosController::class, 'cards'])->name('portfolios.cards');

    // Portfolio özellik route'ları
    Route::get('portfolios/{portfolio}/notes', [PortfoliosController::class, 'notes'])->name('portfolios.notes');
    Route::get('portfolios/{portfolio}/customers', [PortfoliosController::class, 'customers'])->name('portfolios.customers');
    Route::get('portfolios/{portfolio}/price-history', [PortfoliosController::class, 'priceHistory'])->name('portfolios.price-history');
    Route::get('portfolios/{portfolio}/offers', [PortfoliosController::class, 'offers'])->name('portfolios.offers');
    Route::get('portfolios/{portfolio}/appointments', [PortfoliosController::class, 'appointments'])->name('portfolios.appointments');
    Route::get('portfolios/{portfolio}/actions', [PortfoliosController::class, 'actions'])->name('portfolios.actions');

    // Portfolio Management Routes
    Route::prefix('portfolios/{portfolio}')->name('portfolios.')->group(function () {
        // Offers & Bids
        Route::get('offers', [PortfoliosController::class, 'offers'])->name('offers');
        Route::get('price-history', [PortfoliosController::class, 'priceHistory'])->name('price-history');

        // Notes & Documentation
        Route::get('notes', [PortfoliosController::class, 'notes'])->name('notes');
        Route::post('notes', [PortfoliosController::class, 'storeNote'])->name('notes.store');

        // Customer Management
        Route::get('customers', [PortfoliosController::class, 'customers'])->name('customers');
        Route::get('customer-history', [PortfoliosController::class, 'customerHistory'])->name('customer-history');

        // Appointments
        Route::get('appointments', [PortfoliosController::class, 'appointments'])->name('appointments');
        Route::post('appointments', [PortfoliosController::class, 'storeAppointment'])->name('appointments.store');

        // Status & Actions
        Route::get('status-history', [PortfoliosController::class, 'statusHistory'])->name('status-history');
        Route::get('actions', [PortfoliosController::class, 'actions'])->name('actions');
        Route::post('actions', [PortfoliosController::class, 'storeAction'])->name('actions.store');

        // Suitable Demands
        Route::get('suitable-demands', [PortfoliosController::class, 'suitableDemands'])->name('suitable-demands');
    });
});
