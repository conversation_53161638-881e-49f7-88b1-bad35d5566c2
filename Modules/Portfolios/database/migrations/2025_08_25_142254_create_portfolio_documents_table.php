<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_documents', function (Blueprint $table) {
            $table->id();
    $table->foreignUuid('company_id')->constrained('companies');
    $table->uuidMorphs('documentable');
    $table->string('path');
    $table->string('name')->nullable();
    $table->timestamps();

    $table->index(['documentable_type', 'documentable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_documents');
    }
};
