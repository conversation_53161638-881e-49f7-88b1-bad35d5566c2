<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Modules\Portfolios\App\Models\PortfolioType;
use Illuminate\Http\JsonResponse;

class PortfolioTypeController extends Controller
{
    /**
     * Get all portfolio types.
     */
    public function getPortfolioTypes(): JsonResponse
    {
        $portfolioTypes = PortfolioType::orderBy('name')
            ->get(['id', 'name']);

        return response()->json($portfolioTypes);
    }
}
