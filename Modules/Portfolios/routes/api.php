<?php

use Illuminate\Support\Facades\Route;
use Modules\Portfolios\App\Http\Controllers\PortfoliosController;
use Modules\Portfolios\App\Http\Controllers\Api\LocationController;
use Modules\Portfolios\App\Http\Controllers\Api\CategoryController;

Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
    Route::apiResource('portfolios', PortfoliosController::class)->names('portfolios');
});

// Location API routes
Route::get('cities/{city}/districts', [LocationController::class, 'getDistricts'])->name('api.cities.districts');
Route::get('districts/{district}/neighborhoods', [LocationController::class, 'getNeighborhoods'])->name('api.districts.neighborhoods');

// Category API routes
Route::get('categories/main', [CategoryController::class, 'getMainCategories'])->name('api.categories.main');
Route::get('categories/{category}/subcategories', [CategoryController::class, 'getSubCategories'])->name('api.categories.subcategories');
