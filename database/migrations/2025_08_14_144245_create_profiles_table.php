<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title')->nullable(); // Pozisyon/Ünvan
            $table->date('birth_date')->nullable(); // Doğum tarihi
            $table->enum('gender', ['male', 'female', 'other'])->nullable(); // Cinsiyet
            $table->string('phone', 20)->nullable(); // Telefon
            $table->string('whatsapp', 20)->nullable(); // WhatsApp
            $table->unsignedBigInteger('city_id')->nullable(); // Şehir ID
            $table->unsignedBigInteger('district_id')->nullable(); // İlçe ID
            $table->text('address')->nullable(); // Detay adres
            $table->text('bio')->nullable(); // Kişisel açıklama
            $table->string('avatar_url')->nullable(); // Avatar dosya yolu
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('city_id')->references('id')->on('iller')->onDelete('set null');
            $table->foreign('district_id')->references('id')->on('ilceler')->onDelete('set null');

            // Indexes
            $table->index('user_id');
            $table->index('city_id');
            $table->index('district_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('profiles');
    }
};
