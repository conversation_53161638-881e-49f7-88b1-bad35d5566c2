<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_images', function (Blueprint $table) {
            $table->id();
    $table->foreignUuid('company_id')->constrained('companies');
    $table->uuidMorphs('imageable');
    $table->string('path');
    $table->string('alt')->nullable();
    $table->integer('order')->default(0);
    $table->boolean('is_main')->default(false);
    $table->timestamps();

    $table->index(['imageable_type', 'imageable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_images');
    }
};
