<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Carbon\Carbon;

class ActivityController extends Controller
{
    public function getRecentActivities()
    {
        // Gerçek zamanlı aktiviteleri getir
        $activities = $this->generateRecentActivities();

        return response()->json([
            'activities' => $activities,
            'timestamp' => now()->toISOString()
        ]);
    }

    private function generateRecentActivities()
    {
        $activityTypes = [
            [
                'type' => 'customer_added',
                'title' => 'Yeni müşteri eklendi',
                'descriptions' => ['Ahmet Yılmaz', '<PERSON>hmet Kaya', 'Ayşe Demir', 'Fatma Özkan'],
                'icon' => 'user',
                'color' => 'blue'
            ],
            [
                'type' => 'sale_completed',
                'title' => 'Satış tamamlandı',
                'descriptions' => ['₺850,000', '₺1,200,000', '₺650,000', '₺2,100,000'],
                'icon' => 'home',
                'color' => 'green'
            ],
            [
                'type' => 'listing_added',
                'title' => 'Yeni ilan eklendi',
                'descriptions' => ['Bahçelievler Daire', 'Beşiktaş Villa', 'Kadıköy Ofis', 'Şişli Residence'],
                'icon' => 'document',
                'color' => 'yellow'
            ],
            [
                'type' => 'customer_call',
                'title' => 'Müşteri araması',
                'descriptions' => ['Melis Kaya', 'Can Özdemir', 'Selin Yıldız', 'Emre Şahin'],
                'icon' => 'phone',
                'color' => 'purple'
            ],
            [
                'type' => 'appointment_scheduled',
                'title' => 'Randevu planlandı',
                'descriptions' => ['Ev gezisi - 14:00', 'Ofis toplantısı - 16:30', 'Saha incelemesi - 10:00'],
                'icon' => 'calendar',
                'color' => 'indigo'
            ]
        ];

        $activities = [];
        $now = Carbon::now();

        for ($i = 0; $i < 4; $i++) {
            $activityType = $activityTypes[array_rand($activityTypes)];
            $description = $activityType['descriptions'][array_rand($activityType['descriptions'])];

            $timeAgo = $this->getRandomTimeAgo($i);

            $activities[] = [
                'type' => $activityType['type'],
                'title' => $activityType['title'],
                'description' => $description,
                'time' => $timeAgo,
                'icon' => $activityType['icon'],
                'color' => $activityType['color'],
                'timestamp' => $now->subMinutes(rand(1, 60))->toISOString()
            ];
        }

        return $activities;
    }

    private function getRandomTimeAgo($index)
    {
        $times = [
            '2 saniye önce',
            '5 dakika önce',
            '1 saat önce',
            '2 dakika önce',
            '30 saniye önce',
            '15 dakika önce',
            '45 dakika önce'
        ];

        return $times[array_rand($times)];
    }
}
