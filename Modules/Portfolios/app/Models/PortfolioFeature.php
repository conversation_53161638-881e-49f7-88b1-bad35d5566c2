<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PortfolioFeature extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_features';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'portfolio_id',
        'feature_id',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the portfolio this feature belongs to.
     */
    public function portfolio(): BelongsTo
    {
        return $this->belongsTo(Portfolio::class, 'portfolio_id', 'id');
    }

    /**
     * Get the feature.
     */
    public function feature(): BelongsTo
    {
        return $this->belongsTo(PortfolioCategoryFeature::class, 'feature_id', 'id');
    }

    /**
     * Scope a query to filter by portfolio.
     */
    public function scopeForPortfolio($query, string $portfolioId)
    {
        return $query->where('portfolio_id', $portfolioId);
    }

    /**
     * Scope a query to filter by feature.
     */
    public function scopeForFeature($query, int $featureId)
    {
        return $query->where('feature_id', $featureId);
    }
}
