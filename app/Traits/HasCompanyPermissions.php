<?php

namespace App\Traits;

use App\Models\Permission;
use App\Models\CompanyRole;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait HasCompanyPermissions
{
    /**
     * Kullanıcının şirket rollerini getir.
     */
    public function companyRoles(): BelongsToMany
    {
        return $this->belongsToMany(CompanyRole::class, 'company_user_roles')
            ->withPivot('company_id')
            ->withTimestamps();
    }

    /**
     * Kullanıcının doğrudan izinlerini getir (rol aracılığıyla değil).
     */
    public function directPermissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'company_user_permissions')
            ->withPivot('company_id')
            ->withTimestamps();
    }

    /**
     * Kullanıcının şirket rol ilişkilerini getir (pivot tablosu için).
     */
    public function companyUserRoles()
    {
        return \Illuminate\Support\Facades\DB::table('company_user_roles')
            ->where('user_id', $this->id);
    }

    /**
     * Kullanıcıya şirkette rol ata.
     */
    public function assignCompanyRole(string $companyId, CompanyRole|string $role): self
    {
        if (is_string($role)) {
            $role = CompanyRole::where('company_id', $companyId)
                ->where('name', $role)
                ->first();
        }

        if ($role && !$this->hasCompanyRole($companyId, $role)) {
            $this->companyRoles()->attach($role->id, ['company_id' => $companyId]);
        }

        return $this;
    }

    /**
     * Kullanıcıdan şirketteki rolü kaldır.
     */
    public function removeCompanyRole(string $companyId, CompanyRole|string $role): self
    {
        if (is_string($role)) {
            $role = CompanyRole::where('company_id', $companyId)
                ->where('name', $role)
                ->first();
        }

        if ($role) {
            $this->companyRoles()->wherePivot('company_id', $companyId)->detach($role->id);
        }

        return $this;
    }

    /**
     * Kullanıcının şirketteki rollerini senkronize et.
     */
    public function syncCompanyRoles(string $companyId, array $roleIds): self
    {
        // Önce mevcut rolleri kaldır
        $this->companyRoles()->wherePivot('company_id', $companyId)->detach();

        // Yeni rolleri ekle
        foreach ($roleIds as $roleId) {
            $role = CompanyRole::where('company_id', $companyId)
                ->where('id', $roleId)
                ->first();

            if ($role) {
                $this->companyRoles()->attach($role->id, ['company_id' => $companyId]);
            }
        }

        return $this;
    }

    /**
     * Kullanıcıya şirkette doğrudan izin ver.
     */
    public function giveCompanyPermission(string $companyId, Permission|string $permission): self
    {
        if (is_string($permission)) {
            $permission = Permission::findByName($permission);
        }

        if ($permission && !$this->hasDirectCompanyPermission($companyId, $permission)) {
            $this->directPermissions()->attach($permission->id, ['company_id' => $companyId]);
        }

        return $this;
    }

    /**
     * Kullanıcıdan şirketteki doğrudan izni kaldır.
     */
    public function revokeCompanyPermission(string $companyId, Permission|string $permission): self
    {
        if (is_string($permission)) {
            $permission = Permission::findByName($permission);
        }

        if ($permission) {
            $this->directPermissions()->wherePivot('company_id', $companyId)->detach($permission->id);
        }

        return $this;
    }

    /**
     * Kullanıcının şirkette rolü olup olmadığını kontrol et.
     */
    public function hasCompanyRole(string $companyId, CompanyRole|string $role): bool
    {
        if (is_string($role)) {
            return $this->companyRoles()
                ->wherePivot('company_id', $companyId)
                ->where('name', $role)
                ->exists();
        }

        return $this->companyRoles()
            ->wherePivot('company_id', $companyId)
            ->where('company_roles.id', $role->id)
            ->exists();
    }

    /**
     * Kullanıcının şirkette doğrudan izni olup olmadığını kontrol et.
     */
    public function hasDirectCompanyPermission(string $companyId, Permission|string $permission): bool
    {
        if (is_string($permission)) {
            return $this->directPermissions()
                ->wherePivot('company_id', $companyId)
                ->where('name', $permission)
                ->exists();
        }

        return $this->directPermissions()
            ->wherePivot('company_id', $companyId)
            ->where('permissions.id', $permission->id)
            ->exists();
    }

    /**
     * Kullanıcının şirkette izni olup olmadığını kontrol et (rol veya doğrudan).
     */
    public function hasCompanyPermission(string $companyId, string $permission): bool
    {
        // Doğrudan izin kontrolü
        if ($this->hasDirectCompanyPermission($companyId, $permission)) {
            return true;
        }

        // Roller aracılığıyla izin kontrolü
        $userRoles = $this->companyRoles()
            ->wherePivot('company_id', $companyId)
            ->with('permissions')
            ->get();

        foreach ($userRoles as $role) {
            if ($role->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

        /**
     * Kullanıcının şirketteki tüm izinlerini getir.
     */
    public function getAllCompanyPermissions(string $companyId): array
    {
        $permissions = collect();

        // Doğrudan izinler
        $directPermissions = $this->directPermissions()
            ->wherePivot('company_id', $companyId)
            ->pluck('name');
        $permissions = $permissions->merge($directPermissions);

        // Rol izinleri
        $userRoles = $this->companyRoles()
            ->wherePivot('company_id', $companyId)
            ->with('permissions')
            ->get();

        foreach ($userRoles as $role) {
            $rolePermissions = $role->permissions->pluck('name');
            $permissions = $permissions->merge($rolePermissions);
        }

        return $permissions->unique()->values()->toArray();
    }

    /**
     * Kullanıcının şirketteki rollerini getir.
     */
    public function getCompanyRoles(string $companyId): array
    {
        return $this->companyRoles()
            ->wherePivot('company_id', $companyId)
            ->pluck('name')
            ->toArray();
    }

    /**
     * Kullanıcının şirket sahibi olup olmadığını kontrol et.
     */
    public function isCompanyOwner(string $companyId): bool
    {
        return $this->company_id === $companyId &&
               $this->company &&
               $this->company->owner_id === $this->id;
    }
}
