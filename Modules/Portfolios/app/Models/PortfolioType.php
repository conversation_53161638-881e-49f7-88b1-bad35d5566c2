<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PortfolioType extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_types';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'slug',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the portfolios of this type.
     */
    public function portfolios(): HasMany
    {
        return $this->hasMany(Portfolio::class, 'type_id', 'id');
    }

    /**
     * Get types formatted for select dropdown.
     */
    public static function getSelectOptions(): array
    {
        return static::orderBy('name')->pluck('name', 'id')->toArray();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Prevent deletion if type has portfolios
        static::deleting(function ($type) {
            if ($type->portfolios()->exists()) {
                throw new \Exception('Bu tipe ait portföyler bulunduğu için silinemez.');
            }
        });
    }
}
