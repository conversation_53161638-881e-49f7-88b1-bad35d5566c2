<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolios', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('company_id')->constrained('companies');
            $table->foreignUuid('owner_id')->constrained('customers')->nullOnDelete();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->foreignId('agent_id')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete(); // ekleyen
            $table->enum('status', ['active', 'sold', 'rented', 'archived', 'inactive'])->default('active');
            $table->foreignId('type_id')->nullable()->constrained('portfolio_types')->nullOnDelete();
            $table->foreignId('main_category_id')->nullable()->constrained('portfolio_categories')->nullOnDelete();
            $table->foreignId('category_id')->nullable()->constrained('portfolio_categories')->cascadeOnDelete();
            $table->json('details')->nullable();
            $table->decimal('gross_area', 10, 2)->nullable(); // m²
            $table->decimal('net_area', 10, 2)->nullable(); // m²
            $table->decimal('price_per_m2', 15, 2)->nullable(); // m² fiyatı
            $table->enum('from_whom', ['owner', 'agent', 'developer','bank'])->default('owner')->nullable(); // kimden
            $table->boolean('is_loan_eligible')->default(false); // krediye uygun mu?
            $table->enum('deed_status', ['Kat mülkiyetli', 'Kat irtifaklı', 'Hisseli tapu', 'Müstakil tapu'])->default('Kat mülkiyetli')->nullable(); // tapu durumu
            $table->boolean('swap')->default(false); // takas mı?
            $table->boolean('pool')->default(false); // genel havuz
            $table->smallInteger('city_id')->nullable();
            $table->smallInteger('district_id')->nullable();
            $table->smallInteger('neighborhood_id')->nullable();
            $table->text('address')->nullable();
            $table->decimal('latitude', 10, 8)->nullable(); // enlem
            $table->decimal('longitude', 11, 8)->nullable(); // boylam
            $table->decimal('price', 15, 2)->nullable();
            $table->string('currency', 5)->default('TRY')->comment('Para Birimi Kodu (TRY, USD, EUR)');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status', 'city_id', 'district_id', 'category_id']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolios');
    }
};
