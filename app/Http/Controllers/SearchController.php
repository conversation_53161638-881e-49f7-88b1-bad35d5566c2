<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Portfolios\Models\Portfolio;
use Modules\Customer\Models\Customer;

class SearchController extends Controller
{
    /**
     * Global search functionality
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([
                'results' => [],
                'message' => 'Arama terimi en az 2 karakter olmalıdır'
            ]);
        }

        $results = [];
        
        try {
            // Search in Portfolios
            $portfolios = $this->searchPortfolios($query);
            $results = array_merge($results, $portfolios);
            
            // Search in Customers
            $customers = $this->searchCustomers($query);
            $results = array_merge($results, $customers);
            
            // Search in other modules (can be extended)
            // $reports = $this->searchReports($query);
            // $results = array_merge($results, $reports);
            
            // Sort results by relevance (title matches first)
            usort($results, function($a, $b) use ($query) {
                $aScore = $this->calculateRelevanceScore($a, $query);
                $bScore = $this->calculateRelevanceScore($b, $query);
                return $bScore - $aScore;
            });
            
            // Limit results
            $results = array_slice($results, 0, 10);
            
            return response()->json([
                'results' => $results,
                'total' => count($results),
                'query' => $query
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'results' => [],
                'error' => 'Arama sırasında bir hata oluştu',
                'message' => config('app.debug') ? $e->getMessage() : 'Arama sırasında bir hata oluştu'
            ], 500);
        }
    }
    
    /**
     * Search in portfolios
     */
    private function searchPortfolios(string $query): array
    {
        $results = [];
        
        try {
            $portfolios = Portfolio::where('title', 'LIKE', "%{$query}%")
                ->orWhere('description', 'LIKE', "%{$query}%")
                ->with(['type', 'category', 'city'])
                ->limit(5)
                ->get();
                
            foreach ($portfolios as $portfolio) {
                $results[] = [
                    'type' => 'portfolio',
                    'id' => $portfolio->id,
                    'title' => $portfolio->title,
                    'description' => $this->truncateText($portfolio->description ?? 'Açıklama bulunmuyor', 60),
                    'url' => route('portfolios.show', $portfolio->id),
                    'category' => 'Portföy',
                    'meta' => [
                        'type' => $portfolio->type->name ?? 'Bilinmiyor',
                        'category' => $portfolio->category->name ?? 'Bilinmiyor',
                        'city' => $portfolio->city->il_adi ?? 'Bilinmiyor',
                        'price' => $portfolio->price ? '₺' . number_format($portfolio->price) : 'Fiyat belirtilmemiş',
                        'status' => $this->getStatusText($portfolio->status)
                    ]
                ];
            }
        } catch (\Exception $e) {
            // Log error but don't break the search
            \Log::error('Portfolio search error: ' . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Search in customers
     */
    private function searchCustomers(string $query): array
    {
        $results = [];
        
        try {
            $customers = Customer::where('name', 'LIKE', "%{$query}%")
                ->orWhere('email', 'LIKE', "%{$query}%")
                ->orWhere('phone', 'LIKE', "%{$query}%")
                ->limit(5)
                ->get();
                
            foreach ($customers as $customer) {
                $results[] = [
                    'type' => 'customer',
                    'id' => $customer->id,
                    'title' => $customer->name,
                    'description' => $customer->email . ' • ' . ($customer->phone ?? 'Telefon yok'),
                    'url' => route('customers.show', $customer->id),
                    'category' => 'Müşteri',
                    'meta' => [
                        'email' => $customer->email,
                        'phone' => $customer->phone ?? 'Belirtilmemiş',
                        'type' => $customer->type ?? 'Bilinmiyor',
                        'status' => $this->getCustomerStatusText($customer->status ?? 'active')
                    ]
                ];
            }
        } catch (\Exception $e) {
            // Log error but don't break the search
            \Log::error('Customer search error: ' . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Calculate relevance score for sorting
     */
    private function calculateRelevanceScore(array $result, string $query): int
    {
        $score = 0;
        $queryLower = strtolower($query);
        $titleLower = strtolower($result['title']);
        $descriptionLower = strtolower($result['description']);
        
        // Exact title match gets highest score
        if ($titleLower === $queryLower) {
            $score += 100;
        }
        
        // Title starts with query
        if (strpos($titleLower, $queryLower) === 0) {
            $score += 50;
        }
        
        // Title contains query
        if (strpos($titleLower, $queryLower) !== false) {
            $score += 25;
        }
        
        // Description contains query
        if (strpos($descriptionLower, $queryLower) !== false) {
            $score += 10;
        }
        
        // Boost score for certain types
        if ($result['type'] === 'portfolio') {
            $score += 5;
        }
        
        return $score;
    }
    
    /**
     * Truncate text to specified length
     */
    private function truncateText(string $text, int $length): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . '...';
    }
    
    /**
     * Get human readable status text
     */
    private function getStatusText(string $status): string
    {
        $statuses = [
            'active' => 'Aktif',
            'inactive' => 'Pasif',
            'sold' => 'Satıldı',
            'rented' => 'Kiralandı',
            'archived' => 'Arşivlendi'
        ];
        
        return $statuses[$status] ?? 'Bilinmiyor';
    }
    
    /**
     * Get customer status text
     */
    private function getCustomerStatusText(string $status): string
    {
        $statuses = [
            'active' => 'Aktif',
            'inactive' => 'Pasif',
            'potential' => 'Potansiyel',
            'converted' => 'Dönüştürülmüş'
        ];
        
        return $statuses[$status] ?? 'Bilinmiyor';
    }
}
