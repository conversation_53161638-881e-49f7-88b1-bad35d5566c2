<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PortfolioCategoryFeatureGroup extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_category_feature_groups';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the category this group belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(PortfolioCategory::class, 'category_id', 'id');
    }

    /**
     * Get the features in this group.
     */
    public function features(): HasMany
    {
        return $this->hasMany(PortfolioCategoryFeature::class, 'feature_group_id', 'id')
                    ->orderBy('sort_order');
    }

    /**
     * Get active features in this group.
     */
    public function activeFeatures(): HasMany
    {
        return $this->features()->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeForCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set sort order when creating
        static::creating(function ($group) {
            if (is_null($group->sort_order)) {
                $maxOrder = static::where('category_id', $group->category_id)->max('sort_order');
                $group->sort_order = ($maxOrder ?? 0) + 1;
            }
        });

        // Prevent deletion if group has features
        static::deleting(function ($group) {
            if ($group->features()->exists()) {
                throw new \Exception('Bu gruba ait özellikler bulunduğu için silinemez.');
            }
        });
    }
}
