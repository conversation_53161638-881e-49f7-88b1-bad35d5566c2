<!-- <PERSON><PERSON> Bilgileri Adımı -->
<div x-show="activeTab === 'location'"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-x-8"
     x-transition:enter-end="opacity-100 transform translate-x-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-x-0"
     x-transition:leave-end="opacity-0 transform -translate-x-8"
     class="space-y-8">

    <!-- <PERSON><PERSON><PERSON> -->
    <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full mb-4">
            <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
            </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Konum Bilgileri</h2>
        <p class="text-gray-600">Portföyünüzün konum detaylarını belirtin</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Şehir -->
        <div>
            <label for="city_id" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18m2.25-18v18M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.75m-3.75 3.75h.75m-3.75 3.75h.75m-3.75 3.75H21m-3.75-18v18m3.75-18v18" />
                </svg>
                Şehir <span class="text-red-500">*</span>
            </label>
            <select id="city_id"
                    name="city_id"
                    x-model="formData.city_id"
                    @change="fetchDistricts(); validateField('city_id')"
                    :class="errors.city_id ? 'border-red-500 bg-red-50' : 'border-gray-200'"
                    class="w-full px-4 py-3 bg-white/50 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200" required>
                <option value="">Şehir seçin</option>
                @foreach($cities as $city)
                    <option value="{{ $city->il_id }}" {{ old('city_id') == $city->il_id ? 'selected' : '' }}>
                        {{ $city->il_adi }}
                    </option>
                @endforeach
            </select>
            <div x-show="errors.city_id" class="mt-2 text-sm text-red-600 flex items-center gap-1" x-transition>
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                </svg>
                <span x-text="errors.city_id"></span>
            </div>
            @error('city_id')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- İlçe -->
        <div>
            <label for="district_id" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V9.75a.75.75 0 00-.75-.75h-4.5a.75.75 0 00-.75.75V21m-4.5 0H2.25A1.125 1.125 0 011.125 19.875v-2.25c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125zm6.75-16.5h.008v.008h-.008V4.5z" />
                </svg>
                İlçe
            </label>
            <div class="relative">
                <select id="district_id"
                        name="district_id"
                        x-model="formData.district_id"
                        @change="fetchNeighborhoods()"
                        :disabled="!formData.city_id || isLoading.districts || districtOptions.length === 0"
                        :class="{'bg-gray-100 cursor-not-allowed': !formData.city_id || isLoading.districts || districtOptions.length === 0}"
                        class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                    <option value="" x-text="districtPlaceholder()"></option>
                    <template x-for="district in districtOptions" :key="district.id">
                        <option :value="district.id" x-text="district.ilce_adi"></option>
                    </template>
                </select>
                <div x-show="isLoading.districts" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg class="animate-spin h-5 w-5 text-orange-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
            </div>
            <p class="mt-2 text-xs text-gray-500 flex items-center gap-1">
                <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                </svg>
                Şehir seçtikten sonra ilçeler yüklenecektir
            </p>
        </div>

        <!-- Mahalle -->
        <div>
            <label for="neighborhood_id" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                </svg>
                Mahalle
                <span class="text-xs text-gray-500">(Opsiyonel)</span>
            </label>
            <div class="relative">
                <select id="neighborhood_id"
                        name="neighborhood_id"
                        x-model="formData.neighborhood_id"
                        :disabled="!formData.district_id || isLoading.neighborhoods || neighborhoodOptions.length === 0"
                        :class="{'bg-gray-100 cursor-not-allowed': !formData.district_id || isLoading.neighborhoods || neighborhoodOptions.length === 0}"
                        class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                    <option value="" x-text="neighborhoodPlaceholder()"></option>
                    <template x-for="neighborhood in neighborhoodOptions" :key="neighborhood.id">
                        <option :value="neighborhood.id" x-text="neighborhood.mahalle_adi"></option>
                    </template>
                </select>
                <div x-show="isLoading.neighborhoods" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg class="animate-spin h-5 w-5 text-orange-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
            </div>
            <p class="mt-2 text-xs text-gray-500 flex items-center gap-1">
                <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                </svg>
                İlçe seçtikten sonra mahalleler yüklenecektir
            </p>
        </div>

        <!-- Detay Adres -->
        <div class="lg:col-span-3">
            <label for="address" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                </svg>
                Detay Adres
                <span class="text-xs text-gray-500">(Opsiyonel)</span>
            </label>
            <div class="relative">
                <textarea id="address"
                          name="address"
                          rows="3"
                          class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 resize-none @error('address') border-red-500 @enderror"
                          placeholder="Sokak, cadde, apartman adı, daire no vb. detay adres bilgileri...">{{ old('address') }}</textarea>
                <div class="absolute bottom-3 right-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                    </svg>
                </div>
            </div>
            @error('address')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- Enlem -->
        <div>
            <label for="latitude" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9z" />
                </svg>
                Enlem (Latitude)
                <span class="text-xs text-gray-500">(Opsiyonel)</span>
            </label>
            <div class="relative">
                <input type="number"
                       id="latitude"
                       name="latitude"
                       x-model="formData.latitude"
                       value="{{ old('latitude') }}"
                       step="0.00000001"
                       min="-90"
                       max="90"
                       class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 @error('latitude') border-red-500 @enderror"
                       placeholder="Örn: 41.0082">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span class="text-gray-400 text-xs">°N</span>
                </div>
            </div>
            @error('latitude')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- Boylam -->
        <div>
            <label for="longitude" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9z" />
                </svg>
                Boylam (Longitude)
                <span class="text-xs text-gray-500">(Opsiyonel)</span>
            </label>
            <div class="relative">
                <input type="number"
                       id="longitude"
                       name="longitude"
                       x-model="formData.longitude"
                       value="{{ old('longitude') }}"
                       step="0.00000001"
                       min="-180"
                       max="180"
                       class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 @error('longitude') border-red-500 @enderror"
                       placeholder="Örn: 28.9784">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span class="text-gray-400 text-xs">°E</span>
                </div>
            </div>
            @error('longitude')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- Konum Butonları -->
        <div class="flex flex-col gap-3">
            <label class="text-sm font-semibold text-gray-700 mb-1">Konum İşlemleri</label>

            <!-- Mevcut Konumu Al -->
            <button type="button"
                    @click="getCurrentLocation()"
                    :disabled="isLoading.location"
                    class="flex items-center justify-center gap-2 px-4 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg x-show="!isLoading.location" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                </svg>
                <svg x-show="isLoading.location" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span x-text="isLoading.location ? 'Konum alınıyor...' : 'Mevcut Konumu Al'"></span>
            </button>

            <!-- Haritada Göster -->
            <button type="button"
                    @click="showOnMap()"
                    :disabled="!formData.latitude || !formData.longitude"
                    class="flex items-center justify-center gap-2 px-4 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0z" />
                </svg>
                Haritada Göster
            </button>
        </div>
    </div>

    <!-- Konum Özeti Kartı -->
    <div class="mt-8 p-6 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-2xl">
        <h3 class="text-lg font-semibold text-orange-800 mb-4 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
            </svg>
            Konum Özeti
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-white rounded-xl border border-orange-100">
                <p class="text-sm text-gray-600 mb-1">Şehir</p>
                <p class="text-lg font-semibold text-gray-800" x-text="getSelectedCityName() || 'Seçilmedi'"></p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-orange-100">
                <p class="text-sm text-gray-600 mb-1">İlçe</p>
                <p class="text-lg font-semibold text-gray-800" x-text="getSelectedDistrictName() || 'Seçilmedi'"></p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-orange-100">
                <p class="text-sm text-gray-600 mb-1">Mahalle</p>
                <p class="text-lg font-semibold text-gray-800" x-text="getSelectedNeighborhoodName() || 'Seçilmedi'"></p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-orange-100">
                <p class="text-sm text-gray-600 mb-1">Koordinat</p>
                <p class="text-sm font-semibold text-gray-800" x-text="formData.latitude && formData.longitude ? formData.latitude + ', ' + formData.longitude : 'Belirtilmedi'"></p>
            </div>
        </div>
    </div>
</div>
