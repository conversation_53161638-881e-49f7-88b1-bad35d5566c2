<x-guest-layout>


    <form method="POST" action="{{ route('password.store') }}" class="space-y-6">
        @csrf

        <!-- Password Reset Token -->
        <input type="hidden" name="token" value="{{ $request->route('token') }}">

        <!-- Email Address -->
        <div class="space-y-2">
            <x-input-label for="email" value="E-posta Adresi" />
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
                    </svg>
                </div>
                <input id="email"
                    class="form-input pl-10"
                    type="email"
                    name="email"
                    value="{{ old('email', $request->email) }}"
                    required
                    autofocus
                    autocomplete="username"
                    readonly />
            </div>
            <x-input-error :messages="$errors->get('email')" />
        </div>

        <!-- Password -->
        <div class="space-y-2">
            <x-input-label for="password" value="Yeni Şifre" />
            <div class="relative" x-data="{ show: false }">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0V10.5m-.75 0h10.5m-10.5 0A2.25 2.25 0 006.75 12.75v6A2.25 2.25 0 009 21h6a2.25 2.25 0 002.25-2.25v-6A2.25 2.25 0 0016.5 10.5" />
                    </svg>
                </div>
                <input id="password"
                    class="form-input pl-10 pr-12"
                    :type="show ? 'text' : 'password'"
                    name="password"
                    required
                    autocomplete="new-password"
                    placeholder="••••••••" />
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center z-10 text-gray-500 hover:text-gray-700 transition-colors" x-on:click="show = !show" aria-label="Şifreyi göster/gizle">
                    <svg x-show="!show" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12c2.25-4.5 6.364-7.5 9.75-7.5s7.5 3 9.75 7.5c-2.25 4.5-6.364 7.5-9.75 7.5s-7.5-3-9.75-7.5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <svg x-show="show" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 3l18 18" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.5 12c2.25 4.5 6.364 7.5 9.75 7.5 1.711 0 3.348-.482 4.8-1.318" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.21 6.21C7.764 5.178 9.52 4.5 11.25 4.5c3.386 0 7.5 3 9.75 7.5a10.523 10.523 0 01-4.478 4.77" />
                    </svg>
                </button>
            </div>
            <x-input-error :messages="$errors->get('password')" />
        </div>

        <!-- Confirm Password -->
        <div class="space-y-2">
            <x-input-label for="password_confirmation" value="Şifre Tekrarı" />
            <div class="relative" x-data="{ show: false }">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0V10.5m-.75 0h10.5m-10.5 0A2.25 2.25 0 006.75 12.75v6A2.25 2.25 0 009 21h6a2.25 2.25 0 002.25-2.25v-6A2.25 2.25 0 0016.5 10.5" />
                    </svg>
                </div>
                <input id="password_confirmation"
                    class="form-input pl-10 pr-12"
                    :type="show ? 'text' : 'password'"
                    name="password_confirmation"
                    required
                    autocomplete="new-password"
                    placeholder="••••••••" />
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center z-10 text-gray-500 hover:text-gray-700 transition-colors" x-on:click="show = !show" aria-label="Şifreyi göster/gizle">
                    <svg x-show="!show" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12c2.25-4.5 6.364-7.5 9.75-7.5s7.5 3 9.75 7.5c-2.25 4.5-6.364 7.5-9.75 7.5s-7.5-3-9.75-7.5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <svg x-show="show" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 3l18 18" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.5 12c2.25 4.5 6.364 7.5 9.75 7.5 1.711 0 3.348-.482 4.8-1.318" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.21 6.21C7.764 5.178 9.52 4.5 11.25 4.5c3.386 0 7.5 3 9.75 7.5a10.523 10.523 0 01-4.478 4.77" />
                    </svg>
                </button>
            </div>
            <x-input-error :messages="$errors->get('password_confirmation')" />
        </div>

        <div>
            <button type="submit" class="btn btn-primary w-full text-base gap-3" x-data="{ loading: false }"
                    x-on:click="loading = true; setTimeout(() => loading = false, 3000)"
                    :disabled="loading">
                <svg x-show="!loading" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <svg x-show="loading" class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span x-text="loading ? 'Sıfırlanıyor...' : 'Şifreyi Sıfırla'"></span>
            </button>
        </div>
    </form>
</x-guest-layout>
