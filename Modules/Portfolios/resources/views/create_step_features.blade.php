<!-- <PERSON><PERSON>ı -->
<div x-show="activeTab === 'features'"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-x-8"
     x-transition:enter-end="opacity-100 transform translate-x-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-x-0"
     x-transition:leave-end="opacity-0 transform -translate-x-8"
     class="space-y-8">

    <!-- <PERSON><PERSON><PERSON> -->
    <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full mb-4">
            <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">İlan Özellikleri</h2>
        <p class="text-gray-600">Portföyünüzün özelliklerini ve detaylarını belirtin</p>
    </div>

    <!-- Özellik Kategorileri -->
    <div class="space-y-8">
        <!-- Temel Özellikler -->
        <div class="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-purple-800 mb-6 flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                </svg>
                Temel Özellikler
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Oda Sayısı -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Oda Sayısı</label>
                    <select name="details[room_count]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="1+0">1+0</option>
                        <option value="1+1">1+1</option>
                        <option value="2+1">2+1</option>
                        <option value="3+1">3+1</option>
                        <option value="4+1">4+1</option>
                        <option value="5+1">5+1</option>
                        <option value="6+1">6+1</option>
                        <option value="7+1">7+1</option>
                        <option value="8+1">8+1</option>
                        <option value="9+1">9+1</option>
                        <option value="10+1">10+1</option>
                    </select>
                </div>

                <!-- Banyo Sayısı -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Banyo Sayısı</label>
                    <select name="details[bathroom_count]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5+</option>
                    </select>
                </div>

                <!-- Balkon Sayısı -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Balkon Sayısı</label>
                    <select name="details[balcony_count]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="0">Yok</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4+</option>
                    </select>
                </div>

                <!-- Yaş -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bina Yaşı</label>
                    <select name="details[building_age]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="0">Sıfır Bina</option>
                        <option value="1-5">1-5 Yaş</option>
                        <option value="6-10">6-10 Yaş</option>
                        <option value="11-15">11-15 Yaş</option>
                        <option value="16-20">16-20 Yaş</option>
                        <option value="21-25">21-25 Yaş</option>
                        <option value="26-30">26-30 Yaş</option>
                        <option value="31+">31+ Yaş</option>
                    </select>
                </div>

                <!-- Kat -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bulunduğu Kat</label>
                    <select name="details[floor]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="basement">Bodrum</option>
                        <option value="ground">Zemin</option>
                        <option value="mezzanine">Asma</option>
                        <option value="1">1. Kat</option>
                        <option value="2">2. Kat</option>
                        <option value="3">3. Kat</option>
                        <option value="4">4. Kat</option>
                        <option value="5">5. Kat</option>
                        <option value="6">6. Kat</option>
                        <option value="7">7. Kat</option>
                        <option value="8">8. Kat</option>
                        <option value="9">9. Kat</option>
                        <option value="10">10. Kat</option>
                        <option value="11+">11+ Kat</option>
                    </select>
                </div>

                <!-- Toplam Kat -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Toplam Kat Sayısı</label>
                    <select name="details[total_floors]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                        <option value="11+">11+</option>
                    </select>
                </div>

                <!-- Isıtma -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Isıtma Tipi</label>
                    <select name="details[heating_type]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="central">Merkezi</option>
                        <option value="individual">Bireysel</option>
                        <option value="combi">Kombi</option>
                        <option value="stove">Soba</option>
                        <option value="air_conditioning">Klima</option>
                        <option value="floor_heating">Yerden Isıtma</option>
                        <option value="none">Yok</option>
                    </select>
                </div>

                <!-- Cephe -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Cephe</label>
                    <select name="details[facade]" class="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Seçin</option>
                        <option value="north">Kuzey</option>
                        <option value="south">Güney</option>
                        <option value="east">Doğu</option>
                        <option value="west">Batı</option>
                        <option value="northeast">Kuzey-Doğu</option>
                        <option value="northwest">Kuzey-Batı</option>
                        <option value="southeast">Güney-Doğu</option>
                        <option value="southwest">Güney-Batı</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- İç Özellikler -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-blue-800 mb-6 flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V9.75a.75.75 0 00-.75-.75h-4.5a.75.75 0 00-.75.75V21m-4.5 0H2.25A1.125 1.125 0 011.125 19.875v-2.25c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125zm6.75-16.5h.008v.008h-.008V4.5z" />
                </svg>
                İç Özellikler
            </h3>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- İç Özellikler Checkbox'ları -->
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_furnished" name="details[features][]" value="furnished" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_furnished" class="text-sm text-gray-700">Eşyalı</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_kitchen_appliances" name="details[features][]" value="kitchen_appliances" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_kitchen_appliances" class="text-sm text-gray-700">Beyaz Eşya</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_air_conditioning" name="details[features][]" value="air_conditioning" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_air_conditioning" class="text-sm text-gray-700">Klima</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_fireplace" name="details[features][]" value="fireplace" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_fireplace" class="text-sm text-gray-700">Şömine</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_parquet" name="details[features][]" value="parquet" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_parquet" class="text-sm text-gray-700">Parke</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_laminate" name="details[features][]" value="laminate" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_laminate" class="text-sm text-gray-700">Laminat</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_ceramic" name="details[features][]" value="ceramic" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_ceramic" class="text-sm text-gray-700">Seramik</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_marble" name="details[features][]" value="marble" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                    <label for="feature_marble" class="text-sm text-gray-700">Mermer</label>
                </div>
            </div>
        </div>

        <!-- Dış Özellikler -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6">
            <h3 class="text-lg font-semibold text-green-800 mb-6 flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21" />
                </svg>
                Dış Özellikler & Sosyal Alanlar
            </h3>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <!-- Dış Özellikler Checkbox'ları -->
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_elevator" name="details[features][]" value="elevator" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_elevator" class="text-sm text-gray-700">Asansör</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_parking" name="details[features][]" value="parking" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_parking" class="text-sm text-gray-700">Otopark</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_balcony" name="details[features][]" value="balcony" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_balcony" class="text-sm text-gray-700">Balkon</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_terrace" name="details[features][]" value="terrace" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_terrace" class="text-sm text-gray-700">Teras</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_garden" name="details[features][]" value="garden" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_garden" class="text-sm text-gray-700">Bahçe</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_pool" name="details[features][]" value="pool" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_pool" class="text-sm text-gray-700">Havuz</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_gym" name="details[features][]" value="gym" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_gym" class="text-sm text-gray-700">Spor Salonu</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_sauna" name="details[features][]" value="sauna" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_sauna" class="text-sm text-gray-700">Sauna</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_security" name="details[features][]" value="security" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_security" class="text-sm text-gray-700">Güvenlik</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_concierge" name="details[features][]" value="concierge" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_concierge" class="text-sm text-gray-700">Kapıcı</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_generator" name="details[features][]" value="generator" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_generator" class="text-sm text-gray-700">Jeneratör</label>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input type="checkbox" id="feature_playground" name="details[features][]" value="playground" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500">
                    <label for="feature_playground" class="text-sm text-gray-700">Çocuk Parkı</label>
                </div>
            </div>
        </div>

        <!-- Özellik Özeti -->
        <div class="mt-8 p-6 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-2xl">
            <h3 class="text-lg font-semibold text-purple-800 mb-4 flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3-6h.008v.008H15V12zm0 3h.008v.008H15V15zm0 3h.008v.008H15V18zM6.75 6.75h.008v.008H6.75V6.75zm0 3h.008v.008H6.75V9.75zm0 3h.008v.008H6.75V12.75z" />
                </svg>
                Seçilen Özellikler
            </h3>
            <div id="selected-features" class="flex flex-wrap gap-2">
                <!-- Seçilen özellikler JavaScript ile buraya eklenecek -->
            </div>
            <p class="text-sm text-purple-600 mt-2" id="no-features-message">Henüz özellik seçilmedi</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('input[name="details[features][]"]');
    const selectedFeaturesContainer = document.getElementById('selected-features');
    const noFeaturesMessage = document.getElementById('no-features-message');
    
    function updateSelectedFeatures() {
        const selectedFeatures = Array.from(checkboxes).filter(cb => cb.checked);
        selectedFeaturesContainer.innerHTML = '';
        
        if (selectedFeatures.length === 0) {
            noFeaturesMessage.style.display = 'block';
        } else {
            noFeaturesMessage.style.display = 'none';
            selectedFeatures.forEach(checkbox => {
                const label = document.querySelector(`label[for="${checkbox.id}"]`);
                const badge = document.createElement('span');
                badge.className = 'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800';
                badge.textContent = label.textContent;
                selectedFeaturesContainer.appendChild(badge);
            });
        }
    }
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedFeatures);
    });
    
    // İlk yüklemede çalıştır
    updateSelectedFeatures();
});
</script>
