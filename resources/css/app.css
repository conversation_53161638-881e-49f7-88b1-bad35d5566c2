@import "tailwindcss";

/* Custom CSS Variables */
:root {
    --color-primary: #3b82f6;
    --color-primary-hover: #2563eb;
    --color-secondary: #64748b;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
}

/* Custom Components */
.search-enhanced {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    transition: all 0.2s;
}
.search-enhanced:focus {
    background-color: #ffffff;
    border-color: var(--color-primary);
}

/* Modern Card Styles */
.card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
    @apply p-6;
}

/* Modern Form Styles */
.form-input {
    @apply block w-full py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400
         focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500
         transition-all duration-200 bg-white
         hover:border-gray-400 hover:shadow-md;
         padding-inline: calc(var(--spacing)*4);
         padding-block: calc(var(--spacing)*3);
}

.form-select {
    @apply block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm
         focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500
         transition-all duration-200 bg-white
         hover:border-gray-400 hover:shadow-md;
         padding-inline: calc(var(--spacing)*4);
         padding-block: calc(var(--spacing)*3);
}

.form-textarea {
    @apply block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400
         focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500
         transition-all duration-200 resize-none bg-white
         hover:border-gray-400 hover:shadow-md;
}

.form-checkbox {
    @apply h-5 w-5 text-blue-600 focus:ring-blue-500/20 focus:ring-2 border-2 border-gray-300
         rounded-md transition-all duration-200 cursor-pointer
         hover:border-blue-400 hover:shadow-sm;
}

/* Modern Button Styles */
.btn {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent
         text-sm font-semibold rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2
         transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5
         disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
}

.btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white
         hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500/20
         shadow-blue-500/25 hover:shadow-blue-500/40;
}

.btn-secondary {
    @apply bg-gradient-to-r from-gray-600 to-gray-700 text-white
         hover:from-gray-700 hover:to-gray-800 focus:ring-gray-500/20
         shadow-gray-500/25 hover:shadow-gray-500/40;
}

.btn-success {
    @apply bg-gradient-to-r from-green-600 to-green-700 text-white
         hover:from-green-700 hover:to-green-800 focus:ring-green-500/20
         shadow-green-500/25 hover:shadow-green-500/40;
}

.btn-danger {
    @apply bg-gradient-to-r from-red-600 to-red-700 text-white
         hover:from-red-700 hover:to-red-800 focus:ring-red-500/20
         shadow-red-500/25 hover:shadow-red-500/40;
}

/* Modern Alert Styles */
.alert {
    @apply px-4 py-3 rounded-xl border shadow-sm bg-white;
}

.alert-success {
    @apply bg-green-50/80 border-green-200 text-green-800 shadow-green-500/10;
}

.alert-error {
    @apply bg-red-50/80 border-red-200 text-red-800 shadow-red-500/10;
}

.alert-warning {
    @apply bg-yellow-50/80 border-yellow-200 text-yellow-800 shadow-yellow-500/10;
}

.alert-info {
    @apply bg-blue-50/80 border-blue-200 text-blue-800 shadow-blue-500/10;
}

/* Tab Styles */
.tab-button {
    @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
}

.tab-button.active {
    @apply border-blue-500 text-blue-600 font-semibold;
}

.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modern Divider */
.divider-modern {
    @apply relative flex items-center my-6;
}

.divider-modern::before {
    content: "";
    @apply flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent;
}

.divider-modern span {
    @apply px-4 text-sm text-gray-500 bg-white;
}

/* Glassmorphism Effect */
.glass {
    @apply bg-white/20 border border-white/30 shadow-lg;
}

/* Modern Focus Ring */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500;
}

/* Loading Animation */
@keyframes pulse-modern {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.02);
    }
}

.pulse-modern {
    animation: pulse-modern 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Form Validation Styles */
.form-input.error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500/20 bg-red-50/50;
}

.form-input.success {
    @apply border-green-500 focus:border-green-500 focus:ring-green-500/20 bg-green-50/50;
}

.error-message {
    @apply text-red-600 text-sm mt-1 flex items-center gap-1;
    animation: slideInFromTop 0.2s ease-out;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-message svg {
    @apply w-4 h-4 flex-shrink-0;
}

/* Input Focus Animation */
@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-4px);
    }
    75% {
        transform: translateX(4px);
    }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* Real-time Validation */
.form-group {
    @apply relative;
}

.validation-icon {
    @apply absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 transition-all duration-200 pointer-events-none;
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 50;
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
}

.dropdown-content:not(.hidden) {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Progress Bar Animation */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width);
    }
}

.progress-animated {
    animation: progressFill 2s ease-out forwards;
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading Spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Dashboard Stats Animation */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card {
    animation: countUp 0.6s ease-out forwards;
}

.stat-card:nth-child(1) {
    animation-delay: 0.1s;
}
.stat-card:nth-child(2) {
    animation-delay: 0.2s;
}
.stat-card:nth-child(3) {
    animation-delay: 0.3s;
}
.stat-card:nth-child(4) {
    animation-delay: 0.4s;
}
.stat-card:nth-child(5) {
    animation-delay: 0.5s;
}
.stat-card:nth-child(6) {
    animation-delay: 0.6s;
}

/* Activity Timeline Animation */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.activity-item {
    animation: slideInRight 0.5s ease-out forwards;
}

.activity-item:nth-child(1) {
    animation-delay: 0.1s;
}
.activity-item:nth-child(2) {
    animation-delay: 0.2s;
}
.activity-item:nth-child(3) {
    animation-delay: 0.3s;
}
.activity-item:nth-child(4) {
    animation-delay: 0.4s;
}
/* Settings Page Tabs */
.tab-button {
    @apply bg-white/50 text-gray-600 hover:bg-white/70 hover:text-gray-900;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-button.active {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg;
    transform: translateY(-2px);
}

.tab-content {
    display: none;
    animation: fadeInUp 0.4s ease-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Toggle Switch Styles */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #3b82f6;
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Settings Card Animations */
.settings-card {
    transition: all 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Form Input Focus States */
.form-input:focus {
    @apply ring-2 ring-blue-500/20 border-blue-500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea:focus {
    @apply ring-2 ring-blue-500/20 border-blue-500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:focus {
    @apply ring-2 ring-blue-500/20 border-blue-500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Settings Page Specific Animations */
.settings-section {
    animation: slideInFromBottom 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.settings-section:nth-child(1) {
    animation-delay: 0.1s;
}
.settings-section:nth-child(2) {
    animation-delay: 0.2s;
}
.settings-section:nth-child(3) {
    animation-delay: 0.3s;
}
.settings-section:nth-child(4) {
    animation-delay: 0.4s;
}

@keyframes slideInFromBottom {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
/* Cache buster: Tue Aug 26 16:25:09 +03 2025 */
