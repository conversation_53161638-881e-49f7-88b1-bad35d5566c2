@props([
    'title' => '<PERSON><PERSON>lığı',
    'description' => null,
    'breadcrumbs' => [],
    'actions' => null,
    'tabs' => null,
    'activeTab' => null,
    'showStats' => false,
    'stats' => []
])

<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gray-50 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Breadcrumb -->
            @if(!empty($breadcrumbs))
            <nav class="flex mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    @foreach($breadcrumbs as $breadcrumb)
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            @if(isset($breadcrumb['url']))
                                <a href="{{ $breadcrumb['url'] }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                                    {{ $breadcrumb['title'] }}
                                </a>
                            @else
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                                    {{ $breadcrumb['title'] }}
                                </span>
                            @endif
                        </div>
                    </li>
                    @endforeach
                </ol>
            </nav>
            @endif

            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
                    <div class="min-w-0 flex-1">
                        <h1 class="text-2xl font-bold text-gray-900 sm:text-3xl">
                            {{ $title }}
                        </h1>
                        @if($description)
                        <p class="mt-2 text-gray-600">
                            {{ $description }}
                        </p>
                        @endif
                    </div>

                    @if($actions)
                    <div class="flex flex-col sm:flex-row gap-3">
                        {{ $actions }}
                    </div>
                    @endif
                </div>
            </div>

            <!-- Stats Cards (Optional) -->
            @if($showStats && !empty($stats))
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                @foreach($stats as $stat)
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        @if(isset($stat['icon']))
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center w-10 h-10 {{ $stat['icon_bg'] ?? 'bg-blue-100' }} {{ $stat['icon_color'] ?? 'text-blue-600' }} rounded-lg">
                                {!! $stat['icon'] !!}
                            </div>
                        </div>
                        @endif
                        <div class="ml-4 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    {{ $stat['label'] }}
                                </dt>
                                <dd class="text-2xl font-bold text-gray-900">
                                    {{ $stat['value'] }}
                                </dd>
                                @if(isset($stat['change']))
                                <dd class="flex items-center text-sm {{ $stat['change_positive'] ? 'text-green-600' : 'text-red-600' }}">
                                    @if($stat['change_positive'])
                                    <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 20 20" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
                                    </svg>
                                    @else
                                    <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 20 20" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V4"/>
                                    </svg>
                                    @endif
                                    {{ $stat['change'] }}
                                </dd>
                                @endif
                            </dl>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @endif

            <!-- Tab Navigation (Optional) -->
            @if($tabs)
            <div class="mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        @foreach($tabs as $tabKey => $tab)
                        <button type="button"
                                onclick="showTab('{{ $tabKey }}')"
                                id="tab-{{ $tabKey }}"
                                class="tab-button {{ $activeTab === $tabKey ? 'active' : '' }} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">
                            <div class="flex items-center">
                                @if(isset($tab['icon']))
                                {!! $tab['icon'] !!}
                                @endif
                                {{ $tab['title'] }}
                            </div>
                        </button>
                        @endforeach
                    </nav>
                </div>
            </div>
            @endif

            <!-- Main Content -->
            <div class="space-y-6">
                {{ $slot }}
            </div>
        </div>
    </div>

    @if($tabs)
    @push('scripts')
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const targetContent = document.getElementById('content-' + tabName);
            if (targetContent) {
                targetContent.classList.remove('hidden');
            }

            // Add active class to selected tab button
            const targetButton = document.getElementById('tab-' + tabName);
            if (targetButton) {
                targetButton.classList.add('active');
            }
        }
    </script>
    @endpush

    @push('styles')
    <style>
        .tab-button {
            @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }

        .tab-button.active {
            @apply border-blue-500 text-blue-600;
        }

        .tab-content {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    @endpush
    @endif
    @endsection
</x-backend-layout>
