<?php

namespace Modules\Company\App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\City;
use App\Models\District;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\View\View;

class CompanyController extends Controller
{
    /**
     * Şirket listesi (admin için).
     */
    public function index(): View
    {
        // Sadece manage_company iznine sahip kullanıcılar görebilir
        $user = Auth::user();
        if (!$user->hasCompanyPermission($user->company_id, 'manage_company')) {
            abort(403, 'Bu sayfaya erişim yetkiniz yok.');
        }

        return view('company::index');
    }

    /**
     * Şirket oluşturma sayfası.
     */
    public function create(): View
    {
        return view('company::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request) {}

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('company::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('company::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id) {}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id) {}

    /**
     * Şirket ayarları sayfası.
     */
    public function settings(): View
    {
        $user = Auth::user();

        // Kullanıcının şirket ayarlarını yönetme yetkisi var mı?
        if (!$user->hasCompanyPermission($user->company_id, 'manage_company')) {
            abort(403, 'Şirket ayarlarını yönetme yetkiniz yok.');
        }

        $company = $user->company;
        if (!$company) {
            abort(404, 'Şirket bulunamadı.');
        }

        // İl ve ilçe listelerini getir
        $cities = City::orderBy('il_adi')->get();
        $districts = collect();

        if ($company->city_id) {
            $districts = District::where('il_id', $company->city_id)
                ->orderBy('ilce_adi')
                ->get();
        }

        return view('company::settings', compact('company', 'cities', 'districts'));
    }

    /**
     * Şirket temel bilgilerini güncelle.
     */
    public function updateBasicInfo(Request $request): RedirectResponse
    {
        $user = Auth::user();

        // Yetki kontrolü
        if (!$user->hasCompanyPermission($user->company_id, 'manage_company')) {
            abort(403, 'Şirket bilgilerini güncelleme yetkiniz yok.');
        }

        $company = $user->company;
        if (!$company) {
            abort(404, 'Şirket bulunamadı.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'website' => 'nullable|url|max:255',
            'phone' => 'nullable|string|max:20',
            'whatsapp' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'city_id' => 'nullable|exists:iller,il_id',
            'district_id' => 'nullable|exists:ilceler,ilce_id',
            'location_url' => 'nullable|url|max:500',
        ]);

        // Slug güncelle (eğer isim değiştiyse)
        $slug = Str::slug($request->name);
        if ($company->name !== $request->name) {
            // Benzersiz slug oluştur
            $originalSlug = $slug;
            $counter = 1;
            while (Company::where('slug', $slug)->where('id', '!=', $company->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
        } else {
            $slug = $company->slug; // Mevcut slug'ı koru
        }

        $company->update([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'website' => $request->website,
            'phone' => $request->phone,
            'whatsapp' => $request->whatsapp,
            'email' => $request->email,
            'address' => $request->address,
            'city_id' => $request->city_id,
            'district_id' => $request->district_id,
            'location_url' => $request->location_url,
        ]);

        return redirect()->route('company.settings')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Şirket bilgileri başarıyla güncellendi.'
            ]);
    }

    /**
     * Şirket sosyal medya hesaplarını güncelle.
     */
    public function updateSocialMedia(Request $request): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_company')) {
            abort(403, 'Sosyal medya hesaplarını güncelleme yetkiniz yok.');
        }

        $company = $user->company;
        if (!$company) {
            abort(404, 'Şirket bulunamadı.');
        }

        $request->validate([
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            'linkedin' => 'nullable|url|max:255',
            'youtube' => 'nullable|url|max:255',
            'tiktok' => 'nullable|url|max:255',
            'pinterest' => 'nullable|url|max:255',
        ]);

        $company->update([
            'facebook' => $request->facebook,
            'instagram' => $request->instagram,
            'twitter' => $request->twitter,
            'linkedin' => $request->linkedin,
            'youtube' => $request->youtube,
            'tiktok' => $request->tiktok,
            'pinterest' => $request->pinterest,
        ]);

        return redirect()->route('company.settings')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Sosyal medya hesapları başarıyla güncellendi.'
            ]);
    }

    /**
     * Şirket logosu yükle.
     */
    public function uploadLogo(Request $request): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_company')) {
            abort(403, 'Logo yükleme yetkiniz yok.');
        }

        $company = $user->company;
        if (!$company) {
            abort(404, 'Şirket bulunamadı.');
        }

        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        // Eski logoyu sil
        if ($company->logo_url) {
            $oldLogoPath = str_replace('/storage/', '', $company->logo_url);
            Storage::disk('public')->delete($oldLogoPath);
        }

        // Yeni logoyu kaydet
        $logoPath = $request->file('logo')->store('company-logos', 'public');
        $logoUrl = '/storage/' . $logoPath;

        $company->update([
            'logo_url' => $logoUrl,
        ]);

        return redirect()->route('company.settings')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Logo başarıyla yüklendi.'
            ]);
    }

    /**
     * Şirket ayarlarını güncelle.
     */
    public function updateSettings(Request $request): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_settings')) {
            abort(403, 'Şirket ayarlarını güncelleme yetkiniz yok.');
        }

        $company = $user->company;
        if (!$company) {
            abort(404, 'Şirket bulunamadı.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'whatsapp' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'tax_number' => 'nullable|string|max:20',
            'tax_office' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'settings.auto_backup' => 'boolean',
            'settings.email_notifications' => 'boolean',
            'settings.maintenance_mode' => 'boolean',
            'settings.public_registration' => 'boolean',
            'settings.default_language' => 'required|in:tr,en',
            'settings.timezone' => 'required|string',
            'settings.two_factor_auth' => 'boolean',
            'settings.session_timeout' => 'required|integer|min:30|max:480',
            'settings.strong_password_required' => 'boolean',
            'settings.max_login_attempts' => 'required|integer|min:3|max:10',
            'settings.api_enabled' => 'boolean',
            'settings.webhook_url' => 'nullable|url|max:500',
            'settings.google_maps_api_key' => 'nullable|string|max:255',
            'settings.smtp_host' => 'nullable|string|max:255',
            'settings.smtp_port' => 'nullable|integer|min:1|max:65535',
            'settings.smtp_username' => 'nullable|email|max:255',
            'settings.smtp_password' => 'nullable|string|max:255',
            'settings.ip_whitelist' => 'nullable|string',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($company->logo_url) {
                $oldLogoPath = str_replace('/storage/', '', $company->logo_url);
                Storage::disk('public')->delete($oldLogoPath);
            }

            // Store new logo
            $logoPath = $request->file('logo')->store('company-logos', 'public');
            $company->logo_url = '/storage/' . $logoPath;
        }

        // Update basic company info
        $company->update([
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'whatsapp' => $request->whatsapp,
            'address' => $request->address,
            'tax_number' => $request->tax_number,
            'tax_office' => $request->tax_office,
            'website' => $request->website,
            'logo_url' => $company->logo_url,
        ]);

        // Update settings
        $settings = $company->settings ?? [];
        $requestSettings = $request->input('settings', []);

        $settings = array_merge($settings, [
            'auto_backup' => isset($requestSettings['auto_backup']) ? (bool)$requestSettings['auto_backup'] : false,
            'email_notifications' => isset($requestSettings['email_notifications']) ? (bool)$requestSettings['email_notifications'] : true,
            'maintenance_mode' => isset($requestSettings['maintenance_mode']) ? (bool)$requestSettings['maintenance_mode'] : false,
            'public_registration' => isset($requestSettings['public_registration']) ? (bool)$requestSettings['public_registration'] : true,
            'default_language' => $requestSettings['default_language'] ?? 'tr',
            'timezone' => $requestSettings['timezone'] ?? 'Europe/Istanbul',
            'two_factor_auth' => isset($requestSettings['two_factor_auth']) ? (bool)$requestSettings['two_factor_auth'] : false,
            'session_timeout' => (int)($requestSettings['session_timeout'] ?? 120),
            'strong_password_required' => isset($requestSettings['strong_password_required']) ? (bool)$requestSettings['strong_password_required'] : true,
            'max_login_attempts' => (int)($requestSettings['max_login_attempts'] ?? 5),
            'api_enabled' => isset($requestSettings['api_enabled']) ? (bool)$requestSettings['api_enabled'] : false,
            'webhook_url' => $requestSettings['webhook_url'] ?? '',
            'google_maps_api_key' => $requestSettings['google_maps_api_key'] ?? '',
            'smtp_host' => $requestSettings['smtp_host'] ?? '',
            'smtp_port' => (int)($requestSettings['smtp_port'] ?? 587),
            'smtp_username' => $requestSettings['smtp_username'] ?? '',
            'smtp_password' => $requestSettings['smtp_password'] ?? '',
            'ip_whitelist' => $requestSettings['ip_whitelist'] ?? '',
        ]);

        $company->update(['settings' => $settings]);

        return redirect()->route('company.settings')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Şirket ayarları başarıyla güncellendi.'
            ]);
    }

    /**
     * İlçeleri AJAX ile getir.
     */
    public function getDistricts(Request $request)
    {
        $cityId = $request->get('city_id');

        if (!$cityId) {
            return response()->json([]);
        }

        $districts = District::where('il_id', $cityId)
            ->orderBy('ilce_adi')
            ->get(['ilce_id as id', 'ilce_adi as name']);

        return response()->json($districts);
    }
}
