<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <!-- Header Section -->
            <div class="mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div>
                        <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                            Müşteri Yönetimi
                        </h1>
                        <p class="text-gray-600 text-lg mt-2">Müşterilerinizi görüntüleyin ve yönetin</p>
                    </div>
                    <!-- Action Buttons -->
                    <div class="flex items-center gap-3">
                        <!-- Quick Filters Dropdown -->
                        <div class="relative">
                            <button id="quick-filter-btn"
                                    class="flex items-center gap-2 px-4 py-3 bg-white border border-gray-200 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 cursor-pointer shadow-sm">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
                                </svg>
                                Hızlı Filtre
                                <svg id="filter-arrow" class="h-4 w-4 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                                </svg>
                            </button>

                            <div id="quick-filter-dropdown"
                                 class="absolute right-0 mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-gray-200 py-3 z-50 hidden opacity-0 scale-95 transition-all duration-200">

                                <div class="px-4 py-2 border-b border-gray-100">
                                    <h3 class="text-sm font-semibold text-gray-900">Hızlı Filtreler</h3>
                                </div>

                                <div class="py-2">
                                    <!-- Minimal Quick Filters -->
                                    <div class="px-3 py-2">
                                        <div class="grid grid-cols-1 gap-1">
                                            <a href="{{ route('customers.index', ['status' => 'active']) }}"
                                               class="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 rounded-lg transition-colors duration-200 cursor-pointer">
                                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                                Aktif Müşteriler
                                            </a>
                                            <a href="{{ route('customers.index', ['status' => 'potential']) }}"
                                               class="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-yellow-50 hover:text-yellow-700 rounded-lg transition-colors duration-200 cursor-pointer">
                                                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                                Potansiyel
                                            </a>
                                            <a href="{{ route('customers.index', ['status' => 'inactive']) }}"
                                               class="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-700 rounded-lg transition-colors duration-200 cursor-pointer">
                                                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                                Pasif Müşteriler
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Customer Type Dropdown -->
                        <div class="relative">
                            <button id="customer-type-btn"
                                    class="flex items-center gap-2 px-4 py-3 @if(request('type')) bg-purple-100 border-purple-300 text-purple-700 @else bg-white border-gray-200 text-gray-700 @endif border rounded-xl hover:bg-gray-50 transition-all duration-200 cursor-pointer shadow-sm">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                                                </svg>
                                @php
                                    $selectedTypes = request('type') ? (is_array(request('type')) ? request('type') : [request('type')]) : [];
                                    $selectedTypeNames = $customerTypes->whereIn('id', $selectedTypes)->pluck('name')->toArray();
                                @endphp
                                @if(count($selectedTypeNames) > 0)
                                    @if(count($selectedTypeNames) == 1)
                                        {{ $selectedTypeNames[0] }}
                                    @else
                                        {{ count($selectedTypeNames) }} Tip Seçili
                                    @endif
                                @else
                                    Müşteri Tipi
                                @endif
                                <svg id="type-arrow" class="h-4 w-4 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                                </svg>
                            </button>

                            <div id="customer-type-dropdown"
                                 class="absolute right-0 mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-gray-200 py-3 z-50 hidden opacity-0 scale-95 transition-all duration-200">

                                <div class="px-4 py-2 border-b border-gray-100">
                                    <h3 class="text-sm font-semibold text-gray-900">Müşteri Tipi Seç</h3>
                                </div>

                                <div class="py-2">
                                    <div class="px-4 py-2">
                                        <div class="space-y-1">
                                            <button onclick="clearCustomerTypes()"
                                               class="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-colors duration-200 cursor-pointer w-full text-left">
                                                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                Tümünü Temizle
                                            </button>
                                            @foreach($customerTypes as $type)
                                            <label class="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 rounded-lg transition-colors duration-200 cursor-pointer">
                                                <input type="checkbox"
                                                       class="customer-type-checkbox rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                                                       value="{{ $type->id }}"
                                                       @if(in_array($type->id, $selectedTypes)) checked @endif>
                                                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                                {{ $type->name }}
                                            </label>
                                            @endforeach
                                        </div>

                                        <!-- Apply Button -->
                                        <div class="px-3 py-2 border-t border-gray-100 mt-2">
                                            <button onclick="applyCustomerTypeFilter()"
                                                    class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 text-sm font-medium">
                                                Filtreyi Uygula
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Agent Dropdown -->
                        <div class="relative">
                            <button id="agent-btn"
                                    class="flex items-center gap-2 px-4 py-3 @if(request('agent')) bg-blue-100 border-blue-300 text-blue-700 @else bg-white border-gray-200 text-gray-700 @endif border rounded-xl hover:bg-gray-50 transition-all duration-200 cursor-pointer shadow-sm">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                                </svg>
                                @if(request('agent'))
                                    {{ $agents->where('id', request('agent'))->first()->name ?? 'Danışman' }}
                                @else
                                    Danışman
                                @endif
                                <svg id="agent-arrow" class="h-4 w-4 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                                </svg>
                            </button>

                            <div id="agent-dropdown"
                                 class="absolute right-0 mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-gray-200 py-3 z-50 hidden opacity-0 scale-95 transition-all duration-200">

                                <div class="px-4 py-2 border-b border-gray-100">
                                    <h3 class="text-sm font-semibold text-gray-900">Danışman Seç</h3>
                                </div>

                                <div class="py-2">
                                    <div class="px-4 py-2">
                                        <div class="space-y-1">
                                            <a href="{{ route('customers.index') }}"
                                               class="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-colors duration-200 cursor-pointer">
                                                <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                Tümü
                                            </a>
                                            @foreach($agents as $agent)
                                            <a href="{{ route('customers.index', ['agent' => $agent->id]) }}"
                                               class="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-colors duration-200 cursor-pointer">
                                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                {{ $agent->name }}
                                            </a>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Date Range Picker -->
                        <div class="relative">
                            <button id="daterange-btn"
                                    class="flex items-center gap-2 px-4 py-3 @if(request('date_from') || request('date_to')) bg-green-100 border-green-300 text-green-700 @else bg-white border-gray-200 text-gray-700 @endif border rounded-xl hover:bg-gray-50 transition-all duration-200 cursor-pointer shadow-sm">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5a2.25 2.25 0 0 0 2.25-2.25m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5a2.25 2.25 0 0 1 2.25 2.25v7.5" />
                                                </svg>
                                @if(request('date_from') || request('date_to'))
                                    @if(request('date_from') && request('date_to'))
                                        {{ \Carbon\Carbon::parse(request('date_from'))->format('d.m.Y') }} - {{ \Carbon\Carbon::parse(request('date_to'))->format('d.m.Y') }}
                                    @elseif(request('date_from'))
                                        {{ \Carbon\Carbon::parse(request('date_from'))->format('d.m.Y') }} →
                                    @else
                                        → {{ \Carbon\Carbon::parse(request('date_to'))->format('d.m.Y') }}
                                    @endif
                                @else
                                    Tarih Aralığı
                                @endif
                                <svg id="daterange-arrow" class="h-4 w-4 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                                </svg>
                            </button>

                            <div id="daterange-dropdown"
                                 class="absolute right-0 mt-2 w-80 bg-white rounded-2xl shadow-2xl border border-gray-200 py-3 z-50 hidden opacity-0 scale-95 transition-all duration-200">

                                <div class="px-4 py-2 border-b border-gray-100">
                                    <h3 class="text-sm font-semibold text-gray-900">Tarih Aralığı Seç</h3>
                                        </div>

                                <div class="py-4 px-4">
                                    <!-- Quick Date Ranges -->
                                    <div class="grid grid-cols-2 gap-2 mb-4">
                                        <button onclick="setDateRange('today')" class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Bugün</button>
                                        <button onclick="setDateRange('yesterday')" class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Dün</button>
                                        <button onclick="setDateRange('last7days')" class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Son 7 Gün</button>
                                        <button onclick="setDateRange('last30days')" class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Son 30 Gün</button>
                                        <button onclick="setDateRange('thismonth')" class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Bu Ay</button>
                                        <button onclick="setDateRange('lastmonth')" class="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Geçen Ay</button>
                                    </div>

                                    <!-- Custom Date Inputs -->
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-xs font-medium text-gray-500 mb-1">Başlangıç Tarihi</label>
                                            <input type="date" id="date-from" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" value="{{ request('date_from') }}">
                                </div>
                                        <div>
                                            <label class="block text-xs font-medium text-gray-500 mb-1">Bitiş Tarihi</label>
                                            <input type="date" id="date-to" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" value="{{ request('date_to') }}">
                            </div>
                        </div>

                                    <!-- Action Buttons -->
                                    <div class="flex gap-2 mt-4">
                                        <button onclick="clearDateRange()"
                                                class="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-sm font-medium">
                                            Temizle
                                        </button>
                                        <button onclick="applyDateRange()"
                                                class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm font-medium">
                                            Uygula
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{{ route('customers.create') }}"
                           class="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors duration-200 cursor-pointer">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                            </svg>
                            Yeni Müşteri
                        </a>
                    </div>
                </div>
            </div>



            <!-- Results Section -->
            <div class="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">

                <!-- DataTable -->
                <div class="overflow-x-auto p-4">
                    <table id="customers-table" class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-4 px-6 font-semibold text-gray-900">Müşteri</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-900">İletişim</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-900">Grup/Tip</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-900">Danışman</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-900">Durum</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-900">Kayıt Tarihi</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-900">İşlemler</th>
                            </tr>
                        </thead>
                    </table>
                </div>

                @if(false)
                    <!-- Empty State -->
                    <div class="text-center py-12">
                        <div class="flex justify-center mb-4">
                            <div class="flex items-center justify-center w-16 h-16 bg-gray-100 text-gray-400 rounded-full">
                                <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Müşteri bulunamadı</h3>
                        <p class="text-gray-600 mb-6">
                            @if(request()->hasAny(['search', 'group', 'type', 'status', 'agent']))
                                Arama kriterlerinize uygun müşteri bulunamadı. Filtreleri değiştirmeyi deneyin.
                            @else
                                Henüz hiç müşteri eklenmemiş. İlk müşterinizi eklemek için aşağıdaki butona tıklayın.
                            @endif
                        </p>
                        <a href="{{ route('customers.create') }}"
                           class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 cursor-pointer">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                            </svg>
                            İlk Müşteriyi Ekle
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
    @endsection

    @push('styles')
    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- DataTables CSS - Latest Version 2.3.3 -->
    <link rel="stylesheet" href="https://cdn.datatables.net/2.3.3/css/dataTables.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/3.0.3/css/responsive.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.2.0/css/buttons.dataTables.min.css">

    <!-- Custom Modern DataTable Styles -->
    <style>
        /* Modern DataTable Container */
        .dataTables_wrapper {
            background: #ffffff !important;
            border-radius: 20px !important;
            border: 1px solid #e2e8f0 !important;
            box-shadow:
                0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* Modern Table Styling */
        #customers-table {
            border-collapse: separate !important;
            border-spacing: 0 !important;
            background: transparent !important;
        }

                /* Header Styling */
        #customers-table thead th {
            background: #f8fafc !important;
            color: #1e293b !important;
            font-weight: 700 !important;
            font-size: 13px !important;
            padding: 18px 20px !important;
            border: none !important;
            border-bottom: 2px solid #e2e8f0 !important;
            text-transform: none !important;
            letter-spacing: 0.025em !important;
            position: relative !important;
            white-space: nowrap !important;
            z-index: 0;
        }

        #customers-table thead th:hover {
            background: #f1f5f9 !important;
            color: #0f172a !important;
        }

        #customers-table thead th:first-child {
            border-top-left-radius: 16px !important;
            padding-left: 24px !important;
        }

        #customers-table thead th:last-child {
            border-top-right-radius: 16px !important;
            padding-right: 24px !important;
        }

        /* Header Icons */
        #customers-table thead th::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 2px;
            opacity: 0.7;
        }

                /* Row Styling */
        #customers-table tbody tr {
            background: rgba(255, 255, 255, 0.95) !important;
            border: none !important;
            transition: all 0.2s ease !important;
        }

        #customers-table tbody tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.95) !important;
        }

        #customers-table tbody tr:hover {
            background: rgba(239, 246, 255, 0.95) !important;
            box-shadow:
                4px 0 0 0 #3b82f6,
                0 4px 12px -2px rgba(59, 130, 246, 0.15) !important;
        }

        /* Cell Styling */
        #customers-table tbody td {
            padding: 16px !important;
            border: none !important;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
            vertical-align: middle !important;
        }

        /* Modern Buttons */
        .dt-buttons {
            margin-bottom: 12px !important;
        }

        .dt-button {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 10px !important;
            padding: 10px 12px !important;
            font-weight: 600 !important;
            font-size: 14px !important;
            margin-right: 8px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
        }

        .dt-button:hover {
            transform: translateY(0) !important;
            --tw-translate-y: 0;

        }

        .dt-button.buttons-collection {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3) !important;
        }

        /* Pagination */
        .dataTables_paginate {
            padding: 16px 24px 20px 24px !important;
            background: #f8fafc !important;
            border-top: 1px solid #e2e8f0 !important;
            border-radius: 0 0 20px 20px !important;
        }

        .paginate_button {
            background: #ffffff !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 10px !important;
            margin: 0 4px !important;
            padding: 10px 16px !important;
            transition: all 0.3s ease !important;
        }

        .paginate_button:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            color: white !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3) !important;
        }

        .paginate_button.current {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            color: white !important;
            border-color: transparent !important;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
        }

        /* Search and Length */
        .dataTables_filter input,
        .dataTables_length select {
            background: #ffffff !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 12px !important;
            padding: 10px 16px !important;
            transition: all 0.3s ease !important;
        }

        .dataTables_filter input:focus,
        .dataTables_length select:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
            outline: none !important;
        }

        /* Info Text */
        .dataTables_info {
            color: #64748b !important;
            font-weight: 500 !important;
        }

        /* Processing */
        .dataTables_processing {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(147, 51, 234, 0.9) 100%) !important;
            color: white !important;
            border-radius: 16px !important;
            padding: 20px !important;
            font-weight: 600 !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
        }

        /* Dropdown Collection */
        .dt-button-collection {
            background: #ffffff !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 16px !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
            padding: 8px !important;
        }

        .dt-button-collection .dt-button {
            background: transparent !important;
            color: #374151 !important;
            border-radius: 8px !important;
            margin: 2px !important;
            box-shadow: none !important;
        }

        .dt-button-collection .dt-button:hover {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
            transform: none !important;
        }

        /* Avatar Styling */
        .customer-avatar {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
        }

        /* Status Badges */
        .status-badge {
            border-radius: 20px !important;
            font-weight: 600 !important;
            font-size: 12px !important;
            padding: 6px 12px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        /* Action Buttons */
        .action-btn {
            border-radius: 10px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .action-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
        }

        /* Dropdown Menu Styles */
        .dropdown-menu {
            transition: all 0.2s ease-out;
            transform-origin: top right;
        }

        .dropdown-menu.opacity-0 {
            opacity: 0;
        }

        .dropdown-menu.opacity-100 {
            opacity: 1;
        }

        .dropdown-menu.scale-95 {
            transform: scale(0.95);
        }

        .dropdown-menu.scale-100 {
            transform: scale(1);
        }

        .dropdown-menu.hidden {
            display: none;
        }

        /* Dropdown item hover effects */
        .dropdown-menu a:hover {
            background: rgba(59, 130, 246, 0.05) !important;
        }

        .dropdown-menu a:hover .bg-blue-100 {
            background: rgba(59, 130, 246, 0.2) !important;
        }

        .dropdown-menu a:hover .bg-purple-100 {
            background: rgba(147, 51, 234, 0.2) !important;
        }

        .dropdown-menu a:hover .bg-green-100 {
            background: rgba(34, 197, 94, 0.2) !important;
        }

        .dropdown-menu a:hover .bg-orange-100 {
            background: rgba(249, 115, 22, 0.2) !important;
        }

        .dropdown-menu a:hover .bg-indigo-100 {
            background: rgba(99, 102, 241, 0.2) !important;
        }

        .dropdown-menu button:hover .bg-red-100 {
            background: rgba(239, 68, 68, 0.2) !important;
        }

        /* Filter Dropdown Animations */
        #quick-filter-dropdown,
        #customer-type-dropdown,
        #agent-dropdown,
        #daterange-dropdown {
            transform-origin: top right;
        }

        #quick-filter-dropdown.opacity-0,
        #customer-type-dropdown.opacity-0,
        #agent-dropdown.opacity-0,
        #daterange-dropdown.opacity-0 {
            opacity: 0;
        }

        #quick-filter-dropdown.opacity-100,
        #customer-type-dropdown.opacity-100,
        #agent-dropdown.opacity-100,
        #daterange-dropdown.opacity-100 {
            opacity: 1;
        }

        #quick-filter-dropdown.scale-95,
        #customer-type-dropdown.scale-95,
        #agent-dropdown.scale-95,
        #daterange-dropdown.scale-95 {
            transform: scale(0.95);
        }

        #quick-filter-dropdown.scale-100,
        #customer-type-dropdown.scale-100,
        #agent-dropdown.scale-100,
        #daterange-dropdown.scale-100 {
            transform: scale(1);
        }

        #filter-arrow.rotate-180,
        #type-arrow.rotate-180,
        #agent-arrow.rotate-180,
        #daterange-arrow.rotate-180 {
            transform: rotate(180deg);
        }
    </style>
    @endpush

        @push('scripts')

    <!-- DataTables JS - Latest Version -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/2.3.3/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/3.0.3/js/dataTables.responsive.min.js"></script>

    <!-- DataTables Buttons Extension -->
    <script src="https://cdn.datatables.net/buttons/3.2.0/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.print.min.js"></script>

    <!-- Required for Excel/PDF Export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>

    <script>
    // Global table variable
    let table;

    $(document).ready(function() {
                // DataTable initialization
        table = $('#customers-table').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: '{{ route("customers.datatable") }}',
                data: function(d) {
                    // Dropdown filtrelerini ekle
                    d.status_filter = getActiveFilter('status');
                    d.group_filter = getActiveFilter('group');
                    d.type_filter = getActiveFilter('type');
                    d.agent_filter = getActiveFilter('agent');
                    d.date_from = $('input[name="date_from"]').val();
                    d.date_to = $('input[name="date_to"]').val();

                    // URL parametrelerinden gelen filtreler
                    d.type = getActiveFilter('type');
                    d.agent = getActiveFilter('agent');
                    d.date_from = getActiveFilter('date_from');
                    d.date_to = getActiveFilter('date_to');
                }
            },
            columns: [
                { data: 'customer_info', name: 'first_name', orderable: true, searchable: true, title: 'Müşteri' },
                { data: 'contact_info', name: 'email', orderable: true, searchable: true, title: 'İletişim' },
                { data: 'group_type', name: 'customer_group_id', orderable: false, searchable: false, title: 'Grup/Tip' },
                { data: 'agent_info', name: 'agent_id', orderable: false, searchable: false, title: 'Danışman' },
                { data: 'status_badge', name: 'status', orderable: true, searchable: true, title: 'Durum' },
                { data: 'registration_date', name: 'created_at', orderable: true, searchable: false, title: 'Kayıt Tarihi' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false, title: 'İşlemler' }
            ],
            order: [[5, 'desc']], // Kayıt tarihine göre sırala
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],

            // Buttons Configuration
            layout: {
                topStart: {
                    buttons: [
                        {
                            extend: 'collection',
                            text: '<i class="fas fa-download mr-2"></i>Dışa Aktar',
                            className: 'btn btn-primary dropdown-toggle',
                            background: false,
                            buttons: [
                                {
                                    extend: 'excel',
                                    text: '<i class="fas fa-file-excel mr-2"></i>Excel (.xlsx)',
                                    className: 'btn btn-success',
                                    title: 'Müşteri Listesi',
                                    filename: 'musteriler_' + new Date().toISOString().slice(0,10),
                                    exportOptions: {
                                        columns: [0, 1, 2, 3, 4, 5] // İşlemler kolonu hariç
                                    }
                                },
                                {
                                    extend: 'csv',
                                    text: '<i class="fas fa-file-csv mr-2"></i>CSV Dosyası',
                                    className: 'btn btn-info',
                                    title: 'Müşteri Listesi',
                                    filename: 'musteriler_' + new Date().toISOString().slice(0,10),
                                    exportOptions: {
                                        columns: [0, 1, 2, 3, 4, 5]
                                    }
                                },
                                {
                                    extend: 'pdf',
                                    text: '<i class="fas fa-file-pdf mr-2"></i>PDF Raporu',
                                    className: 'btn btn-danger',
                                    title: 'Müşteri Listesi',
                                    filename: 'musteriler_' + new Date().toISOString().slice(0,10),
                                    orientation: 'landscape',
                                    pageSize: 'A4',
                                    exportOptions: {
                                        columns: [0, 1, 2, 3, 4, 5]
                                    },
                                    customize: function(doc) {
                                        doc.content[1].table.widths = ['20%', '20%', '15%', '15%', '15%', '15%'];
                                        doc.styles.tableHeader.fontSize = 10;
                                        doc.defaultStyle.fontSize = 8;
                                    }
                                },
                                {
                                    extend: 'print',
                                    text: '<i class="fas fa-print mr-2"></i>Yazdır',
                                    className: 'btn btn-secondary',
                                    title: 'Müşteri Listesi',
                                    exportOptions: {
                                        columns: [0, 1, 2, 3, 4, 5]
                                    }
                                }
                            ]
                        },
                        {
                            text: '<i class="fas fa-sync-alt mr-2"></i>Yenile',
                            className: 'btn btn-outline-primary',
                            action: function(e, dt, node, config) {
                                if (dt && dt.ajax) {
                                dt.ajax.reload();
                                }
                            }
                        }
                    ]
                }
            },

            language: {
        url: 'https://cdn.datatables.net/plug-ins/2.3.3/i18n/tr.json',
    },
                        initComplete: function() {
                // Modern tablo başlatma işlemleri
                console.log('Modern DataTable başlatıldı!');

                // Arama kutusuna placeholder ekle
                $('.dataTables_filter input').attr('placeholder', 'İsim, email, telefon ara...');

                // Arama kutusuna focus olduğunda placeholder'ı temizle
                $('.dataTables_filter input').on('focus', function() {
                    $(this).attr('placeholder', '');
                }).on('blur', function() {
                    if ($(this).val() === '') {
                        $(this).attr('placeholder', 'İsim, email, telefon ara...');
                    }
                });

                // DataTable içindeki navigation linklerinin normal çalışması için
                $('#customers-table').on('click', 'a[data-action="navigate"]', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    window.location.href = this.href;
                });
            }
        });

    });

    // Global functions - DataTable dışında tanımlanmalı
        function getActiveFilter(type) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(type) || '';
        }

    // Filtre dropdown'larından tıklama
    $(document).on('click', '[href*="status="], [href*="date_from="], [href*="type="], [href*="agent="]', function(e) {
            e.preventDefault();
            const url = new URL(this.href);
            const params = new URLSearchParams(url.search);

            // URL'yi güncelle
            const newUrl = new URL(window.location);

        // Mevcut parametreleri temizle (sadece ilgili filtreyi güncelle)
        const currentParam = Array.from(params.keys())[0];
        if (currentParam) {
            if (params.get(currentParam)) {
                newUrl.searchParams.set(currentParam, params.get(currentParam));
                } else {
                newUrl.searchParams.delete(currentParam);
                }
        }

            window.history.pushState({}, '', newUrl);

            // DataTable'ı yenile
        if (table) {
            table.ajax.reload();
        }
        });

        // Form submit olayları
        $('form').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    params.set(key, value);
                }
            }

            // URL'yi güncelle
            const newUrl = new URL(window.location);
            newUrl.search = params.toString();
            window.history.pushState({}, '', newUrl);

            // DataTable'ı yenile
        if (table) {
            table.ajax.reload();
        }
        });

        // Temizle butonu
        $(document).on('click', 'a[href*="customers"]', function(e) {
            if (this.href.includes('customers') && !this.href.includes('show') && !this.href.includes('edit') && !this.href.includes('create')) {
                e.preventDefault();
                window.history.pushState({}, '', '{{ route("customers.index") }}');
            if (table) {
                table.ajax.reload();
            }
            }
    });

    // Dropdown menü fonksiyonları
    function toggleDropdown(dropdownId) {
        // Tüm açık dropdown'ları kapat
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            if (menu.id !== dropdownId && !menu.classList.contains('hidden')) {
                menu.classList.remove('opacity-100', 'scale-100');
                menu.classList.add('opacity-0', 'scale-95');
                setTimeout(() => {
                menu.classList.add('hidden');
                }, 200);
            }
        });

        // Seçili dropdown'ı aç/kapat
        const dropdown = document.getElementById(dropdownId);
        const isHidden = dropdown.classList.contains('hidden');

        if (isHidden) {
            // Dropdown'ı aç
            dropdown.classList.remove('hidden');
            setTimeout(() => {
                dropdown.classList.remove('opacity-0', 'scale-95');
                dropdown.classList.add('opacity-100', 'scale-100');
            }, 10);
        } else {
            // Dropdown'ı kapat
            dropdown.classList.remove('opacity-100', 'scale-100');
            dropdown.classList.add('opacity-0', 'scale-95');
            setTimeout(() => {
                dropdown.classList.add('hidden');
            }, 200);
        }
    }

    // Action dropdown dışına tıklandığında kapat
    document.addEventListener('click', function(event) {
        // Eğer tıklanan element bir action button değilse (navigation linkler hariç)
        if (!event.target.closest('.action-btn') && !event.target.closest('.dropdown-menu') && !event.target.closest('.action-link')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // DataTable içindeki navigation linklerinin normal çalışması için
    $(document).on('click', 'a.action-link[data-action="navigate"]', function(e) {
        e.stopPropagation();
        e.preventDefault();
        window.location.href = this.href;
    });

    // Müşteri işlem fonksiyonları
    function openCustomerNotes(customerId) {
        // Müşteri notları modalını aç
        console.log('Müşteri notları açılıyor:', customerId);
        // TODO: Modal implementasyonu
        alert('Müşteri notları özelliği yakında eklenecek!');
        closeAllDropdowns();
    }

    function createPresentation(customerId) {
        // Sunum oluşturma sayfasına yönlendir
        console.log('Sunum oluşturuluyor:', customerId);
        // TODO: Sunum oluşturma sayfası
        alert('Sunum oluşturma özelliği yakında eklenecek!');
        closeAllDropdowns();
    }

    function customerActions(customerId) {
        // Müşteri aksiyonları sayfasına yönlendir
        console.log('Müşteri aksiyonları:', customerId);
        // TODO: Aksiyon yönetimi sayfası
        alert('Müşteri aksiyonları özelliği yakında eklenecek!');
        closeAllDropdowns();
    }

    function customerRequests(customerId) {
        // Müşteri talepleri sayfasına yönlendir
        console.log('Müşteri talepleri:', customerId);
        // TODO: Talep yönetimi sayfası
        alert('Müşteri talepleri özelliği yakında eklenecek!');
        closeAllDropdowns();
    }

    function customerReports(customerId) {
        // Müşteri raporları sayfasına yönlendir
        console.log('Müşteri raporları:', customerId);
        // TODO: Rapor sayfası
        alert('Müşteri raporları özelliği yakında eklenecek!');
        closeAllDropdowns();
    }

    function deleteCustomer(customerId) {
        if (confirm('Bu müşteriyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
            // CSRF token al
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Form oluştur ve gönder
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/customers/${customerId}`;

            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';

            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = token;

            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            form.submit();
        }
        closeAllDropdowns();
    }

    function closeAllDropdowns() {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            if (!menu.classList.contains('hidden')) {
                menu.classList.remove('opacity-100', 'scale-100');
                menu.classList.add('opacity-0', 'scale-95');
                setTimeout(() => {
                    menu.classList.add('hidden');
                }, 200);
            }
        });
    }

        // Modüler Filter Dropdown Yönetimi
    const FilterDropdownManager = {
        // Tüm filter dropdown'ları kapat
        closeAll: function() {
            const dropdowns = [
                { dropdown: '#quick-filter-dropdown', arrow: '#filter-arrow' },
                { dropdown: '#customer-type-dropdown', arrow: '#type-arrow' },
                { dropdown: '#agent-dropdown', arrow: '#agent-arrow' },
                { dropdown: '#daterange-dropdown', arrow: '#daterange-arrow' }
            ];

            dropdowns.forEach(item => {
                const $dropdown = $(item.dropdown);
                const $arrow = $(item.arrow);

                if (!$dropdown.hasClass('hidden')) {
                    $dropdown.removeClass('opacity-100 scale-100').addClass('opacity-0 scale-95');
                    setTimeout(() => {
                        $dropdown.addClass('hidden');
                    }, 200);
                    $arrow.removeClass('rotate-180');
                }
            });
        },

        // Belirli dropdown'ı toggle et
        toggle: function(dropdownId, arrowId) {
            const $dropdown = $(dropdownId);
            const $arrow = $(arrowId);
            const isHidden = $dropdown.hasClass('hidden');

            // Önce tüm dropdown'ları kapat
            this.closeAll();

            if (isHidden) {
                // Dropdown'ı aç
                $dropdown.removeClass('hidden');
                setTimeout(() => {
                    $dropdown.removeClass('opacity-0 scale-95').addClass('opacity-100 scale-100');
                }, 10);
                $arrow.addClass('rotate-180');
            }
        }
    };

    // Müşteri Tipi Filtre Fonksiyonları
    function clearCustomerTypes() {
        $('.customer-type-checkbox').prop('checked', false);
        applyCustomerTypeFilter();
    }

    function applyCustomerTypeFilter() {
        const selectedTypes = [];
        $('.customer-type-checkbox:checked').each(function() {
            selectedTypes.push($(this).val());
        });

        const newUrl = new URL(window.location);
        if (selectedTypes.length > 0) {
            newUrl.searchParams.set('type', selectedTypes.join(','));
        } else {
            newUrl.searchParams.delete('type');
        }

        window.history.pushState({}, '', newUrl);
        if (table) {
            table.ajax.reload();
        }
        FilterDropdownManager.closeAll();
    }

    // Tarih Aralığı Fonksiyonları
    function setDateRange(range) {
        const today = new Date();
        let startDate, endDate;

        switch(range) {
            case 'today':
                startDate = endDate = today.toISOString().split('T')[0];
                break;
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(today.getDate() - 1);
                startDate = endDate = yesterday.toISOString().split('T')[0];
                break;
            case 'last7days':
                const week = new Date(today);
                week.setDate(today.getDate() - 7);
                startDate = week.toISOString().split('T')[0];
                endDate = today.toISOString().split('T')[0];
                break;
            case 'last30days':
                const month = new Date(today);
                month.setDate(today.getDate() - 30);
                startDate = month.toISOString().split('T')[0];
                endDate = today.toISOString().split('T')[0];
                break;
            case 'thismonth':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                endDate = today.toISOString().split('T')[0];
                break;
            case 'lastmonth':
                const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                startDate = lastMonth.toISOString().split('T')[0];
                endDate = lastMonthEnd.toISOString().split('T')[0];
                break;
        }

        $('#date-from').val(startDate);
        $('#date-to').val(endDate);
    }

    function clearDateRange() {
        $('#date-from').val('');
        $('#date-to').val('');
        applyDateRange();
    }

    function applyDateRange() {
        const dateFrom = $('#date-from').val();
        const dateTo = $('#date-to').val();

        const newUrl = new URL(window.location);
        if (dateFrom) {
            newUrl.searchParams.set('date_from', dateFrom);
        } else {
            newUrl.searchParams.delete('date_from');
        }

        if (dateTo) {
            newUrl.searchParams.set('date_to', dateTo);
        } else {
            newUrl.searchParams.delete('date_to');
        }

        window.history.pushState({}, '', newUrl);
        if (table) {
            table.ajax.reload();
        }
        FilterDropdownManager.closeAll();
    }

    // Filter dropdown event handlers - DOM ready olduğunda çalışacak
    $(document).ready(function() {
        // Hızlı filtre dropdown toggle
        $('#quick-filter-btn').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            FilterDropdownManager.toggle('#quick-filter-dropdown', '#filter-arrow');
        });

        // Müşteri tipi dropdown toggle
        $('#customer-type-btn').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            FilterDropdownManager.toggle('#customer-type-dropdown', '#type-arrow');
        });

        // Danışman dropdown toggle
        $('#agent-btn').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            FilterDropdownManager.toggle('#agent-dropdown', '#agent-arrow');
        });

        // Tarih aralığı dropdown toggle
        $('#daterange-btn').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            FilterDropdownManager.toggle('#daterange-dropdown', '#daterange-arrow');
        });

        // Filter dropdown dışına tıklandığında kapat
        $(document).on('click', function(e) {
            const filterElements = '#quick-filter-btn, #quick-filter-dropdown, #customer-type-btn, #customer-type-dropdown, #agent-btn, #agent-dropdown, #daterange-btn, #daterange-dropdown';
            const actionElements = '.action-btn, .dropdown-menu';

            // Eğer filter elementi veya action elementi değilse filter dropdown'ları kapat
            if (!$(e.target).closest(filterElements).length && !$(e.target).closest(actionElements).length) {
                FilterDropdownManager.closeAll();
            }
        });

        // Dropdown içindeki linklere tıklandığında dropdown'ı kapat
        $('#quick-filter-dropdown a, #agent-dropdown a').on('click', function() {
            FilterDropdownManager.closeAll();
        });

        // ESC tuşu ile dropdown'ları kapat
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                FilterDropdownManager.closeAll();
            }
        });
    });
    </script>
    @endpush
</x-backend-layout>
