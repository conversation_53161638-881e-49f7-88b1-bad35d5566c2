<x-backend-layout>
    @section('content')
        <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-20">
                <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
                <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
                <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
                <!-- Header Section -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        <div>
                            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                                {{ $role->name }}
                            </h1>
                            <p class="text-gray-600 text-lg mt-2">Rol detayları ve yetkileri</p>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center gap-4">
                            <a href="{{ route('roles.edit', $role->id) }}"
                               class="flex items-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                </svg>
                                Düzenle
                            </a>
                            <a href="{{ route('roles.index') }}"
                               class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                                </svg>
                                Geri Dön
                            </a>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Role Information -->
                    <div class="lg:col-span-1">
                        <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl">
                            <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                                <svg class="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                                </svg>
                                Rol Bilgileri
                            </h3>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Rol Adı:</span>
                                    <span class="text-gray-900 font-semibold">{{ $role->name }}</span>
                                </div>

                                @if($role->description)
                                    <div class="py-3 border-b border-gray-100">
                                        <span class="text-gray-600 font-medium block mb-2">Açıklama:</span>
                                        <span class="text-gray-900">{{ $role->description }}</span>
                                    </div>
                                @endif

                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Durum:</span>
                                    <span class="px-3 py-1 text-sm font-medium rounded-full {{ $role->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $role->is_active ? 'Aktif' : 'Pasif' }}
                                    </span>
                                </div>

                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Yetki Sayısı:</span>
                                    <span class="text-gray-900 font-semibold">{{ $role->permissions->count() }}</span>
                                </div>

                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Kullanıcı Sayısı:</span>
                                    <span class="text-gray-900 font-semibold">{{ $role->users->count() }}</span>
                                </div>

                                <div class="flex justify-between items-center py-3">
                                    <span class="text-gray-600 font-medium">Oluşturulma:</span>
                                    <span class="text-gray-900">{{ $role->created_at->format('d.m.Y H:i') }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Users with this role -->
                        @if($role->users->count() > 0)
                            <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl mt-8">
                                <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                    </svg>
                                    Bu Role Sahip Kullanıcılar
                                </h3>

                                <div class="space-y-3">
                                    @foreach($role->users as $user)
                                        <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                            @if($user->profile && $user->profile->avatar_url)
                                                <img src="{{ asset($user->profile->avatar_url) }}"
                                                     alt="{{ $user->name }}"
                                                     class="w-10 h-10 rounded-full object-cover">
                                            @else
                                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
                                                    <span class="text-white font-semibold text-sm">
                                                        {{ substr($user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                            @endif
                                            <div class="flex-1">
                                                <div class="font-medium text-gray-900">{{ $user->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                            </div>
                                            <a href="{{ route('users.show', $user->id) }}"
                                               class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                                                Görüntüle
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Permissions -->
                    <div class="lg:col-span-2">
                        <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl">
                            <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                                <svg class="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                                </svg>
                                Yetkiler ({{ $role->permissions->count() }})
                            </h3>

                            @if($role->permissions->count() > 0)
                                @php
                                    $permissionGroups = [
                                        'Kullanıcı Yönetimi' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'user');
                                        }),
                                        'Şirket Yönetimi' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'company') || str_contains($p->name, 'manage_');
                                        }),
                                        'Emlak Yönetimi' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'property') || str_contains($p->name, 'properties');
                                        }),
                                        'Müşteri Yönetimi' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'customer');
                                        }),
                                        'Raporlar' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'report');
                                        }),
                                        'Finansal' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'financial');
                                        }),
                                        'İletişim' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'message') || str_contains($p->name, 'email');
                                        }),
                                        'Sistem' => $role->permissions->filter(function($p) {
                                            return str_contains($p->name, 'api') || str_contains($p->name, 'webhook') || str_contains($p->name, 'template');
                                        }),
                                        'Diğer' => $role->permissions->filter(function($p) {
                                            return !str_contains($p->name, 'user') && !str_contains($p->name, 'company') &&
                                                   !str_contains($p->name, 'manage_') && !str_contains($p->name, 'property') &&
                                                   !str_contains($p->name, 'properties') && !str_contains($p->name, 'customer') &&
                                                   !str_contains($p->name, 'report') && !str_contains($p->name, 'financial') &&
                                                   !str_contains($p->name, 'message') && !str_contains($p->name, 'email') &&
                                                   !str_contains($p->name, 'api') && !str_contains($p->name, 'webhook') &&
                                                   !str_contains($p->name, 'template');
                                        })
                                    ];
                                @endphp

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    @foreach($permissionGroups as $groupName => $groupPermissions)
                                        @if($groupPermissions->count() > 0)
                                            <div class="bg-gray-50 rounded-xl p-6">
                                                <h4 class="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    {{ $groupName }}
                                                    <span class="text-sm text-gray-500">({{ $groupPermissions->count() }})</span>
                                                </h4>
                                                <div class="space-y-2">
                                                    @foreach($groupPermissions as $permission)
                                                        <div class="flex items-center gap-2 p-2 bg-white rounded-lg">
                                                            <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                                            </svg>
                                                            <span class="text-sm text-gray-700">{{ $permission->label }}</span>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                                    </svg>
                                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Henüz Yetki Yok</h4>
                                    <p class="text-gray-600 mb-6">Bu role henüz hiç yetki atanmamış.</p>
                                    <a href="{{ route('roles.edit', $role->id) }}"
                                       class="inline-flex items-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors duration-200">
                                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                        </svg>
                                        Yetki Ekle
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endsection
</x-backend-layout>
