<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Package;
use Illuminate\Http\Request;

class PackageController extends Controller
{
    public function index()
    {
        $packages = Package::where('is_active', true)
                          ->orderBy('sort_order', 'asc')
                          ->get();
        return view('pricing', compact('packages'));
    }
}
