<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_category_form_fields', function (Blueprint $table) {
            $table->id();
    $table->foreignId('category_id')->constrained('portfolio_categories')->cascadeOnDelete();
    $table->string('field_name'); // 'room_count', 'building_age', 'zoning_status'
    $table->string('field_label'); // 'Oda Sayısı', '<PERSON>a Yaşı', 'İmar Durumu'
    $table->enum('field_type', ['text', 'number', 'select', 'boolean', 'textarea', 'multi_select']);
    $table->json('field_options')->nullable(); // Select için seçenekler
    $table->string('field_group')->nullable(); // 'basic_info', 'building_info', 'land_info'
    $table->boolean('is_required')->default(false);
    $table->boolean('is_searchable')->default(false); // Arama filtrelerinde gösterilsin mi?
    $table->string('placeholder')->nullable();
    $table->text('help_text')->nullable();
    $table->integer('sort_order')->default(0);
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_category_form_fields');
    }
};
