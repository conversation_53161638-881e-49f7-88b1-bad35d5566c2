<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Package;

class PackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Başlang<PERSON><PERSON> (Ücretsiz)
        Package::create([
            'name' => 'Başlangıç',
            'slug' => 'baslangic',
            'description' => 'Küçük emlak ofisleri için temel özellikler',
            'monthly_price' => 0,
            'yearly_price' => 0,
            'features' => [
                'Temel İlan Yönetimi',
                'E-posta Desteği',
                'Temel Raporlar',
                'Mobil Erişim'
            ],
            'limits' => [
                'max_staff' => 2,
                'max_properties' => 50,
                'max_storage_gb' => 1,
                'max_photo_per_property' => 10,
                'has_api_access' => false,
                'has_custom_domain' => false,
                'has_advanced_reports' => false,
                'has_crm' => false
            ],
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Profesyonel Paket
        Package::create([
            'name' => 'Profesyonel',
            'slug' => 'profesyonel',
            'description' => 'Orta büyüklükteki emlak ofisleri için gelişmiş özellikler',
            'monthly_price' => 249,
            'yearly_price' => 2490,
            'features' => [
                'Gelişmiş İlan Yönetimi',
                'CRM Sistemi',
                'Detaylı Raporlar',
                'WhatsApp Entegrasyonu',
                'Özel Alan Adı',
                'API Erişimi',
                'Öncelikli Destek',
                'Video Tur Desteği'
            ],
            'limits' => [
                'max_staff' => 10,
                'max_properties' => 500,
                'max_storage_gb' => 10,
                'max_photo_per_property' => 30,
                'has_api_access' => true,
                'has_custom_domain' => true,
                'has_advanced_reports' => true,
                'has_crm' => true
            ],
            'is_popular' => true,
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Kurumsal Paket
        Package::create([
            'name' => 'Kurumsal',
            'slug' => 'kurumsal',
            'description' => 'Büyük emlak şirketleri için sınırsız özellikler',
            'monthly_price' => 599,
            'yearly_price' => 5990,
            'features' => [
                'Sınırsız İlan',
                'Gelişmiş CRM',
                'AI Destekli Analizler',
                'Özel Entegrasyonlar',
                'Çoklu Ofis Yönetimi',
                'Özel Eğitim',
                '7/24 Telefon Desteği',
                'Özel Geliştirmeler'
            ],
            'limits' => [
                'max_staff' => -1, // -1 means unlimited
                'max_properties' => -1,
                'max_storage_gb' => -1,
                'max_photo_per_property' => -1,
                'has_api_access' => true,
                'has_custom_domain' => true,
                'has_advanced_reports' => true,
                'has_crm' => true
            ],
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 3
        ]);
    }
}
