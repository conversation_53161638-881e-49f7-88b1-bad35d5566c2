<?php

namespace Modules\Customer\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\Customer\Database\Factories\CustomerSourceFactory;

class CustomerSource extends Model
{
    use HasFactory;

    protected $table = 'customers_source';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];

    // protected static function newFactory(): CustomerSourceFactory
    // {
    //     // return CustomerSourceFactory::new();
    // }
}
