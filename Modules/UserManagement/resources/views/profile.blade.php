<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <!-- Header Section -->
            <div class="mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div>
                        <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                            Profil Ayarları
                        </h1>
                        <p class="text-gray-600 text-lg mt-2">Kişisel bilgilerinizi ve sistem tercihlerinizi yönetin</p>
                    </div>

                    <!-- Quick Actions -->
                    <div class="flex gap-3">
                        <button class="group bg-white/80 border border-white/30 text-gray-700 px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                            <div class="flex items-center gap-2">
                                <svg class="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                </svg>
                                <span>Sıfırla</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>

            <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    <!-- Profile Card -->
                    <div class="xl:col-span-1">
                        <div class="group relative bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <div class="relative z-10">
                                <!-- Avatar Section -->
                                <div class="text-center mb-8">
                                    <div class="relative inline-block">
                                        <div class="w-32 h-32 mx-auto mb-4 relative">
                                            @if(auth()->user()->profile && auth()->user()->profile->avatar_url)
                                                <img src="{{ asset(auth()->user()->profile->avatar_url) }}"
                                                     alt="{{ auth()->user()->name }}"
                                                     class="w-full h-full rounded-2xl object-cover shadow-lg border-4 border-white">
                                            @else
                                                <div class="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg border-4 border-white">
                                                    <span class="text-white text-4xl font-bold">
                                                        {{ substr(auth()->user()->name, 0, 1) }}
                                                    </span>
                                                </div>
                                            @endif

                                            <!-- Upload Button -->
                                            <label for="image" class="absolute -bottom-2 -right-2 w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110">
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6.827 6.175A2.31 2.31 0 015.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 00-1.134-.175 2.31 2.31 0 01-1.64-1.055l-.822-1.316a2.192 2.192 0 00-1.736-1.039 48.774 48.774 0 00-5.232 0 2.192 2.192 0 00-1.736 1.039l-.821 1.316z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 12.75a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0zM18.75 10.5h.008v.008h-.008V10.5z" />
                                                </svg>
                                            </label>
                                            <input type="file" id="image" name="image" accept="image/*" class="hidden">
                                        </div>

                                        <h2 class="text-2xl font-bold text-gray-900 mb-1">{{ auth()->user()->name }}</h2>
                                        <p class="text-gray-600 mb-2">{{ auth()->user()->email }}</p>

                                        @if(auth()->user()->profile && auth()->user()->profile->title)
                                            <div class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-full text-sm font-medium">
                                                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z" />
                                                </svg>
                                                {{ auth()->user()->profile->title }}
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Stats -->
                                    <div class="grid grid-cols-2 gap-4 mt-6">
                                        <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl border border-blue-200/50">
                                            <div class="text-2xl font-bold text-blue-600">{{ round(auth()->user()->created_at->diffInDays()) }}</div>
                                            <div class="text-xs text-blue-600/70 font-medium">Gün Aktif</div>
                                        </div>
                                        <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-2xl border border-purple-200/50">
                                            <div class="text-2xl font-bold text-purple-600">{{ auth()->user()->email_verified_at ? '✓' : '!' }}</div>
                                            <div class="text-xs text-purple-600/70 font-medium">{{ auth()->user()->email_verified_at ? 'Doğrulandı' : 'Beklemede' }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Info -->
                                <div class="space-y-4">
                                    @if(auth()->user()->profile && auth()->user()->profile->phone)
                                    <div class="flex items-center gap-3 p-3 bg-white/50 rounded-xl border border-white/30">
                                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Telefon</div>
                                            <div class="text-xs text-gray-600">{{ auth()->user()->profile->phone }}</div>
                                        </div>
                                    </div>
                                    @endif

                                    @if(auth()->user()->company)
                                    <div class="flex items-center gap-3 p-3 bg-white/50 rounded-xl border border-white/30">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">Şirket</div>
                                            <div class="text-xs text-gray-600">{{ auth()->user()->company->name }}</div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Form -->
                    <div class="xl:col-span-2 space-y-8">
                        <!-- Personal Information -->
                        <div class="group relative bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <div class="relative z-10">
                                <div class="flex items-center gap-4 mb-6">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-2xl font-bold text-gray-900">Kişisel Bilgiler</h2>
                                        <p class="text-gray-600 text-sm">Temel profil bilgilerinizi güncelleyin</p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Name -->
                                    <div class="form-group">
                                        <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Ad Soyad <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                                </svg>
                                            </div>
                                            <input type="text" id="name" name="name"
                                                   value="{{ old('name', auth()->user()->name) }}"
                                                   required
                                                   class="form-input {{ $errors->has('name') ? 'error' : '' }}">
                                        </div>
                                        @error('name')
                                            <div class="error-message">
                                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                                                </svg>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>

                                    <!-- Email -->
                                    <div class="form-group">
                                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Email Adresi <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                                </svg>
                                            </div>
                                            <input type="email" id="email" name="email"
                                                   value="{{ old('email', auth()->user()->email) }}"
                                                   required
                                                   class="form-input {{ $errors->has('email') ? 'error' : '' }}">
                                        </div>
                                        @error('email')
                                            <div class="error-message">
                                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                                                </svg>
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>

                                    <!-- Title -->
                                    <div class="form-group">
                                        <label for="title" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Pozisyon / Ünvan
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z" />
                                                </svg>
                                            </div>
                                            <input type="text" id="title" name="title"
                                                   value="{{ old('title', optional(auth()->user()->profile)->title) }}"
                                                   placeholder="Örn: Emlak Danışmanı"
                                                   class="form-input">
                                        </div>
                                    </div>

                                    <!-- Phone -->
                                    <div class="form-group">
                                        <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Telefon Numarası
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                                </svg>
                                            </div>
                                            <input type="tel" id="phone" name="phone"
                                                   value="{{ old('phone', optional(auth()->user()->profile)->phone) }}"
                                                   placeholder="0(555)123-45-67"
                                                   class="form-input">
                                        </div>
                                    </div>

                                    <!-- WhatsApp -->
                                    <div class="form-group">
                                        <label for="whatsapp" class="block text-sm font-semibold text-gray-700 mb-2">
                                            WhatsApp
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                                </svg>
                                            </div>
                                            <input type="tel" id="whatsapp" name="whatsapp"
                                                   value="{{ old('whatsapp', optional(auth()->user()->profile)->whatsapp) }}"
                                                   placeholder="0(555)123-45-67"
                                                   class="form-input">
                                        </div>
                                    </div>

                                    <!-- Birth Date -->
                                    <div class="form-group">
                                        <label for="birth_date" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Doğum Tarihi
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                                                </svg>
                                            </div>
                                            <input type="date" id="birth_date" name="birth_date"
                                                   value="{{ old('birth_date', optional(auth()->user()->profile)->birth_date) }}"
                                                   class="form-input">
                                        </div>
                                    </div>
                                </div>

                                <!-- Bio -->
                                <div class="form-group mt-6">
                                    <label for="bio" class="block text-sm font-semibold text-gray-700 mb-2">
                                        Hakkımda
                                    </label>
                                    <textarea id="bio" name="bio" rows="4"
                                              placeholder="Kendiniz hakkında kısa bir açıklama yazın..."
                                              class="form-textarea">{{ old('bio', optional(auth()->user()->profile)->bio) }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Location Information -->
                        <div class="group relative bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <div class="relative z-10">
                                <div class="flex items-center gap-4 mb-6">
                                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-2xl font-bold text-gray-900">Konum Bilgileri</h2>
                                        <p class="text-gray-600 text-sm">Adres ve konum bilgilerinizi güncelleyin</p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- City -->
                                    <div class="form-group">
                                        <label for="city_id" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Şehir
                                        </label>
                                        <select id="city_id" name="city_id" class="form-select">
                                            <option value="">Şehir Seçiniz</option>
                                            @foreach($cities as $city)
                                                <option value="{{ $city->il_id }}"
                                                    {{ old('city_id', optional(auth()->user()->profile)->city_id) == $city->il_id ? 'selected' : '' }}>
                                                    {{ $city->il_adi }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <!-- District -->
                                    <div class="form-group">
                                        <label for="district_id" class="block text-sm font-semibold text-gray-700 mb-2">
                                            İlçe
                                        </label>
                                        <select id="district_id" name="district_id" class="form-select">
                                            <option value="">İlçe Seçiniz</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Address -->
                                <div class="form-group mt-6">
                                    <label for="address" class="block text-sm font-semibold text-gray-700 mb-2">
                                        Adres
                                    </label>
                                    <textarea id="address" name="address" rows="3"
                                              placeholder="Detaylı adres bilginizi girin..."
                                              class="form-textarea">{{ old('address', optional(auth()->user()->profile)->address) }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Theme Settings -->
                        <div class="group relative bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <div class="relative z-10">
                                <div class="flex items-center gap-4 mb-6">
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.098 19.902a3.75 3.75 0 005.304 0l6.401-6.402M6.75 21A3.75 3.75 0 013 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 003.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-2xl font-bold text-gray-900">Tema Ayarları</h2>
                                        <p class="text-gray-600 text-sm">Arayüz tercihlerinizi özelleştirin</p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Theme Color -->
                                    <div class="form-group">
                                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                                            Tema Rengi
                                        </label>
                                        <div class="grid grid-cols-3 gap-3">
                                            @foreach(\App\Models\User::getThemeColors() as $key => $color)
                                            <label class="relative cursor-pointer">
                                                <input type="radio" name="theme_color" value="{{ $key }}"
                                                       {{ old('theme_color', auth()->user()->theme_color) == $key ? 'checked' : '' }}
                                                       class="sr-only peer">
                                                <div class="w-full h-12 rounded-xl border-2 border-gray-200 peer-checked:border-4 peer-checked:border-white peer-checked:shadow-lg transition-all duration-300 flex items-center justify-center text-white font-medium text-sm"
                                                     style="background: linear-gradient(135deg, {{ $color['primary'] }}, {{ $color['dark'] }});">
                                                    {{ $color['name'] }}
                                                </div>
                                            </label>
                                            @endforeach
                                        </div>
                                    </div>


                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="group relative bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <div class="relative z-10">
                                <div class="flex items-center gap-4 mb-6">
                                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-2xl font-bold text-gray-900">Güvenlik Ayarları</h2>
                                        <p class="text-gray-600 text-sm">Şifrenizi değiştirin ve güvenlik ayarlarınızı yönetin</p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <!-- Current Password -->
                                    <div class="form-group">
                                        <label for="current_password" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Mevcut Şifre
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                                </svg>
                                            </div>
                                            <input type="password" id="current_password" name="current_password"
                                                   placeholder="Mevcut şifrenizi girin"
                                                   class="form-input">
                                        </div>
                                    </div>

                                    <!-- New Password -->
                                    <div class="form-group">
                                        <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Yeni Şifre
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                                                </svg>
                                            </div>
                                            <input type="password" id="password" name="password"
                                                   placeholder="Yeni şifrenizi girin"
                                                   class="form-input">
                                        </div>
                                    </div>

                                    <!-- Confirm Password -->
                                    <div class="form-group">
                                        <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 mb-2">
                                            Şifre Tekrar
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <input type="password" id="password_confirmation" name="password_confirmation"
                                                   placeholder="Yeni şifrenizi tekrar girin"
                                                   class="form-input">
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                                    <div class="flex items-start gap-3">
                                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.75 0a9 9 0 1119.5 0 9 9 0 01-19.5 0zm9.75 3.75h.008v.008H12v-.008z" />
                                        </svg>
                                        <div>
                                            <h4 class="text-sm font-semibold text-yellow-800">Güvenlik Uyarısı</h4>
                                            <p class="text-sm text-yellow-700 mt-1">Şifrenizi değiştirmek için önce mevcut şifrenizi girmeniz gerekir. Yeni şifreniz en az 8 karakter olmalı ve güçlü bir şifre seçmeniz önerilir.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4 justify-end">
                            <button type="button" class="group bg-white/80 border border-white/30 text-gray-700 px-8 py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                                <div class="flex items-center justify-center gap-2">
                                    <svg class="w-5 h-5 group-hover:-rotate-12 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
                                    </svg>
                                    <span>İptal</span>
                                </div>
                            </button>

                            <button type="submit" class="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                                <div class="flex items-center justify-center gap-2">
                                    <svg class="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Profili Kaydet</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Hidden Fields -->
                <input type="hidden" name="user_id" value="{{ auth()->user()->id }}">
            </form>
        </div>
    </div>

    @push('scripts')
    <script>
        // City-District functionality
        document.getElementById('city_id').addEventListener('change', function() {
            const cityId = this.value;
            const districtSelect = document.getElementById('district_id');

            if (cityId) {
                fetch(`{{ route('profile.getDistricts') }}?city_id=${cityId}`)
                    .then(response => response.text())
                    .then(html => {
                        districtSelect.innerHTML = html;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        districtSelect.innerHTML = '<option value="">İlçe Seçiniz</option>';
                    });
            } else {
                districtSelect.innerHTML = '<option value="">İlçe Seçiniz</option>';
            }
        });

        // Load districts on page load if city is selected
        document.addEventListener('DOMContentLoaded', function() {
            const citySelect = document.getElementById('city_id');
            if (citySelect.value) {
                citySelect.dispatchEvent(new Event('change'));
            }
        });

        // Image preview
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.querySelector('.w-32.h-32 img, .w-32.h-32 div');
                    if (img.tagName === 'IMG') {
                        img.src = e.target.result;
                    } else {
                        // Replace div with img
                        const newImg = document.createElement('img');
                        newImg.src = e.target.result;
                        newImg.alt = '{{ auth()->user()->name }}';
                        newImg.className = 'w-full h-full rounded-2xl object-cover shadow-lg border-4 border-white';
                        img.parentNode.replaceChild(newImg, img);
                    }
                };
                reader.readAsDataURL(file);
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('password').value;
            const confirmPassword = document.getElementById('password_confirmation').value;

            if (newPassword && !currentPassword) {
                e.preventDefault();
                alert('Şifrenizi değiştirmek için mevcut şifrenizi girmeniz gerekir.');
                document.getElementById('current_password').focus();
                return;
            }

            if (newPassword && newPassword !== confirmPassword) {
                e.preventDefault();
                alert('Yeni şifre ve şifre tekrarı eşleşmiyor.');
                document.getElementById('password_confirmation').focus();
                return;
            }

            if (newPassword && newPassword.length < 8) {
                e.preventDefault();
                alert('Yeni şifreniz en az 8 karakter olmalıdır.');
                document.getElementById('password').focus();
                return;
            }
        });
    </script>
    @endpush
    @endsection
</x-backend-layout>
