<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            PackageSeeder::class,
        ]);

        // Create test super admin user
        $superAdmin = User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
        ]);
        //$superAdmin->assignRole('super_admin');
    }
}
