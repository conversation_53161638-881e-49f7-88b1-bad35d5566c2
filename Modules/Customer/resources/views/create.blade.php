<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <!-- Header Section -->
            <div class="mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div>
                        <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                            Yeni Müşteri Ekle
                        </h1>
                        <p class="text-gray-600 text-lg mt-2">Yeni müşteri bilgilerini ekleyin</p>
                    </div>
                    <!-- Action Buttons -->
                    <div class="flex items-center gap-4">
                        <a href="{{ route('customers.index') }}"
                            class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                            </svg>
                            Geri Dön
                        </a>
                    </div>
                </div>
            </div>

            <!-- Form Container -->
            <div class="bg-white/80 border border-white/20 rounded-3xl shadow-xl overflow-hidden">
                <form action="{{ route('customers.store') }}" method="POST" x-data="{ activeTab: 'personal' }">
                    @csrf

                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200 bg-gray-50/50">
                        <nav class="flex space-x-8 px-8 py-4" aria-label="Tabs">
                            <button type="button"
                                    @click="activeTab = 'personal'"
                                    :class="activeTab === 'personal' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center gap-2 cursor-pointer">
                                <div class="flex items-center justify-center w-6 h-6 rounded-lg"
                                     :class="activeTab === 'personal' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                                    </svg>
                                </div>
                                Kişisel Bilgiler
                            </button>

                            <button type="button"
                                    @click="activeTab = 'contact'"
                                    :class="activeTab === 'contact' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center gap-2 cursor-pointer">
                                <div class="flex items-center justify-center w-6 h-6 rounded-lg"
                                     :class="activeTab === 'contact' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" />
                                    </svg>
                                </div>
                                İletişim
                            </button>

                            <button type="button"
                                    @click="activeTab = 'customer'"
                                    :class="activeTab === 'customer' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center gap-2 cursor-pointer">
                                <div class="flex items-center justify-center w-6 h-6 rounded-lg"
                                     :class="activeTab === 'customer' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                    </svg>
                                </div>
                                Müşteri Bilgileri
                            </button>

                            <button type="button"
                                    @click="activeTab = 'financial'"
                                    :class="activeTab === 'financial' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center gap-2 cursor-pointer">
                                <div class="flex items-center justify-center w-6 h-6 rounded-lg"
                                     :class="activeTab === 'financial' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                    </svg>
                                </div>
                                Finansal
                            </button>

                            <button type="button"
                                    @click="activeTab = 'other'"
                                    :class="activeTab === 'other' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center gap-2 cursor-pointer">
                                <div class="flex items-center justify-center w-6 h-6 rounded-lg"
                                     :class="activeTab === 'other' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c0 .621-.504 1.125-1.125 1.125H18a2.25 2.25 0 0 0 2.25-2.25M6 7.5h3v4.875c0 .621.504 1.125 1.125 1.125h2.25s.621-.504 1.125-1.125V7.5h3m-9 8.25h9" />
                                    </svg>
                                </div>
                                Diğer Bilgiler
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-8">
                        <!-- Kişisel Bilgiler Tab -->
                        <div x-show="activeTab === 'personal'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform translate-x-4"
                             x-transition:enter-end="opacity-100 transform translate-x-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-x-0"
                             x-transition:leave-end="opacity-0 transform -translate-x-4"
                             class="space-y-6">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Ad -->
                            <div>
                                <label for="first_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Ad <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="first_name"
                                       name="first_name"
                                       value="{{ old('first_name') }}"
                                       class="form-input @error('first_name') border-red-500 @enderror"
                                       placeholder="Müşterinin adı" required>
                                @error('first_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Soyad -->
                            <div>
                                <label for="last_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Soyad <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="last_name"
                                       name="last_name"
                                       value="{{ old('last_name') }}"
                                       class="form-input @error('last_name') border-red-500 @enderror"
                                       placeholder="Müşterinin soyadı" required>
                                @error('last_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Cinsiyet -->
                            <div>
                                <label for="gender" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Cinsiyet
                                </label>
                                <select id="gender"
                                        name="gender"
                                        class="form-select @error('gender') border-red-500 @enderror">
                                    <option value="">Seçiniz</option>
                                    <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>Erkek</option>
                                    <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>Kadın</option>
                                    <option value="other" {{ old('gender') == 'other' ? 'selected' : '' }}>Diğer</option>
                                </select>
                                @error('gender')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Doğum Tarihi -->
                            <div>
                                <label for="birth_date" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Doğum Tarihi
                                </label>
                                <input type="date"
                                       id="birth_date"
                                       name="birth_date"
                                       value="{{ old('birth_date') }}"
                                       class="form-input @error('birth_date') border-red-500 @enderror">
                                @error('birth_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Şirket Adı -->
                            <div class="md:col-span-2">
                                <label for="company_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Şirket Adı
                                </label>
                                <input type="text"
                                       id="company_name"
                                       name="company_name"
                                       value="{{ old('company_name') }}"
                                       class="form-input @error('company_name') border-red-500 @enderror"
                                       placeholder="Şirket adı (varsa)">
                                @error('company_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        </div>

                        <!-- İletişim Bilgileri Tab -->
                        <div x-show="activeTab === 'contact'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform translate-x-4"
                             x-transition:enter-end="opacity-100 transform translate-x-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-x-0"
                             x-transition:leave-end="opacity-0 transform -translate-x-4"
                             class="space-y-6">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- E-posta -->
                            <div>
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                                    E-posta Adresi
                                </label>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"
                                       class="form-input @error('email') border-red-500 @enderror"
                                       placeholder="<EMAIL>">
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Telefon -->
                            <div x-data="phoneMask" x-init="userPhone='{{ old('phone') ?? '' }}'; formatPhone()">
                                <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Telefon <span class="text-red-500">*</span>
                                </label>
                                <input type="tel"
                                       id="phone"
                                       name="phone"
                                       x-model="userPhone"
                                       x-on:input="formatPhone()"
                                       class="form-input @error('phone') border-red-500 @enderror"
                                       placeholder="0(555)123-45-67">
                                @error('phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp -->
                            <div x-data="phoneMask" x-init="userPhone='{{ old('whatsapp') ?? '' }}'; formatPhone()">
                                <label for="whatsapp" class="block text-sm font-semibold text-gray-700 mb-2">
                                    WhatsApp
                                </label>
                                <input type="tel"
                                       id="whatsapp"
                                       name="whatsapp"
                                       x-model="userPhone"
                                       x-on:input="formatPhone()"
                                       class="form-input @error('whatsapp') border-red-500 @enderror"
                                       placeholder="0(555)123-45-67">
                                @error('whatsapp')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- İletişim Tercihleri -->
                            <div class="space-y-3">
                                <label class="block text-sm font-semibold text-gray-700">İletişim Tercihleri</label>
                                <div class="space-y-3">
                                    <label class="flex items-center p-3 bg-white/30 border border-gray-200 rounded-xl hover:bg-white/50 cursor-pointer transition-all duration-200">
                                        <input type="checkbox"
                                               name="wants_sms"
                                               value="1"
                                               {{ old('wants_sms') ? 'checked' : '' }}
                                               class="form-checkbox">
                                        <span class="ml-3 text-sm font-medium text-gray-700">SMS almak istiyorum</span>
                                    </label>
                                    <label class="flex items-center p-3 bg-white/30 border border-gray-200 rounded-xl hover:bg-white/50 cursor-pointer transition-all duration-200">
                                        <input type="checkbox"
                                               name="wants_email"
                                               value="1"
                                               {{ old('wants_email') ? 'checked' : '' }}
                                               class="form-checkbox">
                                        <span class="ml-3 text-sm font-medium text-gray-700">E-posta almak istiyorum</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Adres -->
                        <div>
                            <label for="address" class="block text-sm font-semibold text-gray-700 mb-2">
                                Adres
                            </label>
                            <textarea id="address"
                                      name="address"
                                      rows="3"
                                      class="form-textarea @error('address') border-red-500 @enderror"
                                      placeholder="Tam adres bilgisi">{{ old('address') }}</textarea>
                            @error('address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        </div>

                        <!-- Müşteri Bilgileri Tab -->
                        <div x-show="activeTab === 'customer'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform translate-x-4"
                             x-transition:enter-end="opacity-100 transform translate-x-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-x-0"
                             x-transition:leave-end="opacity-0 transform -translate-x-4"
                             class="space-y-6">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Durum -->
                            <div>
                                <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Durum <span class="text-red-500">*</span>
                                </label>
                                <select id="status"
                                        name="status"
                                        class="form-select @error('status') border-red-500 @enderror" required>
                                    <option value="">Seçiniz</option>
                                    <option value="potential" {{ old('status') == 'potential' ? 'selected' : '' }}>Potansiyel</option>
                                    <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                                    <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Pasif</option>
                                </select>
                                @error('status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Müşteri Grubu -->
                            <div>
                                <label for="customer_group_id" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Müşteri Grubu
                                </label>
                                <select id="customer_group_id"
                                        name="customer_group_id"
                                        class="form-select @error('customer_group_id') border-red-500 @enderror">
                                    <option value="">Seçiniz</option>
                                    @foreach($customerGroups as $group)
                                        <option value="{{ $group->id }}" {{ old('customer_group_id') == $group->id ? 'selected' : '' }}>
                                            {{ $group->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('customer_group_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Sorumlu Danışman -->
                            <div>
                                <label for="agent_id" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Sorumlu Danışman
                                </label>
                                <select id="agent_id"
                                        name="agent_id"
                                        class="form-select @error('agent_id') border-red-500 @enderror">
                                    <option value="">Seçiniz</option>
                                    @foreach($agents as $agent)
                                        <option value="{{ $agent->id }}" {{ old('agent_id') == $agent->id ? 'selected' : '' }}>
                                            {{ $agent->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('agent_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Kaynak -->
                            <div>
                                <label for="source" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Kaynak
                                </label>
                                <select id="source"
                                        name="source"
                                        class="form-select @error('source') border-red-500 @enderror">
                                    <option value="">Seçiniz</option>
                                    @foreach($customerSources as $source)
                                        <option value="{{ $source->id }}" {{ old('source') == $source->id ? 'selected' : '' }}>
                                            {{ $source->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('source')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Müşteri Tipleri -->
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">
                                Müşteri Tipleri
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                @foreach($customerTypes as $type)
                                    <label class="flex items-center p-4 bg-white/30 border border-gray-200 rounded-xl hover:bg-white/50 cursor-pointer transition-all duration-200">
                                        <input type="checkbox"
                                               name="customer_types[]"
                                               value="{{ $type->id }}"
                                               {{ in_array($type->id, old('customer_types', [])) ? 'checked' : '' }}
                                               class="form-checkbox">
                                        <span class="ml-3 text-sm font-medium text-gray-700">{{ $type->name }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('customer_types')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        </div>

                        <!-- Finansal Bilgiler Tab -->
                        <div x-show="activeTab === 'financial'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform translate-x-4"
                             x-transition:enter-end="opacity-100 transform translate-x-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-x-0"
                             x-transition:leave-end="opacity-0 transform -translate-x-4"
                             class="space-y-6">

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Minimum Gelir -->
                            <div>
                                <label for="minimum_income" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Minimum Aylık Gelir
                                </label>
                                <input type="number"
                                       id="minimum_income"
                                       name="minimum_income"
                                       value="{{ old('minimum_income') }}"
                                       step="0.01"
                                       min="0"
                                       class="form-input @error('minimum_income') border-red-500 @enderror"
                                       placeholder="0.00">
                                @error('minimum_income')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Yatırım Bütçesi -->
                            <div>
                                <label for="investment_budget" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Yatırım Bütçesi
                                </label>
                                <input type="number"
                                       id="investment_budget"
                                       name="investment_budget"
                                       value="{{ old('investment_budget') }}"
                                       step="0.01"
                                       min="0"
                                       class="form-input @error('investment_budget') border-red-500 @enderror"
                                       placeholder="0.00">
                                @error('investment_budget')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Para Birimi -->
                            <div>
                                <label for="currency" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Para Birimi
                                </label>
                                <select id="currency"
                                        name="currency"
                                        class="form-select @error('currency') border-red-500 @enderror">
                                    <option value="TRY" {{ old('currency', 'TRY') == 'TRY' ? 'selected' : '' }}>TRY - Türk Lirası</option>
                                    <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>USD - Amerikan Doları</option>
                                    <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                    <option value="GBP" {{ old('currency') == 'GBP' ? 'selected' : '' }}>GBP - İngiliz Sterlini</option>
                                </select>
                                @error('currency')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        </div>

                        <!-- Diğer Bilgiler Tab -->
                        <div x-show="activeTab === 'other'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform translate-x-4"
                             x-transition:enter-end="opacity-100 transform translate-x-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform translate-x-0"
                             x-transition:leave-end="opacity-0 transform -translate-x-4"
                             class="space-y-6">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Uyruk -->
                            <div>
                                <label for="nationality" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Uyruk
                                </label>
                                <select id="nationality"
                                        name="nationality"
                                        class="form-select @error('nationality') border-red-500 @enderror">
                                    <option value="">Seçiniz</option>
                                    <option value="tr" {{ old('nationality') == 'tr' ? 'selected' : '' }}>Türkiye</option>
                                    <option value="de" {{ old('nationality') == 'de' ? 'selected' : '' }}>Almanya</option>
                                    <option value="gb" {{ old('nationality') == 'gb' ? 'selected' : '' }}>İngiltere</option>
                                    <option value="abd" {{ old('nationality') == 'abd' ? 'selected' : '' }}>ABD</option>
                                    <option value="sa" {{ old('nationality') == 'sa' ? 'selected' : '' }}>Suudi Arabistan</option>
                                    <option value="ae" {{ old('nationality') == 'ae' ? 'selected' : '' }}>Birleşik Arap Emirlikleri</option>
                                    <option value="kw" {{ old('nationality') == 'kw' ? 'selected' : '' }}>Kuveyt</option>
                                    <option value="fr" {{ old('nationality') == 'fr' ? 'selected' : '' }}>Fransız</option>
                                    <option value="it" {{ old('nationality') == 'it' ? 'selected' : '' }}>İtalya</option>
                                    <option value="es" {{ old('nationality') == 'es' ? 'selected' : '' }}>İspanya</option>
                                    <option value="nl" {{ old('nationality') == 'nl' ? 'selected' : '' }}>Hollanda</option>
                                    <option value="be" {{ old('nationality') == 'be' ? 'selected' : '' }}>Belçika</option>
                                    <option value="ch" {{ old('nationality') == 'ch' ? 'selected' : '' }}>İsviçre</option>
                                    <option value="at" {{ old('nationality') == 'at' ? 'selected' : '' }}>Avusturya</option>
                                    <option value="se" {{ old('nationality') == 'se' ? 'selected' : '' }}>İsveç</option>
                                    <option value="no" {{ old('nationality') == 'no' ? 'selected' : '' }}>Norveç</option>
                                    <option value="dk" {{ old('nationality') == 'dk' ? 'selected' : '' }}>Danimarka</option>
                                    <option value="pl" {{ old('nationality') == 'pl' ? 'selected' : '' }}>Polonya</option>
                                    <option value="ru" {{ old('nationality') == 'ru' ? 'selected' : '' }}>Rusya</option>
                                    <option value="ua" {{ old('nationality') == 'ua' ? 'selected' : '' }}>Ukrayna</option>
                                    <option value="ro" {{ old('nationality') == 'ro' ? 'selected' : '' }}>Romanya</option>
                                    <option value="bg" {{ old('nationality') == 'bg' ? 'selected' : '' }}>Bulgaristan</option>
                                    <option value="hu" {{ old('nationality') == 'hu' ? 'selected' : '' }}>Macaristan</option>
                                    <option value="cz" {{ old('nationality') == 'cz' ? 'selected' : '' }}>Çek Cumhuriyeti</option>
                                    <option value="sk" {{ old('nationality') == 'sk' ? 'selected' : '' }}>Slovakya</option>
                                    <option value="hr" {{ old('nationality') == 'hr' ? 'selected' : '' }}>Hırvatistan</option>
                                    <option value="ba" {{ old('nationality') == 'ba' ? 'selected' : '' }}>Bosna ve Hersek</option>
                                    <option value="me" {{ old('nationality') == 'me' ? 'selected' : '' }}>Karadağ</option>
                                    <option value="al" {{ old('nationality') == 'al' ? 'selected' : '' }}>Arnavutluk</option>
                                    <option value="mk" {{ old('nationality') == 'mk' ? 'selected' : '' }}>Makedonya</option>
                                    <option value="rs" {{ old('nationality') == 'rs' ? 'selected' : '' }}>Sırbistan</option>
                                    <option value="gr" {{ old('nationality') == 'gr' ? 'selected' : '' }}>Yunanistan</option>
                                    <option value="cy" {{ old('nationality') == 'cy' ? 'selected' : '' }}>Kıbrıs</option>
                                    <option value="il" {{ old('nationality') == 'il' ? 'selected' : '' }}>İsrail</option>
                                </select>
                                @error('nationality')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Meslek -->
                            <div>
                                <label for="occupation" class="block text-sm font-semibold text-gray-700 mb-2">
                                    Meslek
                                </label>
                                <select id="occupation"
                                        name="occupation"
                                        class="form-select @error('occupation') border-red-500 @enderror">
                                    <option value="">Seçiniz</option>
                                    @foreach($customerOccupations as $occupation)
                                        <option value="{{ $occupation->id }}" {{ old('occupation') == $occupation->id ? 'selected' : '' }}>
                                            {{ $occupation->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <!-- Notlar -->
                        <div>
                            <label for="notes" class="block text-sm font-semibold text-gray-700 mb-2">
                                Notlar
                            </label>
                            <textarea id="notes"
                                      name="notes"
                                      rows="4"
                                      class="form-textarea @error('notes') border-red-500 @enderror"
                                      placeholder="Müşteri hakkında özel notlar, tercihler, önemli bilgiler vb.">{{ old('notes') }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        </div>

                        <!-- Form Butonları -->
                        <div class="flex items-center justify-between pt-8 border-t border-gray-200 mt-8">
                            <!-- Tab Navigation Buttons -->
                            <div class="flex items-center gap-3">
                                <button type="button"
                                        @click="activeTab = activeTab === 'personal' ? 'personal' : activeTab === 'contact' ? 'personal' : activeTab === 'customer' ? 'contact' : activeTab === 'financial' ? 'customer' : 'financial'"
                                        x-show="activeTab !== 'personal'"
                                        class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center gap-2 cursor-pointer">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                                    </svg>
                                    Önceki
                                </button>

                                <button type="button"
                                        @click="activeTab = activeTab === 'personal' ? 'contact' : activeTab === 'contact' ? 'customer' : activeTab === 'customer' ? 'financial' : activeTab === 'financial' ? 'other' : 'other'"
                                        x-show="activeTab !== 'other'"
                                        class="px-4 py-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors duration-200 flex items-center gap-2 cursor-pointer">
                                    Sonraki
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                                    </svg>
                                </button>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center gap-4">
                                <a href="{{ route('customers.index') }}"
                                   class="px-6 py-3 bg-white/50 border border-gray-200 text-gray-700 rounded-xl hover:bg-white/80 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 font-medium">
                                    İptal
                                </a>
                                <button type="submit"
                                        class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 cursor-pointer">
                                    <svg class="h-5 w-5 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                    </svg>
                                    Müşteriyi Kaydet
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endsection
</x-backend-layout>
