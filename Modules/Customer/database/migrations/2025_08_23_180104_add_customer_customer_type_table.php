<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_customer_type', function (Blueprint $table) {
            $table->uuid('customer_id'); // customers.id ile eşleşecek
            $table->unsignedInteger('customer_type_id'); // customer_types.id ile eşleşecek
            $table->timestamps();

            // Composite primary key (ikisini birlikte benzersiz yapıyoruz)
            $table->primary(['customer_id', 'customer_type_id']);

            // Foreign keyler
            $table->foreign('customer_id')
                  ->references('id')->on('customers')
                  ->onDelete('cascade');

            $table->foreign('customer_type_id')
                  ->references('id')->on('customer_types')
                  ->onDelete('cascade');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_customer_type');
    }
};
