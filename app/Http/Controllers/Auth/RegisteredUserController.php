<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Company;
use App\Models\Package;
use App\Models\Order;
use App\Models\Subscription;
use App\Models\TransactionLog;
use App\Models\CompanyRole;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'company_name' => ['required', 'string', 'max:255'],
            'city_id' => ['required', 'integer', 'exists:iller,il_id'],
            'company_phone' => ['required', 'string', 'max:20'],
        ]);

        DB::transaction(function () use ($request, &$user) {
            // 1. Başlangıç paketini al (ID=1, ücretsiz paket)
            $freePackage = Package::find(1);
            if (!$freePackage) {
                throw new \Exception('Başlangıç paketi bulunamadı!');
            }

            // 2. Ücretsiz deneme süresi (30 gün)
            $trialDays = 30;
            $packageExpiresAt = Carbon::now()->addDays($trialDays);

            // 3. Önce kullanıcıyı oluştur (company_id olmadan)
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            // 4. Şirket oluştur (owner_id ile)
            $company = Company::create([
                'name' => $request->company_name,
                'slug' => Str::slug($request->company_name),
                'subdomain' => Str::slug($request->company_name),
                'city_id' => $request->city_id,
                'phone' => $request->company_phone,
                'owner_id' => $user->id,
                'is_active' => true,
                'settings' => [
                    'auto_backup' => false,
                    'email_notifications' => true,
                    'maintenance_mode' => false,
                    'public_registration' => false,
                    'default_language' => 'tr',
                    'timezone' => 'Europe/Istanbul',
                    'two_factor_auth' => false,
                    'session_timeout' => 120,
                    'strong_password_required' => true,
                    'max_login_attempts' => 5,
                    'api_enabled' => false,
                ]
            ]);

            // 5. Kullanıcıyı şirkete bağla
            $user->update(['company_id' => $company->id]);

                        // 6. Şirket için varsayılan rolleri oluştur
            $ownerRole = CompanyRole::createForCompany(
                $company->id,
                'Şirket Sahibi',
                'Şirketin sahibi - tüm yetkiler'
            );

            $managerRole = CompanyRole::createForCompany(
                $company->id,
                'Yönetici',
                'Şirket yöneticisi - çoğu yetki'
            );

            $agentRole = CompanyRole::createForCompany(
                $company->id,
                'Emlak Danışmanı',
                'Emlak danışmanı - temel yetkiler'
            );

            // 7. Şirket sahibine owner rolü ata
            $user->assignCompanyRole($company->id, $ownerRole);

            // 8. Şirket sahibi rolüne tüm temel izinleri ver
            $ownerPermissions = [
                'manage_users', 'manage_properties', 'manage_customers',
                'view_reports', 'export_reports', 'manage_settings',
                'manage_company', 'manage_roles', 'view_financial',
                'send_messages', 'send_emails','view_customers','create_customers','edit_customers','delete_customers','view_users','create_users','edit_users','delete_users'
            ];
            $ownerRole->syncPermissions($ownerPermissions);

            // 6. Ücretsiz deneme için sipariş oluştur
            $orderNumber = 'ORD-' . strtoupper(Str::random(8)) . '-' . date('Ymd');
            $order = Order::create([
                'company_id' => $company->id,
                'package_id' => $freePackage->id,
                'order_number' => $orderNumber,
                'subtotal' => 0.00,
                'discount_amount' => 0.00,
                'amount' => 0.00,
                'currency_code' => 'TRY',
                'status' => 'paid', // Ücretsiz olduğu için direkt paid
                'meta' => [
                    'payment_method' => 'free_trial',
                    'trial_days' => $trialDays,
                    'registration_ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]
            ]);

            // 7. Abonelik oluştur
            $subscription = Subscription::create([
                'company_id' => $company->id,
                'package_id' => $freePackage->id,
                'status' => 'active',
                'starts_at' => Carbon::now()->toDateString(),
                'ends_at' => $packageExpiresAt->toDateString(),
                'auto_renew' => false, // Ücretsiz deneme için auto-renew kapalı
            ]);

            // 8. Transaction log kaydet
            TransactionLog::create([
                'company_id' => $company->id,
                'order_id' => $order->id,
                'action' => 'company_registration',
                'status' => 'success',
                'data' => [
                    'company_name' => $company->name,
                    'package_name' => $freePackage->name,
                    'trial_days' => $trialDays,
                    'expires_at' => $packageExpiresAt->toISOString(),
                    'user_email' => $user->email,
                ],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // 9. Abonelik oluşturma log'u
            TransactionLog::create([
                'company_id' => $company->id,
                'order_id' => $order->id,
                'action' => 'subscription_created',
                'status' => 'success',
                'data' => [
                    'subscription_id' => $subscription->id,
                    'package_name' => $freePackage->name,
                    'starts_at' => $subscription->starts_at,
                    'ends_at' => $subscription->ends_at,
                ],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
        });

        event(new Registered($user));

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}
