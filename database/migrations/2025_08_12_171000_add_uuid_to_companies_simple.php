<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Şimdilik companies tablosunu integer ID ile bırakıyoruz
        // UUID'ye geçiş daha sonra yapılabilir

        // Sadece eksik sosyal medya kolonlarını ekle
        if (Schema::hasTable('companies')) {
            Schema::table('companies', function (Blueprint $table) {
                if (!Schema::hasColumn('companies', 'facebook')) {
                    $table->string('facebook')->nullable()->after('email');
                }
                if (!Schema::hasColumn('companies', 'instagram')) {
                    $table->string('instagram')->nullable()->after('facebook');
                }
                if (!Schema::hasColumn('companies', 'twitter')) {
                    $table->string('twitter')->nullable()->after('instagram');
                }
                if (!Schema::hasColumn('companies', 'linkedin')) {
                    $table->string('linkedin')->nullable()->after('twitter');
                }
                if (!Schema::hasColumn('companies', 'youtube')) {
                    $table->string('youtube')->nullable()->after('linkedin');
                }
                if (!Schema::hasColumn('companies', 'tiktok')) {
                    $table->string('tiktok')->nullable()->after('youtube');
                }
                if (!Schema::hasColumn('companies', 'pinterest')) {
                    $table->string('pinterest')->nullable()->after('tiktok');
                }
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('companies')) {
            Schema::table('companies', function (Blueprint $table) {
                $columns = ['facebook', 'instagram', 'twitter', 'linkedin', 'youtube', 'tiktok', 'pinterest'];
                foreach ($columns as $column) {
                    if (Schema::hasColumn('companies', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }
};
