<!-- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>ı -->
<div x-show="activeTab === 'documents'"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-x-8"
     x-transition:enter-end="opacity-100 transform translate-x-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-x-0"
     x-transition:leave-end="opacity-0 transform -translate-x-8"
     class="space-y-8">

    <!-- <PERSON><PERSON><PERSON> -->
    <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full mb-4">
            <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
            </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Belgeler & Sözleşmeler</h2>
        <p class="text-gray-600">Portföyünüzle ilgili belgeleri ve sözleşmeleri ekleyin</p>
    </div>

    <!-- Tapu Belgeleri -->
    <div class="bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 rounded-2xl p-6">
        <h3 class="text-lg font-semibold text-emerald-800 mb-6 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3-6h.008v.008H15V12zm0 3h.008v.008H15V15zm0 3h.008v.008H15V18zM6.75 6.75h.008v.008H6.75V6.75zm0 3h.008v.008H6.75V9.75zm0 3h.008v.008H6.75V12.75z" />
            </svg>
            Tapu & Resmi Belgeler
            <span class="text-sm font-normal text-emerald-600">(Opsiyonel)</span>
        </h3>

        <div x-data="documentUploader('deed')" class="space-y-4">
            <!-- Tapu Yükleme Alanı -->
            <div @drop="handleDrop($event)" 
                 @dragover.prevent 
                 @dragenter.prevent
                 :class="isDragging ? 'border-emerald-500 bg-emerald-100' : 'border-gray-300'"
                 class="border-2 border-dashed rounded-xl p-6 text-center transition-all duration-200 cursor-pointer hover:border-emerald-400 hover:bg-emerald-50"
                 @click="$refs.deedInput.click()">
                
                <input type="file" 
                       x-ref="deedInput"
                       @change="handleFileSelect($event)"
                       multiple 
                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                       class="hidden">
                
                <div class="flex flex-col items-center">
                    <svg class="w-10 h-10 text-emerald-400 mb-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 18H3.75a1.5 1.5 0 0 1-1.5-1.5V4.5a1.5 1.5 0 0 1 1.5-1.5h5.25a1.5 1.5 0 0 1 1.5 1.5v10.5a1.5 1.5 0 0 0 1.5 1.5h3.75a1.5 1.5 0 0 1 1.5 1.5v2.25a1.5 1.5 0 0 1-1.5 1.5Z" />
                    </svg>
                    <h4 class="text-md font-medium text-gray-700 mb-1">Tapu belgelerini yükleyin</h4>
                    <p class="text-sm text-gray-500 mb-2">PDF, DOC, JPG, PNG • Maksimum 10MB</p>
                    <div class="text-xs text-gray-400">
                        Tapu senedi, imar durumu, iskan ruhsatı vb.
                    </div>
                </div>
            </div>

            <!-- Yüklenen Tapu Belgeleri -->
            <div x-show="documents.length > 0" class="space-y-3">
                <h4 class="text-sm font-semibold text-gray-700">Yüklenen Belgeler</h4>
                <div class="space-y-2">
                    <template x-for="(doc, index) in documents" :key="index">
                        <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 18H3.75a1.5 1.5 0 0 1-1.5-1.5V4.5a1.5 1.5 0 0 1 1.5-1.5h5.25a1.5 1.5 0 0 1 1.5 1.5v10.5a1.5 1.5 0 0 0 1.5 1.5h3.75a1.5 1.5 0 0 1 1.5 1.5v2.25a1.5 1.5 0 0 1-1.5 1.5Z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900" x-text="doc.name"></p>
                                    <p class="text-xs text-gray-500" x-text="formatFileSize(doc.size)"></p>
                                </div>
                            </div>
                            <button type="button"
                                    @click="removeDocument(index)"
                                    class="text-red-500 hover:text-red-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                                </svg>
                            </button>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- Enerji Kimlik Belgesi -->
    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-6">
        <h3 class="text-lg font-semibold text-yellow-800 mb-6 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
            </svg>
            Enerji Kimlik Belgesi
            <span class="text-sm font-normal text-yellow-600">(Opsiyonel)</span>
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Enerji Sınıfı -->
            <div>
                <label for="energy_class" class="block text-sm font-medium text-gray-700 mb-2">
                    Enerji Sınıfı
                </label>
                <select id="energy_class"
                        name="energy_class"
                        class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-all duration-200">
                    <option value="">Seçin</option>
                    <option value="A+">A+ (En Verimli)</option>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <option value="D">D</option>
                    <option value="E">E</option>
                    <option value="F">F</option>
                    <option value="G">G (En Az Verimli)</option>
                </select>
            </div>

            <!-- EKB Dosyası -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    EKB Belgesi
                </label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-yellow-400 hover:bg-yellow-50 transition-all duration-200 cursor-pointer"
                     onclick="document.getElementById('energy_certificate').click()">
                    <input type="file" 
                           id="energy_certificate"
                           name="energy_certificate"
                           accept=".pdf,.jpg,.jpeg,.png"
                           class="hidden">
                    
                    <svg class="w-8 h-8 text-yellow-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                    </svg>
                    <p class="text-sm text-gray-600">EKB belgesi yükleyin</p>
                    <p class="text-xs text-gray-400">PDF, JPG, PNG</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Diğer Belgeler -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6">
        <h3 class="text-lg font-semibold text-blue-800 mb-6 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25H11.69l-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v6.75" />
            </svg>
            Diğer Belgeler
            <span class="text-sm font-normal text-blue-600">(Opsiyonel)</span>
        </h3>

        <div x-data="documentUploader('other')" class="space-y-4">
            <!-- Diğer Belgeler Yükleme -->
            <div @drop="handleDrop($event)" 
                 @dragover.prevent 
                 @dragenter.prevent
                 :class="isDragging ? 'border-blue-500 bg-blue-100' : 'border-gray-300'"
                 class="border-2 border-dashed rounded-xl p-6 text-center transition-all duration-200 cursor-pointer hover:border-blue-400 hover:bg-blue-50"
                 @click="$refs.otherInput.click()">
                
                <input type="file" 
                       x-ref="otherInput"
                       @change="handleFileSelect($event)"
                       multiple 
                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx"
                       class="hidden">
                
                <div class="flex flex-col items-center">
                    <svg class="w-10 h-10 text-blue-400 mb-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25H11.69l-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v6.75" />
                    </svg>
                    <h4 class="text-md font-medium text-gray-700 mb-1">Diğer belgeleri yükleyin</h4>
                    <p class="text-sm text-gray-500 mb-2">PDF, DOC, XLS, JPG, PNG • Maksimum 10MB</p>
                    <div class="text-xs text-gray-400">
                        Aidat makbuzu, sigorta poliçesi, plan, proje vb.
                    </div>
                </div>
            </div>

            <!-- Yüklenen Diğer Belgeler -->
            <div x-show="documents.length > 0" class="space-y-3">
                <h4 class="text-sm font-semibold text-gray-700">Yüklenen Belgeler</h4>
                <div class="space-y-2">
                    <template x-for="(doc, index) in documents" :key="index">
                        <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 18H3.75a1.5 1.5 0 0 1-1.5-1.5V4.5a1.5 1.5 0 0 1 1.5-1.5h5.25a1.5 1.5 0 0 1 1.5 1.5v10.5a1.5 1.5 0 0 0 1.5 1.5h3.75a1.5 1.5 0 0 1 1.5 1.5v2.25a1.5 1.5 0 0 1-1.5 1.5Z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900" x-text="doc.name"></p>
                                    <p class="text-xs text-gray-500" x-text="formatFileSize(doc.size)"></p>
                                </div>
                            </div>
                            <button type="button"
                                    @click="removeDocument(index)"
                                    class="text-red-500 hover:text-red-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                                </svg>
                            </button>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- Belge Özeti -->
    <div class="mt-8 p-6 bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-200 rounded-2xl">
        <h3 class="text-lg font-semibold text-emerald-800 mb-4 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3-6h.008v.008H15V12zm0 3h.008v.008H15V15zm0 3h.008v.008H15V18zM6.75 6.75h.008v.008H6.75V6.75zm0 3h.008v.008H6.75V9.75zm0 3h.008v.008H6.75V12.75z" />
            </svg>
            Belge Özeti
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-white rounded-xl border border-emerald-100">
                <p class="text-sm text-gray-600 mb-1">Tapu Belgeleri</p>
                <p class="text-xl font-bold text-emerald-600" id="deed-count">0</p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-emerald-100">
                <p class="text-sm text-gray-600 mb-1">Enerji Sınıfı</p>
                <p class="text-lg font-semibold text-gray-800" id="energy-class-display">Belirtilmedi</p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-emerald-100">
                <p class="text-sm text-gray-600 mb-1">Diğer Belgeler</p>
                <p class="text-xl font-bold text-emerald-600" id="other-docs-count">0</p>
            </div>
        </div>
    </div>
</div>

<script>
function documentUploader(type) {
    return {
        documents: [],
        isDragging: false,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        
        handleDrop(e) {
            e.preventDefault();
            this.isDragging = false;
            const files = Array.from(e.dataTransfer.files);
            this.processFiles(files);
        },
        
        handleFileSelect(e) {
            const files = Array.from(e.target.files);
            this.processFiles(files);
        },
        
        processFiles(files) {
            files.forEach(file => {
                if (file.size > this.maxFileSize) {
                    alert(`${file.name} dosyası çok büyük. Maksimum 10MB olmalıdır.`);
                    return;
                }
                
                this.documents.push({
                    file: file,
                    name: file.name,
                    size: file.size,
                    type: type
                });
            });
            
            this.updateCounts();
        },
        
        removeDocument(index) {
            this.documents.splice(index, 1);
            this.updateCounts();
        },
        
        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        updateCounts() {
            if (type === 'deed') {
                document.getElementById('deed-count').textContent = this.documents.length;
            } else if (type === 'other') {
                document.getElementById('other-docs-count').textContent = this.documents.length;
            }
        }
    }
}

// Enerji sınıfı takibi
document.addEventListener('DOMContentLoaded', function() {
    const energyClassSelect = document.getElementById('energy_class');
    const energyClassDisplay = document.getElementById('energy-class-display');
    
    energyClassSelect.addEventListener('change', function() {
        if (this.value) {
            energyClassDisplay.textContent = this.value;
            energyClassDisplay.className = 'text-lg font-semibold text-green-600';
        } else {
            energyClassDisplay.textContent = 'Belirtilmedi';
            energyClassDisplay.className = 'text-lg font-semibold text-gray-800';
        }
    });
});
</script>
