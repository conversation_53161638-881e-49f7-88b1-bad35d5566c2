<!DOCTYPE html>
<html lang="tr" class="h-full bg-gray-50">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="icon" href="{{ asset('assets/backend/images/favicon.png') }}" type="image/x-icon">

    <title>{{ isset($title) ? $title . ' - EmlakSis' : 'EmlakSis - Emlak Yönetim Sistemi' }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Build edilmiş CSS'ler -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @php
        $themeConfig = auth()->user()
            ? auth()->user()->getThemeConfig()
            : [
                'color' => 'blue',
                'sidebar_type' => 'light',
                'sidebar_collapsed' => false,
                'primary_color' => '#3b82f6',
                'primary_dark' => '#1e40af',
            ];
    @endphp

    <style>
        :root {
            --primary-color: {{ $themeConfig['primary_color'] }};
            --primary-dark: {{ $themeConfig['primary_dark'] }};
        }

        /* Custom search modal styles */
        .search-modal {
            backdrop-filter: blur(8px);
            background: rgba(0, 0, 0, 0.5);
        }

        .search-modal-content {
            animation: modalSlideIn 0.2s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }

            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        /* Smooth dropdown animations */
        .dropdown-enter {
            transition: all 0.15s ease-out;
        }

        /* Modern blob animations */
        @keyframes blob {
            0% {
                transform: translate(0px, 0px) scale(1);
            }

            33% {
                transform: translate(30px, -50px) scale(1.1);
            }

            66% {
                transform: translate(-20px, 20px) scale(0.9);
            }

            100% {
                transform: translate(0px, 0px) scale(1);
            }
        }

        .animate-blob {
            animation: blob 7s infinite;
        }

        .animation-delay-2000 {
            animation-delay: 2s;
        }

        .animation-delay-4000 {
            animation-delay: 4s;
        }

        /* Glassmorphism effects */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        /* Number counter animation */
        @keyframes countUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-count-up {
            animation: countUp 0.8s ease-out;
        }

        /* Floating animation */
        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        /* Pulse glow effect */
        @keyframes pulseGlow {

            0%,
            100% {
                box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
            }

            50% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
            }
        }

        .animate-pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite;
        }

        /* Custom dropdown styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            position: absolute;
            right: 0;
            top: 100%;
            z-index: 50;
            min-width: 250px;
            margin-top: 0.25rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: opacity 200ms ease, transform 200ms ease, visibility 200ms ease;
            pointer-events: none;
            display: none !important;
            /* Başlangıçta tamamen gizli */
        }

        /* JS ile yönetilen açık durum */
        .dropdown.open .dropdown-content {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            pointer-events: auto;
            display: block !important;
        }
    </style>

    @stack('styles')
    @livewireStyles
</head>

<body class="font-sans antialiased bg-gray-50 h-full">
    <div class="flex flex-col h-full">
        <!-- Ultra Modern Navbar -->
        <header
            class="bg-white/90 backdrop-blur-xl supports-[backdrop-filter]:bg-white/70 shadow-xl border-b border-white/20 sticky top-0 z-50 transition-all duration-300">
            <div class="max-w-7xl mx-auto px-6">
                <div class="flex justify-between items-center h-18">
                    <!-- Logo & Main Menu -->
                    <div class="flex items-center space-x-12">
                        <!-- Enhanced Logo -->
                        <a href="{{ route('dashboard') }}" class="group flex items-center space-x-3 cursor-pointer">

                            <div class="flex flex-col">
                                <span
                                    class="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">EmlakSis</span>
                                <span class="text-xs text-gray-500 font-medium -mt-1">Real Estate CRM</span>
                            </div>
                        </a>

                        <!-- Modern Navigation Menu -->
                        <nav class="hidden lg:flex items-center space-x-2">
                            <!-- Müşteriler Dropdown -->
                            <div class="dropdown" data-dropdown>
                                <button type="button"
                                    class="group relative flex items-center px-2 py-2 text-sm font-semibold text-gray-700 hover:text-blue-600 hover:bg-blue-50/80 rounded-2xl transition-all duration-300 cursor-pointer {{ request()->routeIs('customers*') ? 'bg-blue-100 text-blue-700 shadow-md' : '' }}"
                                    data-dropdown-trigger aria-haspopup="true" aria-expanded="false">
                                    <div
                                        class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl mr-3 group-hover:scale-110 transition-transform duration-300">
                                        <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24"
                                            stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                                        </svg>
                                    </div>
                                    <span class="mr-2">Müşteriler</span>
                                    <svg class="h-4 w-4 group-hover:rotate-180 transition-transform duration-300"
                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                    </svg>
                                    <!-- Active indicator -->
                                    @if (request()->routeIs('customers*'))
                                        <div
                                            class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-600 rounded-full">
                                        </div>
                                    @endif
                                </button>
                                <div
                                    class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 py-3 min-w-[250px]">
                                    <div class="px-4 py-2 border-b border-gray-100">
                                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Müşteri
                                            Yönetimi</p>
                                    </div>
                                    <a href="{{ route('customers.index') }}"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Müşteri Listesi</div>
                                            <div class="text-xs text-gray-500">Tüm müşterileri görüntüle</div>
                                        </div>
                                    </a>
                                    <a href="{{ route('company.create') }}"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M12 4.5v15m7.5-7.5h-15" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Yeni Müşteri</div>
                                            <div class="text-xs text-gray-500">Müşteri ekle</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Kategoriler</div>
                                            <div class="text-xs text-gray-500">Müşteri Grupları</div>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <!-- Portföy Dropdown -->
                            <div class="dropdown" data-dropdown>
                                <button type="button"
                                    class="group relative flex items-center px-2 py-2 text-sm font-semibold text-gray-700 hover:text-emerald-600 hover:bg-emerald-50/80 rounded-2xl transition-all duration-300 cursor-pointer {{ request()->routeIs('portfolio*') ? 'bg-emerald-100 text-emerald-700 shadow-md' : '' }}"
                                    data-dropdown-trigger aria-haspopup="true" aria-expanded="false">
                                    <div
                                        class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl mr-3 group-hover:scale-110 transition-transform duration-300">
                                        <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24"
                                            stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                        </svg>
                                    </div>
                                    <span class="mr-2">Portföy</span>
                                    <svg class="h-4 w-4 group-hover:rotate-180 transition-transform duration-300"
                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                    </svg>
                                    <!-- Active indicator -->
                                    @if (request()->routeIs('portfolio*'))
                                        <div
                                            class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-emerald-600 rounded-full">
                                        </div>
                                    @endif
                                </button>
                                <div
                                    class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 py-3 min-w-[250px]">
                                    <div class="px-4 py-2 border-b border-gray-100">
                                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Emlak
                                            Portföyü</p>
                                    </div>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-emerald-50 hover:text-emerald-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-emerald-100 text-emerald-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Emlak Listesi</div>
                                            <div class="text-xs text-gray-500">Tüm emlakları görüntüle</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M12 4.5v15m7.5-7.5h-15" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Yeni Emlak</div>
                                            <div class="text-xs text-gray-500">Emlak ilanı ekle</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-orange-100 text-orange-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Kategoriler</div>
                                            <div class="text-xs text-gray-500">Emlak kategorileri</div>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <!-- Raporlar Dropdown -->
                            <div class="dropdown" data-dropdown>
                                <button type="button"
                                    class="group relative flex items-center px-2 py-2 text-sm font-semibold text-gray-700 hover:text-purple-600 hover:bg-purple-50/80 rounded-2xl transition-all duration-300 cursor-pointer {{ request()->routeIs('reports*') ? 'bg-purple-100 text-purple-700 shadow-md' : '' }}"
                                    data-dropdown-trigger aria-haspopup="true" aria-expanded="false">
                                    <div
                                        class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl mr-3 group-hover:scale-110 transition-transform duration-300">
                                        <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24"
                                            stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                                        </svg>
                                    </div>
                                    <span class="mr-2">Raporlar</span>
                                    <svg class="h-4 w-4 group-hover:rotate-180 transition-transform duration-300"
                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                    </svg>
                                    <!-- Active indicator -->
                                    @if (request()->routeIs('reports*'))
                                        <div
                                            class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-purple-600 rounded-full">
                                        </div>
                                    @endif
                                </button>
                                <div
                                    class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 py-3 min-w-[250px]">
                                    <div class="px-4 py-2 border-b border-gray-100">
                                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                                            Raporlama & Analiz</p>
                                    </div>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Satış Raporları</div>
                                            <div class="text-xs text-gray-500">Aylık ve yıllık satış analizi</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Müşteri Analizi</div>
                                            <div class="text-xs text-gray-500">Müşteri davranış raporları</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Portföy Performansı</div>
                                            <div class="text-xs text-gray-500">Emlak portföy analizi</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-orange-100 text-orange-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Rapor İndir</div>
                                            <div class="text-xs text-gray-500">Excel ve PDF formatları</div>
                                        </div>
                                    </a>

                                    <div class="border-t border-gray-100 my-2"></div>

                                    <!-- Rapor Oluştur -->
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-sky-50 hover:text-sky-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-sky-100 text-sky-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5M5.25 19.5a2.25 2.25 0 01-2.25-2.25V6.75A2.25 2.25 0 015.25 4.5h10.5a2.25 2.25 0 012.25 2.25v10.5a2.25 2.25 0 01-2.25 2.25H5.25z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Rapor Oluştur</div>
                                            <div class="text-xs text-gray-500">Müşteriye özel PDF raporu</div>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <!-- CRM Dropdown -->
                            <div class="dropdown" data-dropdown>
                                <button type="button"
                                    class="group relative flex items-center px-2 py-2 text-sm font-semibold text-gray-700 hover:text-indigo-600 hover:bg-indigo-50/80 rounded-2xl transition-all duration-300 cursor-pointer {{ request()->routeIs('crm*') ? 'bg-indigo-100 text-indigo-700 shadow-md' : '' }}"
                                    data-dropdown-trigger aria-haspopup="true" aria-expanded="false">
                                    <div
                                        class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl mr-3 group-hover:scale-110 transition-transform duration-300">
                                        <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24"
                                            stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
                                        </svg>
                                    </div>
                                    <span class="mr-2">CRM</span>
                                    <svg class="h-4 w-4 group-hover:rotate-180 transition-transform duration-300"
                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                    </svg>
                                    <!-- Active indicator -->
                                    @if (request()->routeIs('crm*'))
                                        <div
                                            class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-indigo-600 rounded-full">
                                        </div>
                                    @endif
                                </button>
                                <div
                                    class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 py-3 min-w-[250px]">
                                    <div class="px-4 py-2 border-b border-gray-100">
                                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Müşteri
                                            İlişkileri</p>
                                    </div>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-indigo-100 text-indigo-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Potansiyel Müşteriler</div>
                                            <div class="text-xs text-gray-500">Lead yönetimi ve takibi</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Arama Kayıtları</div>
                                            <div class="text-xs text-gray-500">Telefon görüşme geçmişi</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">E-posta Kampanyaları</div>
                                            <div class="text-xs text-gray-500">Toplu mail gönderimi</div>
                                        </div>
                                    </a>
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Satış Süreci</div>
                                            <div class="text-xs text-gray-500">Pipeline yönetimi</div>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <!-- Diğer Menüler Dropdown -->
                            <div class="dropdown" data-dropdown>
                                <button type="button"
                                    class="group relative flex items-center px-2 py-2 text-sm font-semibold text-gray-700 hover:text-slate-600 hover:bg-slate-50/80 rounded-2xl transition-all duration-300 cursor-pointer {{ request()->routeIs(['requests*', 'contracts*', 'rent*', 'marketing*', 'finance*', 'documents*']) ? 'bg-slate-100 text-slate-700 shadow-md' : '' }}"
                                    data-dropdown-trigger aria-haspopup="true" aria-expanded="false">
                                    <div
                                        class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-slate-500 to-gray-600 rounded-xl mr-3 group-hover:scale-110 transition-transform duration-300">
                                        <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24"
                                            stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5" />
                                        </svg>
                                    </div>
                                    <span class="mr-2">Diğer</span>
                                    <svg class="h-4 w-4 group-hover:rotate-180 transition-transform duration-300"
                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                    </svg>
                                    <!-- Active indicator -->
                                    @if (request()->routeIs(['requests*', 'contracts*', 'rent*', 'marketing*', 'finance*', 'documents*']))
                                        <div
                                            class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-slate-600 rounded-full">
                                        </div>
                                    @endif
                                </button>
                                <div
                                    class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 py-3 min-w-[250px]">
                                    <div class="px-4 py-2 border-b border-gray-100">
                                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Ek
                                            Modüller</p>
                                    </div>

                                    <!-- Talep Havuzu -->
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-cyan-50 hover:text-cyan-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-cyan-100 text-cyan-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Talep Havuzu</div>
                                            <div class="text-xs text-gray-500">Müşteri talepleri yönetimi</div>
                                        </div>
                                    </a>

                                    <!-- Sözleşmeler -->
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-teal-100 text-teal-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Sözleşmeler</div>
                                            <div class="text-xs text-gray-500">Sözleşme yönetimi</div>
                                        </div>
                                    </a>

                                    <!-- Kira Takibi -->
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-orange-100 text-orange-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H6.75a.75.75 0 01.75.75v.75h3.75V6a.75.75 0 01.75-.75h3.75a.75.75 0 01.75.75v.75H21a.75.75 0 01.75.75v5.25a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V7.5a.75.75 0 01.75-.75h.75m0 0V6a.75.75 0 01.75-.75h.75a.75.75 0 01.75.75v.75h.75V6a.75.75 0 01.75-.75H12a.75.75 0 01.75.75v.75h.75V6a.75.75 0 01.75-.75h.75a.75.75 0 01.75.75v.75H21V6a.75.75 0 00-.75-.75H3.75z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Kira Takibi</div>
                                            <div class="text-xs text-gray-500">Kira ödemeleri takibi</div>
                                        </div>
                                    </a>

                                    <!-- Marketing -->
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-pink-50 hover:text-pink-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-pink-100 text-pink-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 110-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 01-1.44-4.282m3.102.069a18.03 18.03 0 01-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 018.835 2.535M10.34 6.66a23.847 23.847 0 008.835-2.535m0 0A23.74 23.74 0 0018.795 3m.38 1.125a23.91 23.91 0 711.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 001.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 010 3.46" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Marketing</div>
                                            <div class="text-xs text-gray-500">Pazarlama kampanyaları</div>
                                        </div>
                                    </a>

                                    <!-- Gelir/Gider Takibi -->
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-emerald-50 hover:text-emerald-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-emerald-100 text-emerald-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Gelir/Gider Takibi</div>
                                            <div class="text-xs text-gray-500">Mali işlemler ve raporlama</div>
                                        </div>
                                    </a>

                                    <!-- Döküman Yönetimi -->
                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-indigo-100 text-indigo-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25M9 16.5v.75m3-3V15M15 12v.75m-6-3.75v.75m6 0v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Döküman Yönetimi</div>
                                            <div class="text-xs text-gray-500">Belge arşivi ve dosya yönetimi</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </nav>
                    </div>

                    <!-- Right Side: Modern Action Bar -->
                    <div class="flex items-center space-x-2">
                        <!-- Enhanced Search Button -->
                        <div class="relative">
                            <button type="button" onclick="openSearchModal()"
                                class="group relative p-3 text-gray-500 hover:text-blue-600 hover:bg-blue-50/80 rounded-2xl transition-all duration-300 cursor-pointer"
                                title="Ara (⌘K)">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                                <svg class="relative h-5 w-5 group-hover:scale-110 transition-transform duration-300"
                                    fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 15.803a7.5 7.5 0 0010.607 0z" />
                                </svg>
                                <!-- Keyboard shortcut indicator -->
                                <div
                                    class="absolute -top-1 -right-1 px-1.5 py-0.5 bg-gray-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    ⌘K
                                </div>
                            </button>
                        </div>

                        <!-- Enhanced Smart Matching Button -->
                        <div class="dropdown" data-dropdown>
                            <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false"
                                class="group relative p-3 text-gray-500 hover:text-purple-600 hover:bg-purple-50/80 rounded-2xl transition-all duration-300 cursor-pointer"
                                title="Akıllı Eşleşme">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                                <svg class="relative h-5 w-5 group-hover:scale-110 transition-transform duration-300"
                                    fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                                </svg>
                                <!-- Match notification badge -->
                                <div
                                    class="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full border-2 border-white shadow-lg">
                                    <span class="text-xs font-bold text-white">7</span>
                                </div>
                                <div
                                    class="absolute -top-1 -right-1 w-5 h-5 bg-purple-500 rounded-full animate-ping opacity-75">
                                </div>
                            </button>
                            <div
                                class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 w-96">
                                <div class="p-6 border-b border-gray-100">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-bold text-gray-900">Akıllı Eşleşme</h3>
                                        <div class="flex items-center gap-2 px-3 py-1 bg-purple-100 rounded-full">
                                            <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                                            <span class="text-xs font-semibold text-purple-700">7 eşleşme</span>
                                        </div>
                                    </div>
                                    <p class="text-xs text-gray-600 mt-2">Talep havuzundaki ilanlarla portföyünüzdeki
                                        emlaklar arasında bulunan eşleşmeler</p>
                                </div>
                                <div class="max-h-96 overflow-y-auto">
                                    <!-- Match Items -->
                                    <div class="p-4 space-y-3">
                                        <!-- High Match -->
                                        <div
                                            class="group flex items-start gap-3 p-4 bg-green-50/50 hover:bg-green-50 rounded-xl transition-colors cursor-pointer border border-green-100">
                                            <div
                                                class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <svg class="w-6 h-6 text-green-600" fill="none"
                                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center justify-between mb-2">
                                                    <p class="text-sm font-semibold text-gray-900">Bahçelievler Villa
                                                        Talebi</p>
                                                    <div
                                                        class="flex items-center gap-1 px-2 py-1 bg-green-100 rounded-full">
                                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                                        <span class="text-xs font-bold text-green-700">%94</span>
                                                    </div>
                                                </div>
                                                <p class="text-xs text-gray-600 mb-2">Müşteri: Ahmet Yılmaz • 4+1
                                                    Villa, 250m²</p>
                                                <div class="flex items-center gap-2">
                                                    <span class="text-xs text-gray-500">Eşleşen emlak:</span>
                                                    <span class="text-xs font-medium text-green-700">Bahçelievler Lüks
                                                        Villa</span>
                                                </div>
                                                <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                                    <span>📍 Konum: %98</span>
                                                    <span>💰 Fiyat: %89</span>
                                                    <span>🏠 Özellik: %95</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Medium Match -->
                                        <div
                                            class="group flex items-start gap-3 p-4 bg-yellow-50/50 hover:bg-yellow-50 rounded-xl transition-colors cursor-pointer border border-yellow-100">
                                            <div
                                                class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <svg class="w-6 h-6 text-yellow-600" fill="none"
                                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center justify-between mb-2">
                                                    <p class="text-sm font-semibold text-gray-900">Kadıköy Daire
                                                        Arayışı</p>
                                                    <div
                                                        class="flex items-center gap-1 px-2 py-1 bg-yellow-100 rounded-full">
                                                        <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                                        <span class="text-xs font-bold text-yellow-700">%76</span>
                                                    </div>
                                                </div>
                                                <p class="text-xs text-gray-600 mb-2">Müşteri: Elif Demir • 2+1 Daire,
                                                    120m²</p>
                                                <div class="flex items-center gap-2">
                                                    <span class="text-xs text-gray-500">Eşleşen emlak:</span>
                                                    <span class="text-xs font-medium text-yellow-700">Kadıköy Modern
                                                        Daire</span>
                                                </div>
                                                <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                                    <span>📍 Konum: %85</span>
                                                    <span>💰 Fiyat: %72</span>
                                                    <span>🏠 Özellik: %71</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Low Match -->
                                        <div
                                            class="group flex items-start gap-3 p-4 bg-blue-50/50 hover:bg-blue-50 rounded-xl transition-colors cursor-pointer border border-blue-100">
                                            <div
                                                class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24"
                                                    stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center justify-between mb-2">
                                                    <p class="text-sm font-semibold text-gray-900">Beşiktaş Ofis Talebi
                                                    </p>
                                                    <div
                                                        class="flex items-center gap-1 px-2 py-1 bg-blue-100 rounded-full">
                                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                        <span class="text-xs font-bold text-blue-700">%63</span>
                                                    </div>
                                                </div>
                                                <p class="text-xs text-gray-600 mb-2">Müşteri: Mehmet Kaya • Ofis, 80m²
                                                </p>
                                                <div class="flex items-center gap-2">
                                                    <span class="text-xs text-gray-500">Eşleşen emlak:</span>
                                                    <span class="text-xs font-medium text-blue-700">Beşiktaş İş
                                                        Merkezi</span>
                                                </div>
                                                <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                                    <span>📍 Konum: %78</span>
                                                    <span>💰 Fiyat: %55</span>
                                                    <span>🏠 Özellik: %56</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-t border-gray-100">
                                    <div class="grid grid-cols-2 gap-3">
                                        <button
                                            class="py-2 px-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] cursor-pointer text-sm">
                                            Tüm Eşleşmeler
                                        </button>
                                        <button
                                            class="py-2 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] cursor-pointer text-sm">
                                            Ayarlar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Calendar/Tasks Button -->
                        <div class="dropdown" data-dropdown>
                            <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false"
                                class="group relative p-3 text-gray-500 hover:text-green-600 hover:bg-green-50/80 rounded-2xl transition-all duration-300 cursor-pointer"
                                title="Görev Takibi & Ajanda">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                                <svg class="relative h-5 w-5 group-hover:scale-110 transition-transform duration-300"
                                    fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5m-9-6h.008v.008H12V12.75z" />
                                </svg>
                                <!-- Task notification badge -->
                                <div
                                    class="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full border-2 border-white shadow-lg">
                                    <span class="text-xs font-bold text-white">5</span>
                                </div>
                            </button>
                            <div
                                class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 w-80">
                                <div class="p-6 border-b border-gray-100">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-bold text-gray-900">Görev Takibi</h3>
                                        <div class="flex items-center gap-2 px-3 py-1 bg-green-100 rounded-full">
                                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                            <span class="text-xs font-semibold text-green-700">5 aktif</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="max-h-96 overflow-y-auto">
                                    <!-- Quick Actions -->
                                    <div class="p-4 border-b border-gray-100">
                                        <div class="grid grid-cols-2 gap-3">
                                            <a href="#"
                                                class="group flex flex-col items-center p-3 bg-blue-50/50 hover:bg-blue-50 rounded-xl transition-colors cursor-pointer">
                                                <div
                                                    class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform duration-200">
                                                    <svg class="w-5 h-5 text-blue-600" fill="none"
                                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M12 4.5v15m7.5-7.5h-15" />
                                                    </svg>
                                                </div>
                                                <span class="text-xs font-semibold text-gray-700">Yeni Görev</span>
                                            </a>
                                            <a href="#"
                                                class="group flex flex-col items-center p-3 bg-purple-50/50 hover:bg-purple-50 rounded-xl transition-colors cursor-pointer">
                                                <div
                                                    class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform duration-200">
                                                    <svg class="w-5 h-5 text-purple-600" fill="none"
                                                        viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                                                    </svg>
                                                </div>
                                                <span class="text-xs font-semibold text-gray-700">Ajanda</span>
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Today's Tasks -->
                                    <div class="p-4 space-y-3">
                                        <h4 class="text-sm font-semibold text-gray-900 mb-3">Bugünün Görevleri</h4>

                                        <div
                                            class="group flex items-start gap-3 p-3 bg-red-50/50 hover:bg-red-50 rounded-xl transition-colors cursor-pointer">
                                            <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-semibold text-gray-900">Müşteri ziyareti</p>
                                                <p class="text-xs text-gray-600 mt-1">Ahmet Bey - Villa görüşmesi</p>
                                                <p class="text-xs text-red-600 font-medium mt-1">14:00 - Yüksek Öncelik
                                                </p>
                                            </div>
                                        </div>

                                        <div
                                            class="group flex items-start gap-3 p-3 bg-yellow-50/50 hover:bg-yellow-50 rounded-xl transition-colors cursor-pointer">
                                            <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-semibold text-gray-900">Sözleşme hazırlığı</p>
                                                <p class="text-xs text-gray-600 mt-1">Bahçelievler daire satışı</p>
                                                <p class="text-xs text-yellow-600 font-medium mt-1">16:30 - Orta
                                                    Öncelik</p>
                                            </div>
                                        </div>

                                        <div
                                            class="group flex items-start gap-3 p-3 bg-green-50/50 hover:bg-green-50 rounded-xl transition-colors cursor-pointer">
                                            <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-semibold text-gray-900">Emlak fotoğrafları</p>
                                                <p class="text-xs text-gray-600 mt-1">Yeni ilan için çekim</p>
                                                <p class="text-xs text-green-600 font-medium mt-1">18:00 - Düşük
                                                    Öncelik</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-t border-gray-100">
                                    <button
                                        class="w-full py-2 px-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] cursor-pointer">
                                        Tüm Görevleri Gör
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Notifications -->
                        <div class="dropdown" data-dropdown>
                            <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false"
                                class="group relative p-3 text-gray-500 hover:text-orange-600 hover:bg-orange-50/80 rounded-2xl transition-all duration-300 cursor-pointer">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                                <svg class="relative h-5 w-5 group-hover:scale-110 transition-transform duration-300"
                                    fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                                </svg>
                                <!-- Notification Badge -->
                                <div
                                    class="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 bg-gradient-to-r from-red-500 to-pink-600 rounded-full border-2 border-white shadow-lg">
                                    <span class="text-xs font-bold text-white">3</span>
                                </div>
                                <div
                                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full animate-ping opacity-75">
                                </div>
                            </button>
                            <div
                                class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 w-96">
                                <div class="p-6 border-b border-gray-100">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-bold text-gray-900">Bildirimler</h3>
                                        <div class="flex items-center gap-2 px-3 py-1 bg-red-100 rounded-full">
                                            <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                            <span class="text-xs font-semibold text-red-700">3 yeni</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="max-h-96 overflow-y-auto">
                                    <!-- Notification Items -->
                                    <div class="p-4 space-y-3">
                                        <div
                                            class="group flex items-start gap-3 p-3 bg-blue-50/50 hover:bg-blue-50 rounded-xl transition-colors cursor-pointer">
                                            <div
                                                class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24"
                                                    stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-semibold text-gray-900">Yeni müşteri kaydı</p>
                                                <p class="text-xs text-gray-600 mt-1">Ahmet Yılmaz sisteme kaydoldu</p>
                                                <p class="text-xs text-blue-600 font-medium mt-1">2 dakika önce</p>
                                            </div>
                                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        </div>

                                        <div
                                            class="group flex items-start gap-3 p-3 bg-green-50/50 hover:bg-green-50 rounded-xl transition-colors cursor-pointer">
                                            <div
                                                class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <svg class="w-5 h-5 text-green-600" fill="none"
                                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-semibold text-gray-900">Satış tamamlandı</p>
                                                <p class="text-xs text-gray-600 mt-1">₺850,000 değerinde villa satışı
                                                </p>
                                                <p class="text-xs text-green-600 font-medium mt-1">5 dakika önce</p>
                                            </div>
                                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        </div>

                                        <div
                                            class="group flex items-start gap-3 p-3 bg-yellow-50/50 hover:bg-yellow-50 rounded-xl transition-colors cursor-pointer">
                                            <div
                                                class="w-10 h-10 bg-yellow-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <svg class="w-5 h-5 text-yellow-600" fill="none"
                                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12V15.75z" />
                                                </svg>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-semibold text-gray-900">İlan onay bekliyor</p>
                                                <p class="text-xs text-gray-600 mt-1">Bahçelievler daire ilanı</p>
                                                <p class="text-xs text-yellow-600 font-medium mt-1">10 dakika önce</p>
                                            </div>
                                            <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-t border-gray-100">
                                    <button
                                        class="w-full py-2 px-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] cursor-pointer">
                                        Tüm Bildirimleri Gör
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Profile Dropdown -->
                        <div class="dropdown" data-dropdown>
                            <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false"
                                class="group flex items-center space-x-2 p-2 rounded-2xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300 cursor-pointer">
                                <div class="relative">
                                    <div
                                        class="w-12 h-12 bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl overflow-hidden ring-2 ring-white shadow-lg group-hover:ring-blue-200 transition-all duration-300">
                                        <img src="{{ auth()->user() && auth()->user()->avatar_url ? asset(auth()->user()->avatar_url) : asset('assets/backend/images/user/7.jpg') }}"
                                            alt="{{ auth()->user()->name ?? 'User' }}"
                                            class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
                                    </div>

                                </div>
                                <div class="hidden md:block text-left flex-1">
                                    <div
                                        class="text-sm font-bold text-gray-900 group-hover:text-blue-700 transition-colors duration-300">
                                        {{ explode(' ', auth()->user()->name ?? 'User')[0] }}</div>

                                </div>
                                <svg class="h-4 w-4 text-gray-400 group-hover:text-blue-600 group-hover:rotate-180 transition-all duration-300"
                                    fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                </svg>
                            </button>
                            <div
                                class="dropdown-content bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 w-72 py-4">
                                <!-- Profile Header -->
                                <div class="px-6 py-4 border-b border-gray-100">
                                    <div class="flex items-center gap-3">
                                        <div
                                            class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                            {{ substr(auth()->user()->name ?? 'U', 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="font-bold text-gray-900">{{ auth()->user()->name ?? 'User' }}
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                {{ auth()->user()->email ?? '<EMAIL>' }}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-2">
                                    <a href="{{ route('profile') }}"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Profilim</div>
                                            <div class="text-xs text-gray-500">Profil bilgilerini düzenle</div>
                                        </div>
                                    </a>

                                    <a href="{{ route('company.settings') }}"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Ayarlar</div>
                                            <div class="text-xs text-gray-500">Sistem ayarları</div>
                                        </div>
                                    </a>

                                    <a href="#" onclick="openPasswordModal()"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-orange-100 text-orange-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Şifre Değiştir</div>
                                            <div class="text-xs text-gray-500">Güvenlik ayarları</div>
                                        </div>
                                    </a>
                                </div>

                                <!-- Management Section -->
                                <div class="border-t border-gray-100 pt-2 mt-2">
                                    <div class="px-4 py-2">
                                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Yönetim
                                        </p>
                                    </div>

                                    <a href="{{ route('users.index') }}"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-indigo-100 text-indigo-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Kullanıcı Yönetimi</div>
                                            <div class="text-xs text-gray-500">Sistem kullanıcıları</div>
                                        </div>
                                    </a>

                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-yellow-50 hover:text-yellow-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-yellow-100 text-yellow-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">İşlem Logları</div>
                                            <div class="text-xs text-gray-500">Sistem aktiviteleri</div>
                                        </div>
                                    </a>
                                </div>

                                <!-- Learning & Development Section -->
                                <div class="border-t border-gray-100 pt-2 mt-2">
                                    <div class="px-4 py-2">
                                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Eğitim
                                            & Geliştirme</p>
                                    </div>

                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Akademi</div>
                                            <div class="text-xs text-gray-500">Eğitim materyalleri</div>
                                        </div>
                                    </a>

                                    <a href="#"
                                        class="group flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-teal-50 hover:text-teal-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                        <div
                                            class="flex items-center justify-center w-8 h-8 bg-teal-100 text-teal-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium">Web Sitesi Yönetimi</div>
                                            <div class="text-xs text-gray-500">Site içerik yönetimi</div>
                                        </div>
                                    </a>
                                </div>

                                <!-- Logout Section -->
                                <div class="border-t border-gray-100 pt-2 mt-2">
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit"
                                            class="group flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-red-50 hover:text-red-700 transition-all duration-200 rounded-xl mx-2 cursor-pointer">
                                            <div
                                                class="flex items-center justify-center w-8 h-8 bg-red-100 text-red-600 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-200">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                                    stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="font-medium">Çıkış Yap</div>
                                                <div class="text-xs text-gray-500">Güvenli çıkış</div>
                                            </div>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Mobile Menu Button -->
                        <div class="lg:hidden">
                            <button
                                class="group p-3 text-gray-500 hover:text-blue-600 hover:bg-blue-50/80 rounded-2xl transition-all duration-300 cursor-pointer">
                                <div class="relative">
                                    <svg class="h-6 w-6 group-hover:scale-110 transition-transform duration-300"
                                        fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                                    </svg>
                                    <!-- Notification dot for mobile -->
                                    <div
                                        class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border border-white animate-pulse">
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1">
            @yield('content')
        </main>
    </div>

    <!-- Search Modal -->
    <div id="searchModal" class="fixed inset-0 z-50 hidden search-modal">
        <div class="flex min-h-full items-start justify-center p-4 text-center sm:items-center sm:p-0">
            <div class="search-modal-content relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"
                style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                    <div class="flex items-center border-b border-gray-200 pb-4">
                        <svg class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 15.803a7.5 7.5 0 0010.607 0z" />
                        </svg>
                        <input type="text" id="searchInput"
                            class="flex-1 border-0 bg-transparent text-lg focus:ring-0 focus:outline-none"
                            placeholder="Ara..." autocomplete="off">
                        <button onclick="closeSearchModal()" class="p-2 text-gray-400 hover:text-gray-600">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="mt-4">
                        <div class="space-y-2 max-h-96 overflow-y-auto">
                            <!-- Search results will be populated here -->
                            <div class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24"
                                    stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 15.803a7.5 7.5 0 0010.607 0z" />
                                </svg>
                                <p>Aramaya başlamak için yazmaya başlayın</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <div class="text-xs text-gray-500 flex items-center space-x-4">
                        <span class="flex items-center space-x-1">
                            <kbd class="px-2 py-1 bg-white border border-gray-200 rounded text-xs">↵</kbd>
                            <span>Seç</span>
                        </span>
                        <span class="flex items-center space-x-1">
                            <kbd class="px-2 py-1 bg-white border border-gray-200 rounded text-xs">ESC</kbd>
                            <span>Kapat</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Change Modal -->
    <div id="passwordModal" class="fixed inset-0 z-50 hidden search-modal">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
            <div class="search-modal-content relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-md"
                style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <form method="POST" action="{{ route('profile.update') }}">
                    @csrf
                    @method('PUT')
                    <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                        <div class="flex items-center mb-4">
                            <svg class="h-6 w-6 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                            </svg>
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Şifre Değiştir</h3>
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Mevcut Şifre</label>
                                <input type="password" name="current_password" class="form-input" required
                                    placeholder="Mevcut şifrenizi girin">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Yeni Şifre</label>
                                <input type="password" name="password" class="form-input" required
                                    placeholder="En az 8 karakter">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Yeni Şifre (Tekrar)</label>
                                <input type="password" name="password_confirmation" class="form-input" required
                                    placeholder="Yeni şifrenizi tekrar girin">
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6 space-x-3">
                        <button type="submit" class="btn btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                            </svg>
                            Şifreyi Değiştir
                        </button>
                        <button type="button" onclick="closePasswordModal()" class="btn btn-secondary">
                            İptal
                        </button>
                    </div>

                    <input type="hidden" name="user_id" value="{{ auth()->user()->id ?? '' }}">
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Search Modal Functions
        function openSearchModal() {
            document.getElementById('searchModal').classList.remove('hidden');
            document.getElementById('searchInput').focus();
        }

        function closeSearchModal() {
            document.getElementById('searchModal').classList.add('hidden');
        }

        // Password Modal Functions
        function openPasswordModal() {
            document.getElementById('passwordModal').classList.remove('hidden');
        }

        function closePasswordModal() {
            document.getElementById('passwordModal').classList.add('hidden');
        }

        // Dropdown functionality (hover + click stable, per-dropdown delay)
        document.addEventListener('DOMContentLoaded', function() {
            const dropdowns = document.querySelectorAll('[data-dropdown]');

            function clearCloseTimeout(dropdown) {
                if (dropdown._closeTimeoutId) {
                    clearTimeout(dropdown._closeTimeoutId);
                    dropdown._closeTimeoutId = null;
                }
            }

            function openDropdown(dropdown) {
                dropdown.classList.add('open');
                const trigger = dropdown.querySelector('[data-dropdown-trigger]');
                if (trigger) trigger.setAttribute('aria-expanded', 'true');
            }

            function closeDropdown(dropdown) {
                dropdown.classList.remove('open');
                const trigger = dropdown.querySelector('[data-dropdown-trigger]');
                if (trigger) trigger.setAttribute('aria-expanded', 'false');
            }

            function closeAll(except = null) {
                dropdowns.forEach(dd => {
                    if (dd !== except) closeDropdown(dd);
                });
            }

            dropdowns.forEach(dropdown => {
                const trigger = dropdown.querySelector('[data-dropdown-trigger]');
                const content = dropdown.querySelector('.dropdown-content');
                if (!trigger || !content) return;

                // Click toggle
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const isOpen = dropdown.classList.contains('open');
                    closeAll(dropdown);
                    if (!isOpen) openDropdown(dropdown);
                    else closeDropdown(dropdown);
                });

                // Hover behavior on trigger: close others and open this
                trigger.addEventListener('mouseenter', function() {
                    clearCloseTimeout(dropdown);
                    closeAll(dropdown);
                    openDropdown(dropdown);
                });
                trigger.addEventListener('mouseleave', function() {
                    clearCloseTimeout(dropdown);
                    dropdown._closeTimeoutId = setTimeout(() => closeDropdown(dropdown), 160);
                });

                // Hover behavior on content: keep open while inside
                content.addEventListener('mouseenter', function() {
                    clearCloseTimeout(dropdown);
                    openDropdown(dropdown);
                });
                content.addEventListener('mouseleave', function() {
                    clearCloseTimeout(dropdown);
                    dropdown._closeTimeoutId = setTimeout(() => closeDropdown(dropdown), 160);
                });

                // Prevent clicks inside content from bubbling and closing all
                content.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });

            // Close all when clicking outside
            document.addEventListener('click', function() {
                closeAll();
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Cmd+K or Ctrl+K to open search
            if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                e.preventDefault();
                openSearchModal();
            }

            // ESC to close modals
            if (e.key === 'Escape') {
                closeSearchModal();
                closePasswordModal();
            }
        });

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            const searchModal = document.getElementById('searchModal');
            const passwordModal = document.getElementById('passwordModal');

            if (e.target === searchModal) {
                closeSearchModal();
            }

            if (e.target === passwordModal) {
                closePasswordModal();
            }
        });

        // Search functionality (basic implementation)
        document.getElementById('searchInput')?.addEventListener('input', function(e) {
            const query = e.target.value;
            if (query.length > 2) {
                // Implement search logic here
                console.log('Searching for:', query);
            }
        });

        // Dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const dropdowns = document.querySelectorAll('[data-dropdown]');
            let activeDropdown = null;

            dropdowns.forEach(dropdown => {
                const trigger = dropdown.querySelector('[data-dropdown-trigger]');
                const content = dropdown.querySelector('.dropdown-content');

                if (!trigger || !content) return;

                // Initially hide all dropdowns
                content.style.display = 'none';
                dropdown.classList.remove('open');

                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close other dropdowns
                    if (activeDropdown && activeDropdown !== dropdown) {
                        activeDropdown.classList.remove('open');
                        activeDropdown.querySelector('.dropdown-content').style.display = 'none';
                    }

                    // Toggle current dropdown
                    const isOpen = dropdown.classList.contains('open');

                    if (isOpen) {
                        dropdown.classList.remove('open');
                        content.style.display = 'none';
                        activeDropdown = null;
                    } else {
                        dropdown.classList.add('open');
                        content.style.display = 'block';
                        activeDropdown = dropdown;
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (activeDropdown && !activeDropdown.contains(e.target)) {
                    activeDropdown.classList.remove('open');
                    activeDropdown.querySelector('.dropdown-content').style.display = 'none';
                    activeDropdown = null;
                }
            });

            // Close dropdowns on ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && activeDropdown) {
                    activeDropdown.classList.remove('open');
                    activeDropdown.querySelector('.dropdown-content').style.display = 'none';
                    activeDropdown = null;
                }
            });
        });
    </script>

    @stack('scripts')
    @livewireScripts

    @if (session('status'))
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            Swal.fire({
                title: @json(session('responseTitle')),
                text: @json(session('responseMessage')),
                icon: "{{ session('status') == 'success' ? 'success' : 'error' }}",
                timer: 2000,
                timerProgressBar: true,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        </script>
    @endif

    <script>
        // Dropdown functionality - Fixed version
        document.addEventListener('DOMContentLoaded', function() {
            const dropdowns = document.querySelectorAll('[data-dropdown]');
            let activeDropdown = null;

            dropdowns.forEach(dropdown => {
                const trigger = dropdown.querySelector('[data-dropdown-trigger]');
                const content = dropdown.querySelector('.dropdown-content');

                if (!trigger || !content) return;

                // Ensure dropdowns start completely closed
                dropdown.classList.remove('open');

                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close all other dropdowns first
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.classList.remove('open');
                        }
                    });

                    // Toggle current dropdown
                    if (dropdown.classList.contains('open')) {
                        dropdown.classList.remove('open');
                        activeDropdown = null;
                    } else {
                        dropdown.classList.add('open');
                        activeDropdown = dropdown;
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                const clickedDropdown = e.target.closest('[data-dropdown]');
                if (!clickedDropdown) {
                    dropdowns.forEach(dropdown => {
                        dropdown.classList.remove('open');
                    });
                    activeDropdown = null;
                }
            });

            // Close dropdowns on ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    dropdowns.forEach(dropdown => {
                        dropdown.classList.remove('open');
                    });
                    activeDropdown = null;
                }
            });
        });
    </script>
</body>

</html>
