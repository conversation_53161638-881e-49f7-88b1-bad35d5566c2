<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Company extends Model
{
    use HasUuids;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'name',
        'slug',
        'description',
        'subdomain',
        'logo_url',
        'website',
        'phone',
        'whatsapp',
        'email',
        'facebook',
        'instagram',
        'twitter',
        'linkedin',
        'youtube',
        'tiktok',
        'pinterest',
        'address',
        'city_id',
        'district_id',
        'location_url',
        'owner_id',
        'is_active',
        'settings',
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the current active package through subscription.
     */
    public function getCurrentPackage()
    {
        $activeSubscription = $this->subscriptions()
            ->where('status', 'active')
            ->where(function($query) {
                $query->whereNull('ends_at')
                      ->orWhere('ends_at', '>=', now());
            })
            ->with('package')
            ->first();

        return $activeSubscription ? $activeSubscription->package : null;
    }

    /**
     * Get all users that belong to this company.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all staff invitations for this company.
     */
    public function staffInvitations(): HasMany
    {
        return $this->hasMany(StaffInvitation::class);
    }

    /**
     * Get all orders for this company.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all payments for this company.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get all subscriptions for this company.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the active subscription for this company.
     */
    public function activeSubscription(): HasMany
    {
        return $this->subscriptions()->where('status', 'active');
    }

    /**
     * Get all invoices for this company.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get all transaction logs for this company.
     */
    public function transactionLogs(): HasMany
    {
        return $this->hasMany(TransactionLog::class);
    }

    /**
     * Get all coupon usages for this company.
     */
    public function couponUsages(): HasMany
    {
        return $this->hasMany(CouponUsage::class);
    }

    /**
     * Şirketin tüm rollerini getir.
     */
    public function companyRoles(): HasMany
    {
        return $this->hasMany(CompanyRole::class);
    }

    /**
     * Şirketin sahibini getir.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Check if company's subscription is expired.
     */
    public function isSubscriptionExpired(): bool
    {
        $activeSubscription = $this->getActiveSubscription();
        return $activeSubscription ? $activeSubscription->isExpired() : true;
    }

    /**
     * Get days remaining until subscription expires.
     */
    public function getSubscriptionDaysRemaining(): int
    {
        $activeSubscription = $this->getActiveSubscription();
        return $activeSubscription ? $activeSubscription->getDaysRemaining() : 0;
    }

    /**
     * Get the active subscription.
     */
    public function getActiveSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where(function($query) {
                $query->whereNull('ends_at')
                      ->orWhere('ends_at', '>=', now());
            })
            ->first();
    }
}
