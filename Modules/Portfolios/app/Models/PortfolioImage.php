<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use App\Models\Company;

class PortfolioImage extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_images';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'company_id',
        'imageable_type',
        'imageable_id',
        'path',
        'alt',
        'order',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the company that owns the image.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /**
     * Get the parent imageable model (portfolio, etc.).
     */
    public function imageable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to filter by company.
     */
    public function scopeForCompany($query, string $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to order by image order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    /**
     * Get the full URL of the image.
     */
    public function getUrlAttribute(): string
    {
        return asset('storage/' . $this->path);
    }

    /**
     * Get the filename from path.
     */
    public function getFilenameAttribute(): string
    {
        return basename($this->path);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set order when creating
        static::creating(function ($image) {
            if (is_null($image->order)) {
                $maxOrder = static::where('imageable_type', $image->imageable_type)
                                 ->where('imageable_id', $image->imageable_id)
                                 ->max('order');
                $image->order = ($maxOrder ?? 0) + 1;
            }
        });

        // Delete physical file when model is deleted
        static::deleting(function ($image) {
            if (file_exists(storage_path('app/public/' . $image->path))) {
                unlink(storage_path('app/public/' . $image->path));
            }
        });
    }
}
