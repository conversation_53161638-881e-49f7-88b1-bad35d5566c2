<div>
    <!-- Modal -->
    @if($showModal)
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeModal"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <form wire:submit.prevent="save" id="packageForm">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" id="modal-title">
                                    {{ $editMode ? 'Paket Düzenle' : 'Yeni Paket Oluştur' }}
                                </h3>

                                <div class="grid grid-cols-12 gap-4">
                                    <!-- Basic Information -->
                                    <div class="col-span-12">
                                        <h4 class="text-md font-semibold text-gray-800 mb-3 border-b pb-2">Temel Bilgiler</h4>
                                    </div>

                                    <!-- Package Name -->
                                    <div class="col-span-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Paket Adı *</label>
                                        <input type="text" wire:model.live="name"
                                               class="form-control @error('name') border-red-500 @enderror"
                                               placeholder="Örn: Profesyonel Paket">
                                        @error('name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Package Slug -->
                                    <div class="col-span-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                                        <input type="text" wire:model="slug"
                                               class="form-control @error('slug') border-red-500 @enderror"
                                               placeholder="profesyonel-paket">
                                        @error('slug') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Description -->
                                    <div class="col-span-12">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                                        <textarea wire:model="description" rows="3"
                                                  class="form-control @error('description') border-red-500 @enderror"
                                                  placeholder="Paket açıklaması..."></textarea>
                                        @error('description') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Pricing -->
                                    <div class="col-span-12">
                                        <h4 class="text-md font-semibold text-gray-800 mb-3 border-b pb-2">Fiyatlandırma</h4>
                                    </div>

                                    <div class="col-span-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Aylık Fiyat (₺) *</label>
                                        <input type="number" step="0.01" wire:model="monthly_price"
                                               class="form-control @error('monthly_price') border-red-500 @enderror"
                                               placeholder="0.00">
                                        @error('monthly_price') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>

                                    <div class="col-span-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Yıllık Fiyat (₺) *</label>
                                        <input type="number" step="0.01" wire:model="yearly_price"
                                               class="form-control @error('yearly_price') border-red-500 @enderror"
                                               placeholder="0.00">
                                        @error('yearly_price') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Features -->
                                    <div class="col-span-12">
                                        <h4 class="text-md font-semibold text-gray-800 mb-3 border-b pb-2">Özellikler</h4>
                                    </div>

                                    <div class="col-span-12">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Paket Özellikleri *</label>

                                        <!-- Add new feature -->
                                        <div class="flex gap-2 mb-3">
                                            <input type="text" wire:model="newFeature"
                                                   class="form-control flex-1"
                                                   placeholder="Yeni özellik ekle..."
                                                   wire:keydown.enter.prevent="addFeature">
                                            <button type="button" wire:click="addFeature"
                                                    class="btn btn-success text-white">
                                                <i class="fa-solid fa-plus"></i> Ekle
                                            </button>
                                        </div>

                                        <!-- Features list -->
                                        @if(count($features) > 0)
                                            <div class="space-y-2">
                                                @foreach($features as $index => $feature)
                                                    <div class="flex items-center gap-2 p-2 bg-gray-50 rounded">
                                                        <i class="fa-solid fa-circle-check text-green-500"></i>
                                                        <span class="flex-1">{{ $feature }}</span>
                                                        <button type="button" wire:click="removeFeature({{ $index }})"
                                                                class="text-red-500 hover:text-red-700">
                                                            <i class="fa-solid fa-trash"></i>
                                                        </button>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                        @error('features') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Limits -->
                                    <div class="col-span-12">
                                        <h4 class="text-md font-semibold text-gray-800 mb-3 border-b pb-2">Paket Limitleri</h4>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimum Personel</label>
                                        <input type="number" wire:model="max_staff"
                                               class="form-control"
                                               placeholder="0 (sınırsız için -1)">
                                        <small class="text-gray-500">Sınırsız için -1 girin</small>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimum İlan</label>
                                        <input type="number" wire:model="max_properties"
                                               class="form-control"
                                               placeholder="0 (sınırsız için -1)">
                                        <small class="text-gray-500">Sınırsız için -1 girin</small>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Depolama (GB)</label>
                                        <input type="number" wire:model="max_storage_gb"
                                               class="form-control"
                                               placeholder="0 (sınırsız için -1)">
                                        <small class="text-gray-500">Sınırsız için -1 girin</small>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Fotoğraf/İlan</label>
                                        <input type="number" wire:model="max_photo_per_property"
                                               class="form-control"
                                               placeholder="0 (sınırsız için -1)">
                                        <small class="text-gray-500">Sınırsız için -1 girin</small>
                                    </div>

                                    <!-- Boolean Features -->
                                    <div class="col-span-12">
                                        <h4 class="text-md font-semibold text-gray-800 mb-3 border-b pb-2">Ek Özellikler</h4>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" wire:model="has_api_access" class="form-checkbox">
                                            <span class="ml-2">API Erişimi</span>
                                        </label>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" wire:model="has_custom_domain" class="form-checkbox">
                                            <span class="ml-2">Özel Domain</span>
                                        </label>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" wire:model="has_advanced_reports" class="form-checkbox">
                                            <span class="ml-2">Gelişmiş Raporlar</span>
                                        </label>
                                    </div>

                                    <div class="col-span-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" wire:model="has_crm" class="form-checkbox">
                                            <span class="ml-2">CRM Sistemi</span>
                                        </label>
                                    </div>

                                    <!-- Package Settings -->
                                    <div class="col-span-12">
                                        <h4 class="text-md font-semibold text-gray-800 mb-3 border-b pb-2">Paket Ayarları</h4>
                                    </div>

                                    <div class="col-span-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Sıralama</label>
                                        <input type="number" wire:model="sort_order"
                                               class="form-control"
                                               placeholder="0">
                                    </div>

                                    <div class="col-span-4">
                                        <label class="flex items-center mt-6">
                                            <input type="checkbox" wire:model="is_popular" class="form-checkbox">
                                            <span class="ml-2">Popüler Paket</span>
                                        </label>
                                    </div>

                                    <div class="col-span-4">
                                        <label class="flex items-center mt-6">
                                            <input type="checkbox" wire:model="is_active" class="form-checkbox">
                                            <span class="ml-2">Aktif</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal Footer -->
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                                <button type="submit"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm {{ $isLoading ? 'opacity-50 cursor-not-allowed' : '' }}"
                                {{ $isLoading ? 'disabled' : '' }}>

                            @if($isLoading)
                                <i class="fa-solid fa-spinner fa-spin mr-2"></i>
                                İşleniyor...
                            @else
                                <i class="fa-solid fa-save mr-2"></i>
                                {{ $editMode ? 'Güncelle' : 'Oluştur' }}
                            @endif
                        </button>
                        <button type="button" wire:click="closeModal"
                                class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm {{ $isLoading ? 'opacity-50 cursor-not-allowed' : '' }}"
                                {{ $isLoading ? 'disabled' : '' }}>
                            İptal
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endif
</div>
