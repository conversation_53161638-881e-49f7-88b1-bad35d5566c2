<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;

// Ana rota - Firma Dashboard (giri<PERSON> ya<PERSON> login'e yönlendir)
Route::get('/', [DashboardController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');

// Firma Dashboard rotaları (artık ana rotalar)
Route::middleware(['auth', 'verified'])->group(function(){



    Route::get('/pricing', [PackageController::class, 'index'])->name('pricing');

    // API Routes for real-time updates
    Route::get('/api/activities', [App\Http\Controllers\ActivityController::class, 'getRecentActivities'])->name('api.activities');

    // Search API
    Route::get('/api/search', [App\Http\Controllers\SearchController::class, 'search'])->name('api.search');
});

// Eski dashboard rotası kaldırıldı - artık ana rota (/) dashboard

// Eski profile rotaları kaldırıldı - artık UserController üzerinden yönetiliyor

require __DIR__.'/auth.php';
