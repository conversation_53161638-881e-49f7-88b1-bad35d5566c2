<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Fiyat Geçmişi</h1>
                        <p class="text-gray-600 mt-2">{{ $portfolio->title }} - Fiyat <PERSON></p>
                    </div>
                    <div class="flex items-center gap-3">
                        <a href="{{ route('portfolios.index') }}" 
                           class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                            ← Geri Dön
                        </a>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <PERSON>ya<PERSON>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Current Price Card -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20 shadow-xl">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">₺{{ number_format($portfolio->price ?? 0) }}</div>
                        <div class="text-sm text-gray-500">Mevcut Fiyat</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">₺{{ number_format(($portfolio->price ?? 0) * 0.95) }}</div>
                        <div class="text-sm text-gray-500">Ortalama Teklif</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">₺{{ number_format(($portfolio->price ?? 0) / ($portfolio->net_area ?? 1)) }}/m²</div>
                        <div class="text-sm text-gray-500">m² Fiyatı</div>
                    </div>
                </div>
            </div>

            <!-- Price History Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20 shadow-xl">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Fiyat Değişim Grafiği</h3>
                <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <p class="text-gray-500">Fiyat grafiği burada gösterilecek</p>
                </div>
            </div>

            <!-- Price History Timeline -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-xl">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Fiyat Değişim Geçmişi</h3>
                
                <div class="space-y-4">
                    <div class="flex items-start space-x-4 p-4 bg-green-50 rounded-xl border border-green-200">
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 010 0L21.75 9M21.75 9l-3.75 3.75M21.75 9v3.75" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-semibold text-gray-900">Fiyat Artışı</h4>
                                <span class="text-sm text-gray-500">1 hafta önce</span>
                            </div>
                            <p class="text-green-700 font-medium">₺1,200,000 → ₺1,300,000</p>
                            <p class="text-sm text-gray-600 mt-1">Piyasa koşulları nedeniyle %8.3 artış</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h4 class="font-semibold text-gray-900">İlk Fiyat Belirleme</h4>
                                <span class="text-sm text-gray-500">1 ay önce</span>
                            </div>
                            <p class="text-blue-700 font-medium">₺1,200,000</p>
                            <p class="text-sm text-gray-600 mt-1">Piyasa analizi sonucu belirlenen başlangıç fiyatı</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection
</x-backend-layout>
