<x-guest-layout>
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <div x-data="{
        email: '{{ old('email') }}',
        password: '',
        emailValid: {{ old('email') ? 'true' : 'false' }},
        passwordValid: false,
        loading: false,
        hasServerErrors: {{ $errors->any() ? 'true' : 'false' }},
        init() {
            // İlk yüklemede email varsa validate et
            if (this.email) {
                this.validateEmail();
            }
            // Server error varsa state'i sıfırla
            if (this.hasServerErrors) {
                this.$nextTick(() => {
                    this.emailValid = this.validateEmail();
                    this.passwordValid = this.validatePassword();
                });
            }
        },
        validateEmail() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            this.emailValid = emailRegex.test(this.email) && this.email.length > 0;
            return this.emailValid;
        },
        validatePassword() {
            this.passwordValid = this.password.length >= 8;
            return this.passwordValid;
        },
        async submitForm() {
            if (this.emailValid && this.passwordValid) {
                this.loading = true;
                // DOM güncellemesini bekle
                await this.$nextTick();
                // Kısa bir gecikme ile form submit et
                setTimeout(() => {
                    this.$refs.form.submit();
                }, 500);
            } else {
                // Form geçerli değilse shake effect
                const button = this.$refs.submitBtn;
                button.classList.add('shake');
                setTimeout(() => button.classList.remove('shake'), 500);
            }
        }
    }">
        <form x-ref="form" method="POST" action="{{ route('login') }}" class="space-y-6" x-on:submit.prevent="submitForm()">
          @csrf

        <!-- Email Address -->
        <div class="form-group space-y-2">
            <x-input-label for="email" value="E-posta Adresi" />
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
                    </svg>
                </div>
                <input id="email"
                    name="email"
                    type="email"
                    x-model="email"
                    x-on:input="validateEmail(); hasServerErrors = false"
                    required
                    autofocus
                    autocomplete="username"
                    placeholder="<EMAIL>"
                    class="form-input pl-10 pr-12"
                    :class="{ 'error': (email.length > 0 && !emailValid) || (hasServerErrors && {{ $errors->has('email') ? 'true' : 'false' }}), 'success': emailValid && !hasServerErrors }" />

                <!-- Validation Icon -->
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="email.length > 0 && !hasServerErrors">
                    <svg x-show="emailValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <svg x-show="!emailValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            </div>

                        <!-- Error Messages -->
            @if($errors->get('email'))
                <!-- Server-side Error (Priority) -->
                <x-input-error :messages="$errors->get('email')" />
            @else
                <!-- Real-time Error Message -->
                <div x-show="email.length > 0 && !emailValid" class="error-message">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Geçerli bir e-posta adresi giriniz
                </div>
            @endif
        </div>

        <!-- Password -->
        <div class="form-group space-y-2">
            <x-input-label for="password" value="Şifre" />
            <div class="relative" x-data="{ show: false }">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0V10.5m-.75 0h10.5m-10.5 0A2.25 2.25 0 006.75 12.75v6A2.25 2.25 0 009 21h6a2.25 2.25 0 002.25-2.25v-6A2.25 2.25 0 0016.5 10.5" />
                    </svg>
                </div>
                <input id="password"
                    name="password"
                    x-model="password"
                    x-on:input="validatePassword(); hasServerErrors = false"
                    :type="show ? 'text' : 'password'"
                    required
                    autocomplete="current-password"
                    placeholder="••••••••"
                    class="form-input pl-10 pr-20"
                    :class="{ 'error': (password.length > 0 && !passwordValid) || (hasServerErrors && {{ $errors->has('password') ? 'true' : 'false' }}), 'success': passwordValid && !hasServerErrors }" />

                <!-- Validation Icon -->
                <div class="absolute right-12 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="password.length > 0 && !hasServerErrors">
                    <svg x-show="passwordValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <svg x-show="!passwordValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>

                <!-- Show/Hide Password Button -->
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center z-20 text-gray-500 hover:text-gray-700 transition-colors" x-on:click="show = !show" aria-label="Şifreyi göster/gizle">
                    <svg x-show="!show" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12c2.25-4.5 6.364-7.5 9.75-7.5s7.5 3 9.75 7.5c-2.25 4.5-6.364 7.5-9.75 7.5s-7.5-3-9.75-7.5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <svg x-show="show" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 3l18 18" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.5 12c2.25 4.5 6.364 7.5 9.75 7.5 1.711 0 3.348-.482 4.8-1.318" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.21 6.21C7.764 5.178 9.52 4.5 11.25 4.5c3.386 0 7.5 3 9.75 7.5a10.523 10.523 0 01-4.478 4.77" />
                    </svg>
                </button>
            </div>

                        <!-- Error Messages -->
            @if($errors->get('password'))
                <!-- Server-side Error (Priority) -->
                <x-input-error :messages="$errors->get('password')" />
            @else
                <!-- Real-time Error Message -->
                <div x-show="password.length > 0 && !passwordValid" class="error-message">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Şifre en az 8 karakter olmalıdır
                </div>
            @endif
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between my-6">
            <label for="remember_me" class="flex items-center gap-2 cursor-pointer">
                <input id="remember_me"
                    name="remember"
                    type="checkbox"
                    class="form-checkbox">
                <span class="text-sm text-gray-700">Beni Hatırla</span>
            </label>

            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                    Şifremi Unuttum
                </a>
            @endif
        </div>

                        <!-- Submit Button -->
        <div class="mt-4">
            <button x-ref="submitBtn" type="submit"
                    class="btn btn-primary w-full text-base gap-3"
                    :disabled="loading || (!emailValid || !passwordValid)"
                    :class="{ 'opacity-50 cursor-not-allowed': loading || (!emailValid || !passwordValid), 'cursor-pointer': !loading && emailValid && passwordValid }"
                    x-bind:style="(loading || (!emailValid || !passwordValid)) ? 'pointer-events: none;' : 'pointer-events: auto;'">
                <svg x-show="!loading" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                <svg x-show="loading" class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span x-text="loading ? 'Giriş yapılıyor...' : 'Giriş Yap'"></span>
            </button>
        </div>

        <div class="divider-modern">
            <span>veya</span>
        </div>

        <!-- Register Link -->
        <div class="text-center">
            <a href="{{ route('register') }}" class="btn btn-secondary w-full">
                Yeni Hesap Oluştur
            </a>
        </div>
        </form>
    </div>
</x-guest-layout>
