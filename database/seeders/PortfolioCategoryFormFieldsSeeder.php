<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PortfolioCategoryFormFieldsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $fields = [
            // ---------------- Konut ----------------
            [
                'category_id' => 1,
                'field_label' => 'Oda <PERSON>',
                'field_name' => 'room_count',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    '1+0','1+1','1.5+1','2+0','2+1','2.5+1','2+2','3+0','3+1','3.5+1','3+2','3+3','4+0','4+1','4.5+1','4.5+2','4+2','4+3','4+4','5+1','5.5+1','5+2','5+3','5+4','6+1','6+2','6.5+1','6+3','6+4','7+1','7+2','7+3','8+1','8+2','8+3','8+4','9+1','9+2','9+3','9+4','9+5','9+6','10+1','10+2','10 Üzeri'
                ]),
                'placeholder' => 'Oda + salon sayısı',
                'help_text' => 'Oda + Salon Sayısı',
                'sort_order' => 1,
                'field_group' => 'basic_info'
                ],
                [
                'category_id' => 1,
                'field_label' => 'Bulunduğu Kat',
                'field_name' => 'floor',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    'Giriş Altı Kot 4',
                    'Giriş Altı Kot 3',
                    'Giriş Altı Kot 2',
                    'Giriş Altı Kot 1',
                    'Bodrum Kat',
                    'Zemin Kat',
                    'Bahçe Katı',
                    'Giriş Katı',
                    'Yüksek Giriş',
                    'Müstakil',
                    'Villa Tipi',
                    'Çatı Katı',
                    '1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30 ve üzeri'
                    ]),
                    'placeholder' => 'Bulunduğu kat',
                    'help_text' => 'Bulunduğu kat',
                    'sort_order' => 2,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 1,
                'field_label' => 'Toplam Kat Sayısı',
                'field_name' => 'total_floors',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    '1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30 ve üzeri'
                    ]),
                    'placeholder' => 'Toplam kat sayısı',
                    'help_text' => 'Toplam kat sayısı',
                    'sort_order' => 3,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 1,
                'field_label' => 'Bina Yaşı',
                'field_name' => 'building_age',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    '0','1','2','3','4','5-10 Yaş Arası','11-15 Yaş Arası','16-20 Yaş Arası','21-25 Yaş Arası','26-30 Yaş Arası','31 Yaş ve Üzeri'
                    ]),
                    'placeholder' => 'Bina yaşı',
                    'help_text' => 'Bina yaşı',
                    'sort_order' => 4,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 1,
                'field_label' => 'Isıtma',
                'field_name' => 'heating_type',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    'Kalorifer',
                    'Kombi (Doğalgaz)',
                    'Kombi (Elektrik)',
                    'Merkezi Sistem',
                    'Merkezi (Pay Ölçer)',
                    'Yerden Isıtma',
                    'Soba',
                    'Doğalgaz Sobası',
                    'Klima',
                    'Yerden Isıtma',
                    'Güneş Enerjisi',
                    'Jeotermal',
                    'Şömine',
                    'Isı Pompası',
                    'Yok'
                    ]),
                    'placeholder' => 'Isıtma tipi',
                    'help_text' => 'Isıtma tipi',
                    'sort_order' => 5,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 1,
                'field_label' => 'Banyo Sayısı',
                'field_name' => 'bathroom_count',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    'Yok','1','2','3','4','5','6','6 ve üzeri'
                    ]),
                    'placeholder' => 'Banyo sayısı',
                    'help_text' => 'Banyo sayısı',
                    'sort_order' => 6,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 1,
                'field_label' => 'Balkon',
                'field_name' => 'balcony',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode(['Var', 'Yok']),
                'placeholder' => 'Balkon',
                'help_text' => 'Balkon',
                'sort_order' => 7,
                'field_group' => 'basic_info'
                ],
                [
                'category_id' => 1,
                'field_label' => 'Mutfak',
                'field_name' => 'kitchen',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode(['Açık (Amerikan)', 'Kapalı']),
                'placeholder' => 'Balkon',
                'help_text' => 'Balkon',
                'sort_order' => 8,
                'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 1,
                    'field_label' => 'Asansör',
                    'field_name' => 'elevator',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['Var', 'Yok']),
                    'placeholder' => 'Asansör',
                    'help_text' => 'Asansör',
                    'sort_order' => 9,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 1,
                    'field_label' => 'Otopark',
                    'field_name' => 'parking',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['Açık Otopark','Kapalı Otopark','Açık & Kapalı Otopark', 'Yok']),
                    'placeholder' => 'Otopark',
                    'help_text' => 'Otopark',
                    'sort_order' => 10,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 1,
                    'field_label' => 'Eşyalı',
                    'field_name' => 'furnished',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['Evet', 'Hayır']),
                    'placeholder' => 'Eşyalı',
                    'help_text' => 'Eşyalı',
                    'sort_order' => 11,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 1,
                    'field_label' => 'Kullanım Durumu',
                    'field_name' => 'usage_status',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['Boş', 'Kiracılı', 'Mülk Sahibi']),
                    'placeholder' => 'Kullanım Durumu',
                    'help_text' => 'Kullanım Durumu',
                    'sort_order' => 12,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 1,
                    'field_label' => 'Site İçerisinde',
                    'field_name' => 'in_development',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['Evet', 'Hayır']),
                    'placeholder' => 'Site İçerisinde',
                    'help_text' => 'Site İçerisinde',
                    'sort_order' => 13,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 1,
                    'field_label' => 'Aidat',
                    'field_name' => 'monthly_fee',
                    'field_type' => 'number',
                    'is_searchable' => false,
                    'is_required' => false,
                    'placeholder' => 'Aidat (TL)',
                    'help_text' => 'Aidat (TL)',
                    'sort_order' => 14,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 1,
                    'field_label' => 'Depozito',
                    'field_name' => 'deposit',
                    'field_type' => 'number',
                    'is_searchable' => false,
                    'is_required' => false,
                    'placeholder' => 'Depozito (TL)',
                    'help_text' => 'Depozito (TL)',
                    'sort_order' => 15,
                    'field_group' => 'basic_info'
                ],
                // Arsa
                [
                    'category_id' => 2,
                    'field_label' => 'İmar Durumu',
                    'field_name' => 'zoning_status',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode([
                        'Ada', 'A-Lejantlı', 'Arazi', 'Bağ & Bahçe', 'Depo & Antrepo', 'Eğitim', 'Enerji Depolama', 'Konut', 'Kültürel Tesis', 'Muhtelif', 'Özel Kullanım', 'Sağlık', 'Sanayi', 'Sera', 'Sit Alanı', 'Spor Alanı', 'Tarla', 'Tarla + Bağ', 'Ticari', 'Ticari + Konut', 'Toplu Konut', 'Turizm', 'Turizm + Konut', 'Turizm + Ticari', 'Villa', 'Zeytinlik'
                    ]),
                    'placeholder' => 'İmar Durumu',
                    'help_text' => 'İmar Durumu',
                    'sort_order' => 1,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 2,
                    'field_label' => 'Ada No',
                    'field_name' => 'parcel_number',
                    'field_type' => 'number',
                    'is_searchable' => true,
                    'is_required' => false,
                    'placeholder' => 'Ada No',
                    'help_text' => 'Ada No',
                    'sort_order' => 2,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 2,
                    'field_label' => 'Parsel No',
                    'field_name' => 'plot_number',
                    'field_type' => 'number',
                    'is_searchable' => true,
                    'is_required' => false,
                    'placeholder' => 'Parsel No',
                    'help_text' => 'Parsel No',
                    'sort_order' => 3,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 2,
                    'field_label' => 'Kaks (Emsal)',
                    'field_name' => 'kaks',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode([
                        '0.05', '0.10', '0.15', '0.17', '0.20', '0.24', '0.25', '0.30', '0.35', '0.40', '0.45', '0.50', '0.60', '0.70', '0.75', '0.80', '0.90', '0.95', '1.0', '1.05', '1.10', '1.15', '1.20', '1.25', '1.30', '1.35', '1.40', '1.45', '1.50', '1.55', '1.60', '1.75', '1.80', '1.90', '2.0', '2.07', '2.10', '2.15', '2.30', '2.40', '2.50', '2.80', '3.0', '3.20', '3.30', '5.0', '10.20', '15.30', 'Belirtilmemiş'
                    ]),
                    'placeholder' => 'Kaks (Emsal)',
                    'help_text' => 'Arsaya uygulanan KAKS (emsal) değeri.',
                    'sort_order' => 4,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 2,
                    'field_label' => 'Gabari',
                    'field_name' => 'gabari',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode([
                        '3.50', '6.50', '7.50', '8.50', '9.50', '10.50', '11.50', '12.50', '14.50', '15.50', '18.50', '21.50', '24.50', '27.50', '30.50', '36.00', 'Serbest', 'Belirtilmemiş'
                    ]),
                    'placeholder' => 'Gabari',
                    'help_text' => 'Arsanın izin verilen bina yüksekliği (gabari).',
                    'sort_order' => 5,
                    'field_group' => 'basic_info'
                ],
                // Is Yeri
                [
                    'category_id' => 3,
                    'field_label' => 'Bölüm & Oda Sayısı',
                    'field_name' => 'room_count',
                    'field_type' => 'number',
                    'is_searchable' => true,
                    'is_required' => false,
                    'placeholder' => 'Bölüm & Oda Sayısı',
                    'help_text' => 'Bölüm & Oda Sayısı',
                    'sort_order' => 1,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 3,
                    'field_label' => 'Aidat',
                    'field_name' => 'monthly_fee',
                    'field_type' => 'number',
                    'is_searchable' => false,
                    'is_required' => false,
                    'placeholder' => 'Aidat (TL)',
                    'help_text' => 'Aidat (TL)',
                    'sort_order' => 2,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 3,
                    'field_label' => 'Depozito',
                    'field_name' => 'deposit',
                    'field_type' => 'number',
                    'is_searchable' => false,
                    'is_required' => false,
                    'placeholder' => 'Depozito (TL)',
                    'help_text' => 'Depozito (TL)',
                    'sort_order' => 3,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 3,
                'field_label' => 'Bina Yaşı',
                'field_name' => 'building_age',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    '0','1','2','3','4','5-10 Yaş Arası','11-15 Yaş Arası','16-20 Yaş Arası','21-25 Yaş Arası','26-30 Yaş Arası','31 Yaş ve Üzeri'
                    ]),
                    'placeholder' => 'Bina yaşı',
                    'help_text' => 'Bina yaşı',
                    'sort_order' => 4,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 3,
                'field_label' => 'Isıtma',
                'field_name' => 'heating_type',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    'Kalorifer',
                    'Kombi (Doğalgaz)',
                    'Kombi (Elektrik)',
                    'Merkezi Sistem',
                    'Merkezi (Pay Ölçer)',
                    'Yerden Isıtma',
                    'Soba',
                    'Doğalgaz Sobası',
                    'Klima',
                    'Yerden Isıtma',
                    'Güneş Enerjisi',
                    'Jeotermal',
                    'Şömine',
                    'Isı Pompası',
                    'Yok'
                    ]),
                    'placeholder' => 'Isıtma tipi',
                    'help_text' => 'Isıtma tipi',
                    'sort_order' => 5,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 3,
                'field_label' => 'Giriş Yüksekliği (m)',
                'field_name' => 'entrance_height',
                'field_type' => 'number',
                'is_searchable' => false,
                'is_required' => false,
                'placeholder' => 'Giriş Yüksekliği (m)',
                'help_text' => 'Giriş Yüksekliği (m)',
                'sort_order' => 6,
                'field_group' => 'basic_info'
                ],
                [
                'category_id' => 3,
                'field_label' => 'Yapının Durumu',
                'field_name' => 'building_condition',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    'Yeni', 'İkinci El'
                    ]),
                    'placeholder' => 'Yapının Durumu',
                    'help_text' => 'Yapının Durumu',
                    'sort_order' => 7,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 3,
                'field_label' => 'Zemin Etüdü',
                'field_name' => 'flooring',
                'field_type' => 'select',
                'is_searchable' => false,
                'is_required' => false,
                'field_options' => json_encode(['Var','Yok']),
                    'placeholder' => 'Zemin Etüdü',
                    'help_text' => 'Zemin Etüdü',
                    'sort_order' => 8,
                    'field_group' => 'basic_info'
                ],
                // Bina
                [
                'category_id' => 4,
                'field_label' => 'Kat Sayısı',
                'field_name' => 'floor_count',
                'field_type' => 'number',
                'is_searchable' => false,
                'is_required' => false,
                'placeholder' => 'Kat Sayısı',
                'help_text' => 'Kat Sayısı',
                'sort_order' => 1,
                'field_group' => 'basic_info'
                ],
                [
                'category_id' => 4,
                'field_label' => 'Bir Kattaki Daire',
                'field_name' => 'apartment_per_floor',
                'field_type' => 'number',
                'is_searchable' => false,
                'is_required' => false,
                'placeholder' => 'Bir Kattaki Daire Sayısı',
                'help_text' => 'Bir Kattaki Daire Sayısı',
                'sort_order' => 2,
                'field_group' => 'basic_info'
                ],
                [
                'category_id' => 4,
                'field_label' => 'Isıtma',
                'field_name' => 'heating_type',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    'Kalorifer',
                    'Kombi (Doğalgaz)',
                    'Kombi (Elektrik)',
                    'Merkezi Sistem',
                    'Merkezi (Pay Ölçer)',
                    'Yerden Isıtma',
                    'Soba',
                    'Doğalgaz Sobası',
                    'Klima',
                    'Yerden Isıtma',
                    'Güneş Enerjisi',
                    'Jeotermal',
                    'Şömine',
                    'Isı Pompası',
                    'Yok'
                    ]),
                    'placeholder' => 'Isıtma tipi',
                    'help_text' => 'Isıtma tipi',
                    'sort_order' => 3,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 4,
                    'field_label' => 'Asansör',
                    'field_name' => 'elevator',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['Var', 'Yok']),
                    'placeholder' => 'Asansör',
                    'help_text' => 'Asansör',
                    'sort_order' => 4,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 4,
                    'field_label' => 'Otopark',
                    'field_name' => 'parking',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['Açık Otopark','Kapalı Otopark','Açık & Kapalı Otopark', 'Yok']),
                    'placeholder' => 'Otopark',
                    'help_text' => 'Otopark',
                    'sort_order' => 5,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 4,
                'field_label' => 'Bina Yaşı',
                'field_name' => 'building_age',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    '0','1','2','3','4','5-10 Yaş Arası','11-15 Yaş Arası','16-20 Yaş Arası','21-25 Yaş Arası','26-30 Yaş Arası','31 Yaş ve Üzeri'
                    ]),
                    'placeholder' => 'Bina yaşı',
                    'help_text' => 'Bina yaşı',
                    'sort_order' => 6,
                    'field_group' => 'basic_info'
                ],
                // Devre Mulk
                [
                    'category_id' => 5,
                    'field_label' => 'Oda Sayısı',
                    'field_name' => 'room_count',
                    'field_type' => 'number',
                    'is_searchable' => true,
                    'is_required' => false,
                    'placeholder' => 'Bölüm & Oda Sayısı',
                    'help_text' => 'Bölüm & Oda Sayısı',
                    'sort_order' => 1,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 5,
                    'field_label' => 'Dönem',
                    'field_name' => 'period',
                    'field_type' => 'select',
                    'is_searchable' => true,
                    'is_required' => false,
                    'field_options' => json_encode(['1. Hafta', '2. Hafta', '3. Hafta', '4. Hafta', '5. Hafta', '6. Hafta', '7. Hafta', '8. Hafta', '9. Hafta', '10. Hafta', '11. Hafta', '12. Hafta', '13. Hafta', '14. Hafta', '15. Hafta', '16. Hafta', '17. Hafta', '18. Hafta', '19. Hafta', '20. Hafta', '21. Hafta', '22. Hafta', '23. Hafta', '24. Hafta', '25. Hafta', '26. Hafta', '27. Hafta', '28. Hafta', '29. Hafta', '30. Hafta', '31. Hafta', '32. Hafta', '33. Hafta', '34. Hafta', '35. Hafta', '36. Hafta', '37. Hafta', '38. Hafta', '39. Hafta', '40. Hafta', '41. Hafta', '42. Hafta', '43. Hafta', '44. Hafta', '45. Hafta', '46. Hafta', '47. Hafta', '48. Hafta', '49. Hafta', '50. Hafta', '51. Hafta', '52. Hafta', 'Tüm Dönemler']),
                    'placeholder' => 'Dönem',
                    'help_text' => 'Dönem',
                    'sort_order' => 2,
                    'field_group' => 'basic_info'
                ],
                // Turistik Tesis
                [
                    'category_id' => 6,
                    'field_label' => 'Oda Sayısı',
                    'field_name' => 'room_count',
                    'field_type' => 'number',
                    'is_searchable' => true,
                    'is_required' => false,
                    'placeholder' => 'Oda Sayısı',
                    'help_text' => 'Oda Sayısı',
                    'sort_order' => 1,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 6,
                    'field_label' => 'Yatak Sayısı',
                    'field_name' => 'bed_count',
                    'field_type' => 'number',
                    'is_searchable' => true,
                    'is_required' => false,
                    'placeholder' => 'Yatak Sayısı',
                    'help_text' => 'Yatak Sayısı',
                    'sort_order' => 2,
                    'field_group' => 'basic_info'
                ],
                [
                    'category_id' => 6,
                    'field_label' => 'Apart Sayısı',
                    'field_name' => 'apartment_count',
                    'field_type' => 'number',
                    'is_searchable' => true,
                    'is_required' => false,
                    'placeholder' => 'Apart Sayısı',
                    'help_text' => 'Apart Sayısı',
                    'sort_order' => 3,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 6,
                'field_label' => 'Bina Yaşı',
                'field_name' => 'building_age',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    '0','1','2','3','4','5-10 Yaş Arası','11-15 Yaş Arası','16-20 Yaş Arası','21-25 Yaş Arası','26-30 Yaş Arası','31 Yaş ve Üzeri'
                    ]),
                    'placeholder' => 'Bina yaşı',
                    'help_text' => 'Bina yaşı',
                    'sort_order' => 4,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 6,
                'field_label' => 'Toplam Kat Sayısı',
                'field_name' => 'total_floors',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    '1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30 ve üzeri'
                    ]),
                    'placeholder' => 'Toplam kat sayısı',
                    'help_text' => 'Toplam kat sayısı',
                    'sort_order' => 5,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 6,
                'field_label' => 'Açık Alan (m2)',
                'field_name' => 'open_area',
                'field_type' => 'number',
                'is_searchable' => false,
                'is_required' => false,
                'placeholder' => 'Açık Alan (m2)',
                'help_text' => 'Açık Alan (m2)',
                'sort_order' => 6,
                'field_group' => 'basic_info'
                ],
                [
                'category_id' => 6,
                'field_label' => 'Kapalı Alan (m2)',
                'field_name' => 'closed_area',
                'field_type' => 'number',
                'is_searchable' => false,
                'is_required' => false,
                'placeholder' => 'Kapalı Alan (m2)',
                'help_text' => 'Kapalı Alan (m2)',
                'sort_order' => 7,
                'field_group' => 'basic_info'
                ],
                [
                'category_id' => 6,
                'field_label' => 'Zemin Etüdü',
                'field_name' => 'flooring',
                'field_type' => 'select',
                'is_searchable' => false,
                'is_required' => false,
                'field_options' => json_encode(['Var','Yok']),
                    'placeholder' => 'Zemin Etüdü',
                    'help_text' => 'Zemin Etüdü',
                    'sort_order' => 8,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 6,
                'field_label' => 'Yıldız Sayısı',
                'field_name' => 'star_count',
                'field_type' => 'select',
                'is_searchable' => false,
                'is_required' => false,
                'field_options' => json_encode(['1','2','3','4','5','Özel Belgeli','Yok']),
                    'placeholder' => 'Yıldız Sayısı',
                    'help_text' => 'Yıldız Sayısı',
                    'sort_order' => 9,
                    'field_group' => 'basic_info'
                ],
                [
                'category_id' => 6,
                'field_label' => 'Yapının Durumu',
                'field_name' => 'building_condition',
                'field_type' => 'select',
                'is_searchable' => true,
                'is_required' => false,
                'field_options' => json_encode([
                    'Yeni', 'İkinci El'
                    ]),
                    'placeholder' => 'Yapının Durumu',
                    'help_text' => 'Yapının Durumu',
                    'sort_order' => 10,
                    'field_group' => 'basic_info'
                ],
        ];
    }
}
