<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // 1. Fiyat Geçmişi
        Schema::create('portfolio_price_history', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->decimal('old_price', 15, 2)->nullable();
            $table->decimal('new_price', 15, 2);
            $table->foreignId('changed_by')->constrained('users');
            $table->timestamps();
            $table->index(['company_id', 'portfolio_id']);
        });

        // 2. Müşteri Geçmişi
        Schema::create('portfolio_customer_history', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('customer_id')->constrained('customers');
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->string('action'); // gösterildi, arandı, ilgilendi vs.
            $table->text('note')->nullable();
            $table->timestamps();
            $table->index(['company_id', 'portfolio_id', 'customer_id'],
        'pch_company_portfolio_customer_idx');
        });

        // 3. Teklif Geçmişi
        Schema::create('portfolio_offer_history', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('customer_id')->constrained('customers');
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->decimal('offer_amount', 15, 2);
            $table->string('status')->default('pending'); // accepted, rejected
            $table->timestamps();
            $table->index(['portfolio_id', 'customer_id'], 'poh_portfolio_offer_idx');
        });

        // 4. Durum Geçmişi
        Schema::create('portfolio_status_history', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->string('old_status')->nullable();
            $table->string('new_status'); // active, sold, rented, archived, inactive
            $table->foreignId('changed_by')->constrained('users');
            $table->timestamps();
            $table->index(['company_id', 'portfolio_id']);
        });

        // 5. Görüntülenmeler
        Schema::create('portfolio_views', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();
            $table->index(['company_id', 'portfolio_id']);
        });

        // 6. Notlar
        Schema::create('portfolio_notes', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users');
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->text('note');
            $table->timestamps();
            $table->index(['company_id', 'portfolio_id']);
        });

        // 7. Aktivite Logları
        Schema::create('portfolio_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users');
            $table->string('action'); // price_updated, note_added, offer_created
            $table->text('details')->nullable();
            $table->timestamps();
            $table->index(['company_id', 'portfolio_id']);
        });

        // 8. Randevular
        Schema::create('portfolio_appointments', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('customer_id')->constrained('customers');
            $table->foreignUuid('company_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users'); // danışman
            $table->timestamp('appointment_date');
            $table->string('status')->default('planned'); // planned, done, canceled
            $table->text('note')->nullable();
            $table->timestamps();
            $table->index(['company_id', 'portfolio_id', 'customer_id'],
        'pch_company_portfolio_appointments_idx');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('portfolio_appointments');
        Schema::dropIfExists('portfolio_activity_logs');
        Schema::dropIfExists('portfolio_notes');
        Schema::dropIfExists('portfolio_views');
        Schema::dropIfExists('portfolio_status_history');
        Schema::dropIfExists('portfolio_offer_history');
        Schema::dropIfExists('portfolio_customer_history');
        Schema::dropIfExists('portfolio_price_history');
    }
};
