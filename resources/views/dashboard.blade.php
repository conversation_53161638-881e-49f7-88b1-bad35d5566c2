<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-30">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <!-- <PERSON> Header Section -->
            <div class="mb-12">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
                    <!-- Welcome Section -->
                    <div class="space-y-4">
                        <div class="flex items-center gap-4">

                            <div>
                                <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                                    Hoş geldin, {{ auth()->user()->name }}!
                                </h1>
                                <p class="text-gray-600 text-lg mt-1 flex items-center gap-2">
                                    <svg class="w-5 h-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                                    </svg>
                                    {{ \Carbon\Carbon::now()->locale('tr')->isoFormat('DD MMMM YYYY') }} • {{ \Carbon\Carbon::now()->locale('tr')->isoFormat('dddd') }}
                                </p>
                            </div>
                        </div>

                        <!-- Quick Stats Bar -->
                        <div class="flex items-center gap-6 text-sm">
                            <div class="flex items-center gap-2 px-3 py-2 bg-white rounded-full border border-gray-200 shadow-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-gray-700 font-medium">23 aktif işlem</span>
                            </div>
                            <div class="flex items-center gap-2 px-3 py-2 bg-white rounded-full border border-gray-200 shadow-sm">
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span class="text-gray-700 font-medium">186 portföy</span>
                            </div>
                            <div class="flex items-center gap-2 px-3 py-2 bg-white rounded-full border border-gray-200 shadow-sm">
                                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                <span class="text-gray-700 font-medium">₺824K bu ay</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Center -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <!-- Time Filter -->
                        <div class="relative">
                            <select class="appearance-none bg-white border border-gray-300 rounded-xl px-4 py-3 pr-10 text-sm font-medium text-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option>Son 7 gün</option>
                                <option>Son 30 gün</option>
                                <option>Son 3 ay</option>
                                <option>Bu yıl</option>
                            </select>
                            <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                            </svg>
                        </div>

                        <!-- Quick Actions -->
                        <div class="flex gap-3">
                            <button class="group relative bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="relative flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                    </svg>
                                    Yeni İlan
                                </div>
                            </button>

                            <div class="dropdown" data-dropdown>
                                <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false" class="group bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                                    <div class="flex items-center gap-2">
                                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                        </svg>
                                        Raporlar
                                    </div>
                                </button>
                                <div class="dropdown-content bg-white rounded-xl shadow-2xl border border-gray-200 w-56 py-3">
                                    <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">📊</span> Detaylı Rapor
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">💰</span> Satış Analizi
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">👥</span> Müşteri Raporu
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">🏠</span> Portföy Raporu
                                    </a>
                                    <div class="border-t border-gray-200 my-2 mx-2"></div>
                                    <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">📄</span> Excel İndir
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Bento Grid Stats -->
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 lg:gap-6 mb-8 lg:mb-12">
                <!-- Toplam Müşteri Card -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 overflow-hidden">
                    <!-- Animated Background -->
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-7 h-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                </svg>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <h3 class="text-sm font-medium text-gray-600 uppercase tracking-wider">Toplam Müşteri</h3>
                            <div class="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">{{ number_format($stats['total_customers']['value']) }}</div>
                            <div class="flex items-center gap-2">
                                <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold
                                    @if($stats['total_customers']['trend'] === 'up') bg-emerald-100 text-emerald-700 border border-emerald-200
                                    @elseif($stats['total_customers']['trend'] === 'down') bg-red-100 text-red-700 border border-red-200
                                    @else bg-gray-100 text-gray-700 border border-gray-200 @endif">
                                    @if($stats['total_customers']['trend'] === 'up')
                                        <svg class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
                                        </svg>
                                        +{{ $stats['total_customers']['change'] }}%
                                    @elseif($stats['total_customers']['trend'] === 'down')
                                        <svg class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z" clip-rule="evenodd" />
                                        </svg>
                                        {{ $stats['total_customers']['change'] }}%
                                    @else
                                        <svg class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M3 10a.75.75 0 01.75-.75h10.5a.75.75 0 010 1.5H3.75A.75.75 0 013 10z" clip-rule="evenodd" />
                                        </svg>
                                        {{ $stats['total_customers']['change'] }}%
                                    @endif
                                </div>
                                <span class="text-xs text-gray-500 font-medium">Son 30 gün</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bu Ayki Satış Card -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-transparent to-green-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-7 h-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="dropdown" data-dropdown>
                                <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-xl transition-all duration-200">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                                    </svg>
                                </button>
                                <div class="dropdown-content bg-white rounded-xl shadow-2xl border border-gray-200 w-52 py-2">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">📊</span> Satış Raporları
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">📈</span> Trend Analizi
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">🎯</span> Hedef Belirleme
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <h3 class="text-sm font-medium text-gray-600 uppercase tracking-wider">Bu Ayki Satış</h3>
                            <div class="text-3xl font-bold text-gray-900 group-hover:text-emerald-600 transition-colors duration-300">₺{{ number_format($stats['monthly_sales']['value']) }}</div>
                            <div class="flex items-center gap-2">
                                <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold
                                    @if($stats['monthly_sales']['trend'] === 'up') bg-emerald-100 text-emerald-700 border border-emerald-200
                                    @elseif($stats['monthly_sales']['trend'] === 'down') bg-red-100 text-red-700 border border-red-200
                                    @else bg-gray-100 text-gray-700 border border-gray-200 @endif">
                                    @if($stats['monthly_sales']['trend'] === 'up')
                                        <svg class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
                                        </svg>
                                        +{{ $stats['monthly_sales']['change'] }}%
                                    @else
                                        {{ $stats['monthly_sales']['change'] }}%
                                    @endif
                                </div>
                                <span class="text-xs text-gray-500 font-medium">Geçen aya göre</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aylık Hedef Card -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-7 h-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 01-.982-3.172M9.497 14.25a7.454 7.454 0 00.981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 007.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 002.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.228a25.628 25.628 0 012.916.52 6.003 6.003 0 00-5.395 4.972m0 0a6.726 6.726 0 01-2.749 1.35m0 0a6.772 6.772 0 01-3.044 0" />
                                </svg>
                            </div>
                            <div class="dropdown" data-dropdown>
                                <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-xl transition-all duration-200">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                                    </svg>
                                </button>
                                <div class="dropdown-content bg-white rounded-xl shadow-2xl border border-gray-200 w-52 py-2">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">🎯</span> Hedef Güncelle
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">📊</span> İlerleme Raporu
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">📈</span> Geçmiş Hedefler
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <h3 class="text-sm font-medium text-gray-600 uppercase tracking-wider">Aylık Hedef</h3>
                            <div class="text-3xl font-bold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">₺1.2M</div>
                            <div class="flex items-center gap-2">
                                <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-700 border border-blue-200">
                                    68% tamamlandı
                                </div>
                                <span class="text-xs text-gray-500 font-medium">12 gün kaldı</span>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mt-4">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-indigo-500 to-blue-600 h-2 rounded-full transition-all duration-1000 ease-out" style="width: 68%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Toplam Görüntüleme Card -->
                <div class="group relative bg-white border border-gray-200 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 overflow-hidden lg:col-span-2">
                    <div class="absolute inset-0 bg-gradient-to-br from-orange-500/10 via-transparent to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-7 h-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <div class="dropdown" data-dropdown>
                                <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-xl transition-all duration-200">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                                    </svg>
                                </button>
                                <div class="dropdown-content bg-white rounded-xl shadow-2xl border border-gray-200 w-52 py-2">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">👁️</span> Görüntüleme Detayları
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">📊</span> Trafik Analizi
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-colors rounded-lg mx-2">
                                        <span class="text-lg mr-3">🔍</span> SEO Raporu
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <h3 class="text-sm font-medium text-gray-600 uppercase tracking-wider">Toplam Görüntüleme</h3>
                                <div class="text-4xl font-bold text-gray-900 group-hover:text-orange-600 transition-colors duration-300">45.2K</div>
                                <div class="flex items-center gap-2">
                                    <div class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-700 border border-emerald-200">
                                        <svg class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
                                        </svg>
                                        +24%
                                    </div>
                                    <span class="text-xs text-gray-500 font-medium">Bu hafta</span>
                                </div>
                            </div>

                            <!-- Mini Chart Placeholder -->
                            <div class="flex items-center justify-center bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-4">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-2">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z" />
                                        </svg>
                                    </div>
                                    <p class="text-xs text-gray-600 font-medium">Trend Grafiği</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Interactive Dashboard -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                <!-- Interactive Chart Area -->
                <div class="lg:col-span-2">
                    <div class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                        <!-- Animated Background -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-between mb-8">
                                <div class="flex items-center gap-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-2xl font-bold text-gray-900">Satış Performansı</h2>
                                        <p class="text-gray-600 text-sm">Gerçek zamanlı analiz ve trendler</p>
                                    </div>
                                </div>

                                <div class="flex items-center gap-3">
                                    <div class="relative">
                                        <select class="appearance-none bg-white border border-gray-300 rounded-xl px-4 py-2 pr-10 text-sm font-medium text-gray-700 shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option>Bu Ay</option>
                                            <option>Son 3 Ay</option>
                                            <option>Bu Yıl</option>
                                        </select>
                                        <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                        </svg>
                                    </div>

                                    <div class="dropdown" data-dropdown>
                                        <button type="button" data-dropdown-trigger aria-haspopup="true" aria-expanded="false" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-xl transition-all duration-200">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                                            </svg>
                                        </button>
                                        <div class="dropdown-content bg-white rounded-xl shadow-2xl border border-gray-200 w-52 py-2">
                                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors rounded-lg mx-2">
                                                <span class="text-lg mr-3">📊</span> Detaylı Görünüm
                                            </a>
                                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-colors rounded-lg mx-2">
                                                <span class="text-lg mr-3">📈</span> Trend Analizi
                                            </a>
                                            <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 transition-colors rounded-lg mx-2">
                                                <span class="text-lg mr-3">💾</span> Veriyi İndir
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Interactive Chart Container -->
                            <div class="relative">
                                <div class="bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-4 border border-white/20 shadow-inner">
                                    <canvas id="salesChart" class="w-full h-80"></canvas>
                                </div>

                                <!-- Chart Legend -->
                                <div class="flex items-center justify-center gap-6 mt-6">
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600 font-medium">Satışlar</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 bg-emerald-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600 font-medium">Hedefler</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600 font-medium">Trendler</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Activity Timeline -->
                <div class="group relative bg-white border border-gray-200 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                    <!-- Animated Background -->
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                    <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-lg font-bold text-gray-900">Canlı Aktiviteler</h2>
                                    <p class="text-xs text-gray-600">Gerçek zamanlı güncellemeler</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-emerald-100 to-green-100 rounded-full border border-emerald-200">
                                <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                                <span class="text-xs font-semibold text-emerald-700">Canlı</span>
                            </div>
                        </div>

                        <!-- Modern Activity List -->
                        <div class="space-y-3">
                            <!-- Activity Item 1 -->
                            <div class="group/item relative bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-2xl p-4 border border-blue-200/50 hover:shadow-md transition-all duration-300">
                                <div class="flex items-center gap-4">
                                    <div class="relative">
                                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl flex items-center justify-center text-xs font-bold shadow-lg">
                                            AY
                                        </div>
                                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-blue-600 rounded-full animate-ping"></div>
                                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-blue-600 rounded-full"></div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-gray-900">Yeni müşteri eklendi</p>
                                        <p class="text-xs text-gray-600 flex items-center gap-1">
                                            <span class="font-medium">Ahmet Yılmaz</span>
                                            <span>•</span>
                                            <span class="text-blue-600 font-medium">2s önce</span>
                                        </p>
                                    </div>
                                    <div class="opacity-0 group-hover/item:opacity-100 transition-opacity">
                                        <button class="p-1.5 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-white/50 transition-colors">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item 2 -->
                            <div class="group/item relative bg-gradient-to-r from-emerald-50 to-green-100/50 rounded-2xl p-4 border border-emerald-200/50 hover:shadow-md transition-all duration-300">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 text-white rounded-xl flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 21v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21m0 0h4.5V3.545M12.75 21h7.5V10.75M2.25 21h1.5m18 0h-18M2.25 9l4.5-1.636M18.75 3l-1.5.545m0 6.205l3 1.09m-2.25-1.09v2.25m-2.25-1.09l.75 2.7L9 16.5m4.5-1.636l1.5-.545" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-gray-900">Villa satışı tamamlandı</p>
                                        <p class="text-xs text-gray-600 flex items-center gap-1">
                                            <span class="font-bold text-emerald-600">₺850,000</span>
                                            <span>•</span>
                                            <span class="text-emerald-600 font-medium">5dk önce</span>
                                        </p>
                                    </div>
                                    <div class="opacity-0 group-hover/item:opacity-100 transition-opacity">
                                        <button class="p-1.5 text-gray-400 hover:text-emerald-600 rounded-lg hover:bg-white/50 transition-colors">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item 3 -->
                            <div class="group/item relative bg-gradient-to-r from-amber-50 to-yellow-100/50 rounded-2xl p-4 border border-amber-200/50 hover:shadow-md transition-all duration-300">
                                <div class="flex items-center gap-4">
                                    <div class="relative w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 text-white rounded-xl flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                                        </svg>
                                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-gray-900">Daire ilanı onay bekliyor</p>
                                        <p class="text-xs text-gray-600 flex items-center gap-1">
                                            <span class="font-medium">Bahçelievler</span>
                                            <span>•</span>
                                            <span class="text-amber-600 font-medium">1s önce</span>
                                        </p>
                                    </div>
                                    <div class="opacity-0 group-hover/item:opacity-100 transition-opacity">
                                        <button class="p-1.5 text-gray-400 hover:text-amber-600 rounded-lg hover:bg-white/50 transition-colors">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item 4 -->
                            <div class="group/item relative bg-gradient-to-r from-indigo-50 to-purple-100/50 rounded-2xl p-4 border border-indigo-200/50 hover:shadow-md transition-all duration-300">
                                <div class="flex items-center gap-4">
                                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 text-white rounded-xl flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-gray-900">Müşteri araması</p>
                                        <p class="text-xs text-gray-600 flex items-center gap-1">
                                            <span class="font-medium">Melis Kaya</span>
                                            <span>•</span>
                                            <span class="text-indigo-600 font-medium">2dk önce</span>
                                        </p>
                                    </div>
                                    <div class="opacity-0 group-hover/item:opacity-100 transition-opacity">
                                        <button class="p-1.5 text-gray-400 hover:text-indigo-600 rounded-lg hover:bg-white/50 transition-colors">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Button -->
                        <div class="mt-6 pt-4 border-t border-gray-200/50">
                            <button class="group w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white px-4 py-3 rounded-2xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                <div class="flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Tüm Aktiviteleri Gör</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Yaklaşan Görevler ve Akıllı Eşleşme -->
            <div class="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-12">
                <!-- Yaklaşan Görevler -->
                <div class="xl:col-span-1">
                    <div class="group relative bg-white border border-gray-200 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden h-full">
                        <div class="absolute inset-0 bg-gradient-to-br from-amber-500/5 via-transparent to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <div class="relative z-10">
                            <div class="flex items-center gap-4 mb-6">
                                <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg">
                                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900">Yaklaşan Görevler</h2>
                                    <p class="text-gray-600 text-sm">Bugün ve yakında yapılacaklar</p>
                                </div>
                            </div>

                            <div class="space-y-4 max-h-96 overflow-y-auto">
                                @foreach($upcomingTasks as $task)
                                <div class="group/task relative bg-gradient-to-r
                                    @if($task['priority'] === 'high') from-red-50 to-red-100/50 border-red-200/50
                                    @elseif($task['priority'] === 'medium') from-yellow-50 to-yellow-100/50 border-yellow-200/50
                                    @else from-blue-50 to-blue-100/50 border-blue-200/50 @endif
                                    rounded-2xl p-4 border hover:shadow-md transition-all duration-300">

                                    <div class="flex items-start gap-3">
                                        <div class="relative">
                                            <div class="w-10 h-10
                                                @if($task['priority'] === 'high') bg-gradient-to-br from-red-500 to-red-600
                                                @elseif($task['priority'] === 'medium') bg-gradient-to-br from-yellow-500 to-orange-600
                                                @else bg-gradient-to-br from-blue-500 to-blue-600 @endif
                                                text-white rounded-xl flex items-center justify-center shadow-lg">
                                                @if($task['type'] === 'meeting')
                                                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                                    </svg>
                                                @elseif($task['type'] === 'contract')
                                                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                                    </svg>
                                                @elseif($task['type'] === 'photography')
                                                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.827 6.175A2.31 2.31 0 015.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 00-1.134-.175 2.31 2.31 0 01-1.64-1.055l-.822-1.316a2.192 2.192 0 00-1.736-1.039 48.774 48.774 0 00-5.232 0 2.192 2.192 0 00-1.736 1.039l-.821 1.316z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 12.75a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0zM18.75 10.5h.008v.008h-.008V10.5z" />
                                                    </svg>
                                                @else
                                                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                                    </svg>
                                                @endif
                                            </div>
                                            @if($task['priority'] === 'high')
                                                <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                            @endif
                                        </div>

                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center justify-between mb-1">
                                                <h3 class="text-sm font-semibold text-gray-900 truncate">{{ $task['title'] }}</h3>
                                                <span class="text-xs
                                                    @if($task['priority'] === 'high') text-red-600 font-bold
                                                    @elseif($task['priority'] === 'medium') text-yellow-600 font-semibold
                                                    @else text-blue-600 @endif">
                                                    {{ $task['due_date']->diffForHumans() }}
                                                </span>
                                            </div>
                                            <p class="text-xs text-gray-600 mb-2">{{ $task['description'] }}</p>

                                            @if(isset($task['customer']))
                                                <div class="flex items-center gap-2 text-xs">
                                                    <span class="px-2 py-1 bg-white/70 rounded-lg font-medium text-gray-700">
                                                        👤 {{ $task['customer'] }}
                                                    </span>
                                                    @if(isset($task['property']))
                                                        <span class="px-2 py-1 bg-white/70 rounded-lg font-medium text-gray-700">
                                                            🏠 {{ $task['property'] }}
                                                        </span>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>

                                        <div class="opacity-0 group-hover/task:opacity-100 transition-opacity">
                                            <button class="p-1.5 text-gray-400 hover:text-green-600 rounded-lg hover:bg-white/50 transition-colors">
                                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <div class="mt-6 pt-4 border-t border-gray-200/50">
                                <button class="w-full bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white px-4 py-3 rounded-2xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                    <div class="flex items-center justify-center gap-2">
                                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                        </svg>
                                        <span>Yeni Görev Ekle</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Akıllı Eşleşme Panosu -->
                <div class="xl:col-span-2">
                    <div class="group relative bg-white border border-gray-200 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden h-full">
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <div class="relative z-10">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center gap-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-xl font-bold text-gray-900">Akıllı Eşleşme</h2>
                                        <p class="text-gray-600 text-sm">AI destekli müşteri-emlak eşleştirme</p>
                                    </div>
                                </div>

                                <div class="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full border border-green-200">
                                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="text-xs font-semibold text-green-700">{{ count($smartMatches) }} Eşleşme</span>
                                </div>
                            </div>

                            <div class="space-y-4 max-h-96 overflow-y-auto">
                                @foreach($smartMatches as $match)
                                <div class="group/match relative bg-gradient-to-r from-purple-50 to-pink-100/50 rounded-2xl p-4 border border-purple-200/50 hover:shadow-md transition-all duration-300">
                                    <div class="flex items-start gap-4">
                                        <!-- Match Score -->
                                        <div class="relative">
                                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                <div class="text-center">
                                                    <div class="text-lg font-bold text-white">{{ $match['match_score'] }}%</div>
                                                    <div class="text-xs text-purple-100">Uyum</div>
                                                </div>
                                            </div>
                                            <div class="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                                </svg>
                                            </div>
                                        </div>

                                        <!-- Match Details -->
                                        <div class="flex-1 min-w-0">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <!-- Customer Info -->
                                                <div class="bg-white/70 rounded-xl p-3 border border-white/50">
                                                    <div class="flex items-center gap-2 mb-2">
                                                        <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                                                            <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                                            </svg>
                                                        </div>
                                                        <h4 class="font-semibold text-gray-900 text-sm">{{ $match['customer']['name'] }}</h4>
                                                    </div>
                                                    <div class="text-xs text-gray-600 space-y-1">
                                                        <div>💰 ₺{{ number_format(explode('-', $match['customer']['budget'])[0]) }} - ₺{{ number_format(explode('-', $match['customer']['budget'])[1]) }}</div>
                                                        <div class="flex flex-wrap gap-1">
                                                            @foreach($match['customer']['preferences'] as $pref)
                                                                <span class="px-2 py-0.5 bg-blue-100 text-blue-700 rounded-lg text-xs">{{ $pref }}</span>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Property Info -->
                                                <div class="bg-white/70 rounded-xl p-3 border border-white/50">
                                                    <div class="flex items-center gap-2 mb-2">
                                                        <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                                                            <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                                            </svg>
                                                        </div>
                                                        <h4 class="font-semibold text-gray-900 text-sm">{{ $match['property']['title'] }}</h4>
                                                    </div>
                                                    <div class="text-xs text-gray-600 space-y-1">
                                                        <div>💰 ₺{{ number_format($match['property']['price']) }}</div>
                                                        <div class="flex flex-wrap gap-1">
                                                            @foreach($match['property']['features'] as $feature)
                                                                <span class="px-2 py-0.5 bg-green-100 text-green-700 rounded-lg text-xs">{{ $feature }}</span>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Match Reasons -->
                                            <div class="mt-3 p-2 bg-white/50 rounded-lg border border-white/30">
                                                <div class="text-xs text-gray-600 mb-1">Eşleşme Nedenleri:</div>
                                                <div class="flex flex-wrap gap-1">
                                                    @foreach($match['reasons'] as $reason)
                                                        <span class="px-2 py-0.5 bg-purple-100 text-purple-700 rounded-lg text-xs">✓ {{ $reason }}</span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex flex-col gap-2 opacity-0 group-hover/match:opacity-100 transition-opacity">
                                            <button class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors" title="Eşleştir">
                                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
                                                </svg>
                                            </button>
                                            <button class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="Detaylar">
                                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <div class="mt-6 pt-4 border-t border-gray-200/50">
                                <button class="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white px-4 py-3 rounded-2xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                    <div class="flex items-center justify-center gap-2">
                                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                                        </svg>
                                        <span>Tüm Eşleşmeleri Gör</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Analytics Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Performance Insights -->
                <div class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center gap-4 mb-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.042 21.672L13.684 16.6m0 0l-2.51 2.225.569-9.47 5.227 7.917-3.286-.672zM12 2.25V4.5m5.834.166l-1.591 1.591M20.25 10.5H18M7.757 14.743l-1.59 1.59M6 10.5H3.75m4.007-4.243l-1.59-1.59" />
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-gray-900">Performans İçgörüleri</h2>
                                <p class="text-gray-600 text-sm">AI destekli analiz ve öneriler</p>
                            </div>
                        </div>

                        <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-200/50">
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-orange-600 mb-1">92%</div>
                                    <div class="text-xs text-gray-600 font-medium">Verimlilik</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-red-600 mb-1">+18%</div>
                                    <div class="text-xs text-gray-600 font-medium">Büyüme</div>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Satış Hedefi</span>
                                    <span class="font-semibold text-gray-900">68%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-orange-500 to-red-600 h-2 rounded-full transition-all duration-1000 ease-out" style="width: 68%"></div>
                                </div>
                            </div>

                            <div class="mt-4 p-3 bg-white/70 rounded-xl border border-white/50">
                                <div class="flex items-center gap-2 text-xs text-gray-600">
                                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="font-medium">AI Önerisi:</span>
                                    <span>Pazarlama bütçesini %15 artırın</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Market Intelligence -->
                <div class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center gap-4 mb-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3" />
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-gray-900">Pazar İçgörüleri</h2>
                                <p class="text-gray-600 text-sm">AI destekli pazar analizi</p>
                            </div>
                        </div>

                        <div class="bg-gradient-to-br from-violet-50 to-purple-50 rounded-2xl p-6 border border-violet-200/50">
                            <div class="space-y-6">
                                <!-- Trending Areas -->
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                        <svg class="w-4 h-4 text-violet-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 010 0L21.75 9M21.75 9H15M21.75 9v6.75" />
                                        </svg>
                                        Trend Bölgeler
                                    </h3>
                                    <div class="space-y-2">
                                        @foreach($marketInsights['trending_areas'] as $area)
                                        <div class="flex items-center justify-between p-3 bg-white/70 rounded-xl border border-white/50">
                                            <div class="flex items-center gap-3">
                                                <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                                    <span class="text-white text-xs font-bold">{{ substr($area['name'], 0, 2) }}</span>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-semibold text-gray-900">{{ $area['name'] }}</div>
                                                    <div class="text-xs text-gray-600">Ort. ₺{{ number_format($area['avg_price']) }}</div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-sm font-bold text-green-600">+{{ $area['growth'] }}%</div>
                                                <div class="text-xs text-gray-500">Büyüme</div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>

                                <!-- Price Predictions -->
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                        <svg class="w-4 h-4 text-violet-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75-7.478a12.06 12.06 0 004.5 0m-3.75 7.478a12.06 12.06 0 010-4.5m3.75 2.25a12.06 12.06 0 010 4.5m-3.75-7.478a12.06 12.06 0 010 4.5m0 0a6.01 6.01 0 009.75 0m-9.75 0a6.01 6.01 0 00-9.75 0" />
                                        </svg>
                                        Fiyat Tahminleri
                                    </h3>
                                    <div class="grid grid-cols-3 gap-3">
                                        <div class="text-center p-3 bg-white/70 rounded-xl border border-white/50">
                                            <div class="text-lg font-bold text-blue-600">+{{ $marketInsights['price_predictions']['next_month'] }}%</div>
                                            <div class="text-xs text-gray-600">Gelecek Ay</div>
                                        </div>
                                        <div class="text-center p-3 bg-white/70 rounded-xl border border-white/50">
                                            <div class="text-lg font-bold text-purple-600">+{{ $marketInsights['price_predictions']['next_quarter'] }}%</div>
                                            <div class="text-xs text-gray-600">Çeyrek</div>
                                        </div>
                                        <div class="text-center p-3 bg-white/70 rounded-xl border border-white/50">
                                            <div class="text-lg font-bold text-green-600">+{{ $marketInsights['price_predictions']['next_year'] }}%</div>
                                            <div class="text-xs text-gray-600">Yıllık</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Market Temperature -->
                                <div class="flex items-center justify-between p-4 bg-white/70 rounded-xl border border-white/50">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10
                                            @if($marketInsights['market_temperature'] === 'hot') bg-gradient-to-br from-red-500 to-orange-600
                                            @elseif($marketInsights['market_temperature'] === 'warm') bg-gradient-to-br from-yellow-500 to-orange-600
                                            @elseif($marketInsights['market_temperature'] === 'cool') bg-gradient-to-br from-blue-500 to-blue-600
                                            @else bg-gradient-to-br from-gray-500 to-gray-600 @endif
                                            rounded-xl flex items-center justify-center shadow-lg">
                                            @if($marketInsights['market_temperature'] === 'hot')
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.362 5.214A8.252 8.252 0 0112 21 8.25 8.25 0 016.038 7.048 8.287 8.287 0 009 9.6a8.983 8.983 0 013.361-6.867 8.21 8.21 0 003 2.48z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 18a3.75 3.75 0 00.495-7.467 5.99 5.99 0 00-1.925 3.546 5.974 5.974 0 01-2.133-1A3.75 3.75 0 0012 18z" />
                                                </svg>
                                            @else
                                                <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-6.364-.386l1.591-1.591M3 12h2.25m.386-6.364l1.591 1.591" />
                                                </svg>
                                            @endif
                                        </div>
                                        <div>
                                            <div class="text-sm font-semibold text-gray-900">Pazar Sıcaklığı</div>
                                            <div class="text-xs text-gray-600 capitalize">
                                                @if($marketInsights['market_temperature'] === 'hot') Çok Sıcak 🔥
                                                @elseif($marketInsights['market_temperature'] === 'warm') Sıcak ☀️
                                                @elseif($marketInsights['market_temperature'] === 'cool') Soğuk ❄️
                                                @else Durgun 😴 @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold
                                            @if($marketInsights['market_temperature'] === 'hot') text-red-600
                                            @elseif($marketInsights['market_temperature'] === 'warm') text-yellow-600
                                            @elseif($marketInsights['market_temperature'] === 'cool') text-blue-600
                                            @else text-gray-600 @endif">
                                            {{ ucfirst($marketInsights['market_temperature']) }}
                                        </div>
                                        <div class="text-xs text-gray-500">Durum</div>
                                    </div>
                                </div>

                                <!-- Best Investment Types -->
                                <div>
                                    <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                        <svg class="w-4 h-4 text-violet-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 01-.982-3.172M9.497 14.25a7.454 7.454 0 00.981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 007.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 002.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.228a25.628 25.628 0 012.916.52 6.003 6.003 0 00-5.395 4.972m0 0a6.726 6.726 0 01-2.749 1.35m0 0a6.772 6.772 0 01-3.044 0" />
                                        </svg>
                                        En İyi Yatırım Tipleri
                                    </h3>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach($marketInsights['best_investment_types'] as $type)
                                        <span class="px-3 py-2 bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 rounded-xl text-sm font-medium border border-violet-200">
                                            🏆 {{ $type }}
                                        </span>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: {!! json_encode($chartData['labels']) !!},
                datasets: {!! json_encode($chartData['datasets']) !!}
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#1f2937',
                        bodyColor: '#374151',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        cornerRadius: 12,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ₺' + context.parsed.y.toLocaleString('tr-TR');
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(156, 163, 175, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            callback: function(value) {
                                return '₺' + (value / 1000) + 'K';
                            },
                            color: '#6b7280',
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 4,
                        hoverRadius: 6,
                        borderWidth: 2,
                        hoverBorderWidth: 3
                    },
                    line: {
                        borderWidth: 3,
                        fill: true
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        // Real-time updates simulation
        setInterval(() => {
            // Simulate real-time data updates
            const lastValue = salesChart.data.datasets[0].data[salesChart.data.datasets[0].data.length - 1];
            const newValue = lastValue + (Math.random() - 0.5) * 50000;

            // Update current month data
            salesChart.data.datasets[0].data[salesChart.data.datasets[0].data.length - 1] = Math.max(0, newValue);
            salesChart.update('none');
        }, 5000);

        // Dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const dropdowns = document.querySelectorAll('[data-dropdown]');

            dropdowns.forEach(dropdown => {
                const trigger = dropdown.querySelector('[data-dropdown-trigger]');
                const content = dropdown.querySelector('.dropdown-content');

                trigger.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // Close other dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.querySelector('.dropdown-content').classList.add('hidden');
                        }
                    });

                    // Toggle current dropdown
                    content.classList.toggle('hidden');
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
                dropdowns.forEach(dropdown => {
                    dropdown.querySelector('.dropdown-content').classList.add('hidden');
                });
            });
        });

        // Animate counters on page load
        function animateCounter(element, target, duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (element.textContent.includes('₺')) {
                    element.textContent = '₺' + Math.floor(current).toLocaleString('tr-TR');
                } else if (element.textContent.includes('%')) {
                    element.textContent = Math.floor(current) + '%';
                } else {
                    element.textContent = Math.floor(current).toLocaleString('tr-TR');
                }
            }, 16);
        }

        // Real-time updates with activity feed
        let activityUpdateInterval;

        function updateActivities() {
            fetch('/api/activities')
                .then(response => response.json())
                .then(data => {
                    console.log('Activities updated:', data.activities);
                })
                .catch(error => console.error('Error updating activities:', error));
        }

        // Chart real-time simulation
        function updateChart() {
            const lastValue = salesChart.data.datasets[0].data[salesChart.data.datasets[0].data.length - 1];
            const newValue = lastValue + (Math.random() - 0.5) * 50000;

            salesChart.data.datasets[0].data[salesChart.data.datasets[0].data.length - 1] = Math.max(0, newValue);
            salesChart.update('none');
        }

        // Initialize counter animations
        document.addEventListener('DOMContentLoaded', function() {
            // Add counter animation to stat cards
            setTimeout(() => {
                const statElements = document.querySelectorAll('.text-3xl.font-bold, .text-4xl.font-bold');
                statElements.forEach(element => {
                    const text = element.textContent;
                    const number = parseInt(text.replace(/[^\d]/g, ''));
                    if (number && number > 0) {
                        element.textContent = text.includes('₺') ? '₺0' : '0';
                        animateCounter(element, number);
                    }
                });
            }, 500);

            // Start real-time updates
            activityUpdateInterval = setInterval(updateActivities, 30000);
            setInterval(updateChart, 10000);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (activityUpdateInterval) {
                clearInterval(activityUpdateInterval);
            }
        });
    </script>
    @endpush
    @endsection
</x-backend-layout>
