<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Aksiyonlar & Görevler</h1>
                        <p class="text-gray-600 mt-2">{{ $portfolio->title }} - Yapılacaklar Listesi</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <a href="{{ route('portfolios.index') }}" 
                           class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                            ← Geri Dön
                        </a>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            + <PERSON><PERSON>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-xl">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Aksiyon Listesi</h3>
                
                <div class="space-y-4">
                    <div class="p-4 bg-red-50 rounded-xl border border-red-200">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mt-1">
                                    <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">Ahmet Bey'i Ara</h4>
                                    <p class="text-sm text-gray-600 mt-1">Teklif konusunda geri dönüş almak için aranacak.</p>
                                    <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                        <span>Atanan: Ali Veli</span>
                                        <span>Son Tarih: Bugün 17:00</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="px-2 py-1 bg-red-200 text-red-800 rounded-full text-xs">Acil</span>
                                <button class="p-1 text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mt-1">
                                    <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">Fotoğraf Çekimi</h4>
                                    <p class="text-sm text-gray-600 mt-1">Yeni çekilen fotoğrafları sisteme yükle.</p>
                                    <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                        <span>Atanan: Mehmet Yılmaz</span>
                                        <span>Son Tarih: Yarın</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="px-2 py-1 bg-yellow-200 text-yellow-800 rounded-full text-xs">Orta</span>
                                <button class="p-1 text-gray-400 hover:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 bg-green-50 rounded-xl border border-green-200">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-1">
                                    <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">İlan Metni Güncelle</h4>
                                    <p class="text-sm text-gray-600 mt-1">Web sitesindeki ilan açıklaması güncellenecek.</p>
                                    <div class="flex items-center gap-4 mt-2 text-xs text-gray-500">
                                        <span>Atanan: Fatma Demir</span>
                                        <span>Tamamlandı: 2 gün önce</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="px-2 py-1 bg-green-200 text-green-800 rounded-full text-xs">Tamamlandı</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection
</x-backend-layout>
