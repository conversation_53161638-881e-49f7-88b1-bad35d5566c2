<?php

namespace Modules\Customer\App\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OccupationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
{
    $occupations = [
        '<PERSON><PERSON><PERSON>',
        '<PERSON>ka<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON>zel Sektör',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>eb<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        'Serbest Meslek',
        'Aktör/Aktris',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>'
    ];

    foreach ($occupations as $occupation) {
        DB::table('customers_occupations')->insert([
            'name' => $occupation,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}

}
