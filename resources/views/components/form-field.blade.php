@props([
    'label' => null,
    'name' => null,
    'type' => 'text',
    'required' => false,
    'placeholder' => null,
    'value' => null,
    'icon' => null,
    'help' => null,
    'error' => null,
    'options' => [], // For select fields
    'rows' => 3, // For textarea
])

<div>
    @if($label)
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-2">
        {{ $label }}
        @if($required)
        <span class="text-red-500">*</span>
        @endif
    </label>
    @endif

    <div class="relative">
        @if($icon)
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div class="h-5 w-5 text-gray-400">
                {!! $icon !!}
            </div>
        </div>
        @endif

        @if($type === 'textarea')
        <textarea
            id="{{ $name }}"
            name="{{ $name }}"
            rows="{{ $rows }}"
            @if($placeholder) placeholder="{{ $placeholder }}" @endif
            @if($required) required @endif
            class="form-input {{ $icon ? 'pl-10' : '' }} {{ $errors && $errors->has($name) ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '' }}"
        >{{ old($name, $value) }}</textarea>

        @elseif($type === 'select')
        <select
            id="{{ $name }}"
            name="{{ $name }}"
            @if($required) required @endif
            class="form-select {{ $icon ? 'pl-10' : '' }} {{ $errors && $errors->has($name) ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '' }}"
        >
            @if($placeholder)
            <option value="">{{ $placeholder }}</option>
            @endif

            @foreach($options as $optionValue => $optionLabel)
            <option value="{{ $optionValue }}" {{ old($name, $value) == $optionValue ? 'selected' : '' }}>
                {{ $optionLabel }}
            </option>
            @endforeach
        </select>

        @else
        <input
            type="{{ $type }}"
            id="{{ $name }}"
            name="{{ $name }}"
            @if($placeholder) placeholder="{{ $placeholder }}" @endif
            @if($required) required @endif
            value="{{ old($name, $value) }}"
            class="form-input {{ $icon ? 'pl-10' : '' }} {{ $errors && $errors->has($name) ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '' }}"
        >
        @endif
    </div>

    @if($help)
    <p class="mt-2 text-sm text-gray-500">{{ $help }}</p>
    @endif

    @if($errors && $errors->has($name))
    <p class="mt-1 text-sm text-red-600">{{ $errors->first($name) }}</p>
    @endif

    @if($error)
    <p class="mt-1 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>
