<?php

namespace Modules\Portfolios\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Modules\Portfolios\App\Models\Portfolio;
use Modules\Portfolios\App\Models\PortfolioType;
use Modules\Portfolios\App\Models\PortfolioCategory;
use Modules\Portfolios\App\Models\PortfolioPriceHistory;

use App\Models\User;
use App\Models\City;
use Yajra\DataTables\Facades\DataTables;

class PortfoliosController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $companyId = Auth::user()?->company_id;

       if (!$user->hasCompanyPermission($user->company_id, 'view_properties')) {
            abort(403, 'Bu say<PERSON>ya eri<PERSON>im yetkiniz yok.');
        }

        // Filtreleme için gerekli veriler
        $portfolioTypes = PortfolioType::all();
        $portfolioCategories = PortfolioCategory::whereNull('parent_id')->get();
        $agents = User::where('company_id', $companyId)->get();
        $cities = City::all();

        return view('portfolios::index', compact(
            'portfolioTypes',
            'portfolioCategories',
            'agents',
            'cities'
        ));
    }

    /**
     * Card-based listing for portfolios
     */
    public function cards(Request $request)
    {
        $companyId = Auth::user()?->company_id;

        $query = Portfolio::with(['type', 'category','subCategory', 'agent', 'city', 'district', 'neighborhood', 'images', 'priceHistory'])
            ->forCompany($companyId);

        // Filtreleme
        if ($request->filled('status_filter')) {
            $query->where('status', $request->status_filter);
        }

        if ($request->filled('type_filter')) {
            $query->where('type_id', $request->type_filter);
        }

        if ($request->filled('category_filter')) {
            $query->where('category_id', $request->category_filter);
        }

        if ($request->filled('agent_filter')) {
            $query->where('agent_id', $request->agent_filter);
        }

        if ($request->filled('city_filter')) {
            $query->where('city_id', $request->city_filter);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Arama
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('id', 'like', "%{$search}%");
            });
        }

        $portfolios = $query->orderBy('created_at', 'desc')->paginate(20); // 500 portföy için 20 öğe per sayfa

        $cards = [];
        foreach ($portfolios as $portfolio) {
            $mainImage = $portfolio->images->first();
            $imageUrl = $mainImage ? asset('storage/' . $mainImage->image_path) : asset('images/noimage.jpg');

            // Konum bilgisi
            $location = '';
            if ($portfolio->city) {
                $location .= $portfolio->city->il_adi;
            }
            if ($portfolio->district) {
                $location .= ' / ' . $portfolio->district->ilce_adi;
            }
            if ($portfolio->neighborhood) {
                $location .= ' / ' . $portfolio->neighborhood->mahalle_adi;
            }

            // Durum badge'i
            $statusBadge = '';
            $statusColor = '';
            switch($portfolio->status) {
                case 'active':
                    $statusBadge = 'Aktif';
                    $statusColor = 'bg-green-100 text-green-800';
                    break;
                case 'sold':
                    $statusBadge = 'SATIŞ KAPAT';
                    $statusColor = 'bg-red-100 text-red-800';
                    break;
                case 'rented':
                    $statusBadge = 'Kiralık';
                    $statusColor = 'bg-blue-100 text-blue-800';
                    break;
                case 'inactive':
                    $statusBadge = 'Pasif';
                    $statusColor = 'bg-gray-100 text-gray-800';
                    break;
                default:
                    $statusBadge = $portfolio->status_label ?? 'Bilinmiyor';
                    $statusColor = 'bg-gray-100 text-gray-800';
            }

            // Emlak türü formatı: "Satılık / Konut / Daire"
            $propertyCategory = '';

            $propertyType = $portfolio->type_id ? $portfolio->type->name : 'Satılık';

            // Ana kategori
            $mainCategory = $portfolio->main_category_id ? $portfolio->category->name : 'Konut';

            // Alt tip
            $subType = $portfolio->category_id ? $portfolio->subCategory->name : 'Daire';

            $propertyCategory = $propertyType . ' / ' . $mainCategory . ' / ' . $subType;

            // Fiyat değişimi hesaplama - PortfolioPriceHistory modelinden
            $priceHistory = false;
            $priceChange = 0;

            // En son fiyat değişikliğini al
            $latestPriceHistory = $portfolio->priceHistory()->latest()->first();

            if ($latestPriceHistory && $latestPriceHistory->old_price > 0) {
                $currentPrice = $portfolio->price;
                $previousPrice = $latestPriceHistory->old_price;
                $priceChange = round((($currentPrice - $previousPrice) / $previousPrice) * 100, 1);
                $priceHistory = true;
            }


            $cards[] = [
                'id' => $portfolio->id,
                'portfolio_no' => $portfolio->id, // ID'yi portfolio numarası olarak kullan
                'title' => $portfolio->title,
                'price' => $portfolio->formatted_price,
                'price_history' => $priceHistory,
                'price_change' => $priceChange,
                'location' => $location ?: 'Konum Belirtilmemiş',
                'property_type' => $portfolio->type_id ? $portfolio->type->name : 'Tip Belirtilmemiş',
                'property_category' => $propertyCategory, // Yeni alan: "Satılık / Konut / Daire"
                'area_gross' => $portfolio->gross_area ? number_format($portfolio->gross_area, 0, ',', '.') . ' m²' : null,
                'area_net' => $portfolio->net_area ? number_format($portfolio->net_area, 0, ',', '.') . ' m²' : null,
                'rooms' => $portfolio->details['rooms'] ?? ($portfolio->details['room_count'] ?? null), // Details array'inden al
                'status' => $portfolio->status,
                'status_badge' => $statusBadge,
                'status_color' => $statusColor,
                'created_at' => $portfolio->created_at->format('d.m.Y'),
                'agent_name' => $portfolio->agent_id ? $portfolio->agent->name : 'Atanmamış',
                'agent_initial' => $portfolio->agent_id ? substr($portfolio->agent->name, 0, 1) : 'A',
                'image_url' => $imageUrl,
                'images_count' => $portfolio->images->count(),
                'view_count' => rand(10, 500), // Geçici olarak rastgele sayı
                'view_url' => route('portfolios.show', $portfolio->id),
                'edit_url' => route('portfolios.edit', $portfolio->id),
            ];
        }

        return response()->json([
            'cards' => $cards,
            'pagination' => [
                'current_page' => $portfolios->currentPage(),
                'last_page' => $portfolios->lastPage(),
                'per_page' => $portfolios->perPage(),
                'total' => $portfolios->total(),
                'from' => $portfolios->firstItem(),
                'to' => $portfolios->lastItem(),
            ]
        ]);
    }



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();
        $companyId = $user?->company_id;

        if (!$user->hasCompanyPermission($companyId, 'create_properties')) {
            abort(403, 'Bu sayfaya erişim yetkiniz yok.');
        }

        // Form için gerekli veriler
        $portfolioTypes = PortfolioType::all();
        $portfolioCategories = PortfolioCategory::whereNull('parent_id')->orderBy('name')->get(); // Sadece ana kategoriler
        $agents = User::where('company_id', $companyId)->get();
        $cities = City::all();

        return view('portfolios::create', compact(
            'portfolioTypes',
            'portfolioCategories',
            'agents',
            'cities'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            $companyId = Auth::user()->company_id;
            $userId = Auth::id();

            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'type_id' => 'required|exists:portfolio_types,id',
                'main_category_id' => 'required|exists:portfolio_categories,id',
                'category_id' => 'nullable|exists:portfolio_categories,id',
                'status' => 'required|in:active,inactive,sold,rented,archived',
                'agent_id' => 'nullable|exists:users,id',
                'owner_id' => 'nullable|exists:customers,id',
                'details' => 'nullable|array',
                'price' => 'required|numeric|min:0',
                'currency' => 'required|in:TRY,USD,EUR',
                'gross_area' => 'nullable|numeric|min:0',
                'net_area' => 'nullable|numeric|min:0',
                'price_per_m2' => 'nullable|numeric|min:0',
                'from_whom' => 'nullable|in:owner,agent,developer,bank',
                'deed_status' => 'nullable|in:Kat mülkiyetli,Kat irtifaklı,Hisseli tapu,Müstakil tapu',
                'is_loan_eligible' => 'boolean',
                'swap' => 'boolean',
                'pool' => 'boolean',
                'city_id' => 'required|exists:iller,il_id',
                'district_id' => 'nullable|exists:ilceler,ilce_id',
                'neighborhood_id' => 'nullable|exists:mahalleler,mahalle_id',
                'address' => 'nullable|string',
            ]);

            // Slug oluştur
            $slug = Str::slug($validated['title']);
            $originalSlug = $slug;
            $counter = 1;
            while (Portfolio::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $validated['company_id'] = $companyId;
            $validated['user_id'] = $userId;
            $validated['slug'] = $slug;

            Portfolio::create($validated);

            DB::commit();

            return redirect()
                ->route('portfolios.index')
                ->with([
                    'status' => 'success',
                    'responseTitle' => 'Başarılı!',
                    'responseMessage' => 'Portföy başarıyla oluşturuldu.'
                ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()
                ->back()
                ->withInput()
                ->with([
                    'status' => 'error',
                    'responseTitle' => 'Hata!',
                    'responseMessage' => 'Portföy oluşturulurken bir hata oluştu: ' . $e->getMessage()
                ]);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('portfolios::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('portfolios::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id) {}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id) {}

    /**
     * Show portfolio offers and bids
     */
    public function offers(Portfolio $portfolio)
    {
        return view('portfolios::offers', compact('portfolio'));
    }

    /**
     * Show portfolio price history
     */
    public function priceHistory(Portfolio $portfolio)
    {
        return view('portfolios::price-history', compact('portfolio'));
    }

    /**
     * Show portfolio notes
     */
    public function notes(Portfolio $portfolio)
    {
        return view('portfolios::notes', compact('portfolio'));
    }

    /**
     * Store a new note
     */
    public function storeNote(Request $request, Portfolio $portfolio)
    {
        // Note storage logic here
        return redirect()->back()->with('success', 'Not başarıyla eklendi.');
    }

    /**
     * Show portfolio customers
     */
    public function customers(Portfolio $portfolio)
    {
        return view('portfolios::customers', compact('portfolio'));
    }

    /**
     * Show customer history
     */
    public function customerHistory(Portfolio $portfolio)
    {
        return view('portfolios::customer-history', compact('portfolio'));
    }

    /**
     * Show portfolio appointments
     */
    public function appointments(Portfolio $portfolio)
    {
        return view('portfolios::appointments', compact('portfolio'));
    }

    /**
     * Store a new appointment
     */
    public function storeAppointment(Request $request, Portfolio $portfolio)
    {
        // Appointment storage logic here
        return redirect()->back()->with('success', 'Randevu başarıyla oluşturuldu.');
    }

    /**
     * Show status history
     */
    public function statusHistory(Portfolio $portfolio)
    {
        return view('portfolios::status-history', compact('portfolio'));
    }

    /**
     * Show portfolio actions
     */
    public function actions(Portfolio $portfolio)
    {
        return view('portfolios::actions', compact('portfolio'));
    }

    /**
     * Store a new action
     */
    public function storeAction(Request $request, Portfolio $portfolio)
    {
        // Action storage logic here
        return redirect()->back()->with('success', 'Aksiyon başarıyla eklendi.');
    }

    /**
     * Show suitable demands for this portfolio
     */
    public function suitableDemands(Portfolio $portfolio)
    {
        return view('portfolios::suitable-demands', compact('portfolio'));
    }
}
