<?php

namespace Modules\UserManagement\App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\CompanyRole;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class RoleController extends Controller
{
    /**
     * Rol listesi.
     */
    public function index(): View
    {
        $user = Auth::user();

        // Sadece rol yönetimi iznine sahip olanlar görebilir
        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol yönetimi yetkiniz yok.');
        }

        // Şirket rollerini getir
        $roles = CompanyRole::where('company_id', $user->company_id)
            ->with(['permissions'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('usermanagement::roles.index', compact('roles'));
    }

    /**
     * Yeni rol oluşturma sayfası.
     */
    public function create(): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol oluşturma yetkiniz yok.');
        }

        // Tüm izinleri getir
        $permissions = Permission::orderBy('label')->get();

        return view('usermanagement::roles.create', compact('permissions'));
    }

    /**
     * Yeni rol kaydetme.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol oluşturma yetkiniz yok.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        // Rol oluştur
        $role = CompanyRole::create([
            'id' => Str::uuid(),
            'company_id' => $user->company_id,
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => true,
        ]);

        // İzinleri ata
        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        }

        return redirect()->route('roles.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Rol başarıyla oluşturuldu.'
            ]);
    }

    /**
     * Rol detayları.
     */
    public function show($id): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol görüntüleme yetkiniz yok.');
        }

        $role = CompanyRole::where('company_id', $user->company_id)
            ->with(['permissions', 'users.profile'])
            ->findOrFail($id);

        return view('usermanagement::roles.show', compact('role'));
    }

    /**
     * Rol düzenleme sayfası.
     */
    public function edit($id): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol düzenleme yetkiniz yok.');
        }

        $role = CompanyRole::where('company_id', $user->company_id)
            ->with(['permissions'])
            ->findOrFail($id);

        $permissions = Permission::orderBy('label')->get();

        return view('usermanagement::roles.edit', compact('role', 'permissions'));
    }

    /**
     * Rol güncelleme.
     */
    public function update(Request $request, $id): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol düzenleme yetkiniz yok.');
        }

        $role = CompanyRole::where('company_id', $user->company_id)->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
            'is_active' => 'boolean',
        ]);

        // Rol güncelle
        $role->update([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->has('is_active'),
        ]);

        // İzinleri güncelle
        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        } else {
            $role->permissions()->detach();
        }

        return redirect()->route('roles.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Rol başarıyla güncellendi.'
            ]);
    }

    /**
     * Rol silme.
     */
    public function destroy($id): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol silme yetkiniz yok.');
        }

        $role = CompanyRole::where('company_id', $user->company_id)->findOrFail($id);

        // Kullanıcıları olan rol silinemez
        if ($role->users()->count() > 0) {
            return redirect()->route('roles.index')
                ->with([
                    'status' => 'error',
                    'responseTitle' => 'Hata!',
                    'responseMessage' => 'Bu role atanmış kullanıcılar bulunduğu için silinemez.'
                ]);
        }

        // İzin ilişkilerini sil
        $role->permissions()->detach();

        // Rolü sil
        $role->delete();

        return redirect()->route('roles.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Rol başarıyla silindi.'
            ]);
    }

    /**
     * Rol durumunu değiştir.
     */
    public function toggleStatus($id): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_roles')) {
            abort(403, 'Rol yönetimi yetkiniz yok.');
        }

        $role = CompanyRole::where('company_id', $user->company_id)->findOrFail($id);

        $role->update([
            'is_active' => !$role->is_active
        ]);

        $status = $role->is_active ? 'aktif' : 'pasif';

        return redirect()->route('roles.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => "Rol başarıyla {$status} hale getirildi."
            ]);
    }
}
