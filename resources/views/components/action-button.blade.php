@props([
    'variant' => 'primary', // primary, secondary, success, danger, warning
    'size' => 'md', // sm, md, lg
    'icon' => null,
    'iconPosition' => 'left', // left, right
    'loading' => false,
    'href' => null,
    'type' => 'button'
])

@php
$baseClasses = 'inline-flex items-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

$variantClasses = [
    'primary' => 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    'secondary' => 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    'success' => 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
    'danger' => 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    'warning' => 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
];

$sizeClasses = [
    'sm' => 'px-3 py-2 text-sm',
    'md' => 'px-4 py-2 text-sm',
    'lg' => 'px-6 py-3 text-base',
];

$classes = $baseClasses . ' ' . ($variantClasses[$variant] ?? $variantClasses['primary']) . ' ' . ($sizeClasses[$size] ?? $sizeClasses['md']);
@endphp

@if($href)
<a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
    @if($icon && $iconPosition === 'left')
    <div class="w-4 h-4 {{ $slot->isEmpty() ? '' : 'mr-2' }}">
        {!! $icon !!}
    </div>
    @endif

    @if($loading)
    <svg class="animate-spin w-4 h-4 {{ $slot->isEmpty() ? '' : 'mr-2' }}" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    @endif

    {{ $slot }}

    @if($icon && $iconPosition === 'right')
    <div class="w-4 h-4 {{ $slot->isEmpty() ? '' : 'ml-2' }}">
        {!! $icon !!}
    </div>
    @endif
</a>
@else
<button type="{{ $type }}" {{ $attributes->merge(['class' => $classes]) }}>
    @if($icon && $iconPosition === 'left')
    <div class="w-4 h-4 {{ $slot->isEmpty() ? '' : 'mr-2' }}">
        {!! $icon !!}
    </div>
    @endif

    @if($loading)
    <svg class="animate-spin w-4 h-4 {{ $slot->isEmpty() ? '' : 'mr-2' }}" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    @endif

    {{ $slot }}

    @if($icon && $iconPosition === 'right')
    <div class="w-4 h-4 {{ $slot->isEmpty() ? '' : 'ml-2' }}">
        {!! $icon !!}
    </div>
    @endif
</button>
@endif
