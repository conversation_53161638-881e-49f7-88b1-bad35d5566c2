<?php

namespace App\Livewire;

use App\Models\Package;
use Livewire\Component;
use Livewire\Attributes\Rule;
use Illuminate\Support\Str;

class PackageEditor extends Component
{
    public $showModal = false;
    public $packageId = null;
    public $editMode = false;
    public $isLoading = false;

    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('required|string|max:255|unique:packages,slug')]
    public $slug = '';

    #[Rule('nullable|string')]
    public $description = '';

    #[Rule('required|numeric|min:0')]
    public $monthly_price = 0;

    #[Rule('required|numeric|min:0')]
    public $yearly_price = 0;

    #[Rule('required|array|min:1')]
    public $features = [];

    #[Rule('required|array')]
    public $limits = [];

    #[Rule('boolean')]
    public $is_popular = false;

    #[Rule('boolean')]
    public $is_active = true;

    #[Rule('integer|min:0')]
    public $sort_order = 0;

    // Temporary fields for adding features
    public $newFeature = '';

    // Limits fields
    public $max_staff = 0;
    public $max_properties = 0;
    public $max_storage_gb = 0;
    public $max_photo_per_property = 0;
    public $has_api_access = false;
    public $has_custom_domain = false;
    public $has_advanced_reports = false;
    public $has_crm = false;

    protected $listeners = [
        'openEditModal' => 'editPackage',
        'openNewModal' => 'openModal'
    ];

    public function mount()
    {
        $this->features = [];
        $this->limits = [];
    }

    public function openModal()
    {
        $this->resetForm();
        $this->editMode = false;
        $this->showModal = true;

        // Modal açıldığını bildir
        $this->dispatch('modalOpened');
    }

    public function editPackage($packageId)
    {
        $package = Package::findOrFail($packageId);

        $this->packageId = $package->id;
        $this->name = $package->name;
        $this->slug = $package->slug;
        $this->description = $package->description;
        $this->monthly_price = $package->monthly_price;
        $this->yearly_price = $package->yearly_price;
        $this->features = $package->features ?? [];
        $this->limits = $package->limits ?? [];
        $this->is_popular = $package->is_popular;
        $this->is_active = $package->is_active;
        $this->sort_order = $package->sort_order;

        // Load limits into individual fields
        $this->max_staff = $this->limits['max_staff'] ?? 0;
        $this->max_properties = $this->limits['max_properties'] ?? 0;
        $this->max_storage_gb = $this->limits['max_storage_gb'] ?? 0;
        $this->max_photo_per_property = $this->limits['max_photo_per_property'] ?? 0;
        $this->has_api_access = $this->limits['has_api_access'] ?? false;
        $this->has_custom_domain = $this->limits['has_custom_domain'] ?? false;
        $this->has_advanced_reports = $this->limits['has_advanced_reports'] ?? false;
        $this->has_crm = $this->limits['has_crm'] ?? false;

        $this->editMode = true;
        $this->showModal = true;

        // Modal açıldığını bildir
        $this->dispatch('modalOpened');
    }

    public function updatedName()
    {
        $this->slug = Str::slug($this->name);
    }

    public function addFeature()
    {
        if (!empty(trim($this->newFeature))) {
            $this->features[] = trim($this->newFeature);
            $this->newFeature = '';
        }
    }

    public function removeFeature($index)
    {
        unset($this->features[$index]);
        $this->features = array_values($this->features);
    }

    public function save()
    {
        $this->isLoading = true;

        // Prepare limits array
        $this->limits = [
            'max_staff' => $this->max_staff == -1 ? -1 : (int)$this->max_staff,
            'max_properties' => $this->max_properties == -1 ? -1 : (int)$this->max_properties,
            'max_storage_gb' => $this->max_storage_gb == -1 ? -1 : (int)$this->max_storage_gb,
            'max_photo_per_property' => $this->max_photo_per_property == -1 ? -1 : (int)$this->max_photo_per_property,
            'has_api_access' => (bool)$this->has_api_access,
            'has_custom_domain' => (bool)$this->has_custom_domain,
            'has_advanced_reports' => (bool)$this->has_advanced_reports,
            'has_crm' => (bool)$this->has_crm,
        ];

        // Dynamic validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'slug' => $this->editMode && $this->packageId
                ? 'required|string|max:255|unique:packages,slug,' . $this->packageId
                : 'required|string|max:255|unique:packages,slug',
            'description' => 'nullable|string',
            'monthly_price' => 'required|numeric|min:0',
            'yearly_price' => 'required|numeric|min:0',
            'features' => 'required|array|min:1',
            'limits' => 'required|array',
            'is_popular' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];

        // Custom validation messages
        $messages = [
            'name.required' => 'Paket adı gereklidir.',
            'name.string' => 'Paket adı metin olmalıdır.',
            'name.max' => 'Paket adı en fazla 255 karakter olabilir.',
            'slug.required' => 'Slug gereklidir.',
            'slug.string' => 'Slug metin olmalıdır.',
            'slug.max' => 'Slug en fazla 255 karakter olabilir.',
            'slug.unique' => 'Bu slug zaten kullanılıyor.',
            'description.string' => 'Açıklama metin olmalıdır.',
            'monthly_price.required' => 'Aylık fiyat gereklidir.',
            'monthly_price.numeric' => 'Aylık fiyat sayısal olmalıdır.',
            'monthly_price.min' => 'Aylık fiyat 0 veya daha büyük olmalıdır.',
            'yearly_price.required' => 'Yıllık fiyat gereklidir.',
            'yearly_price.numeric' => 'Yıllık fiyat sayısal olmalıdır.',
            'yearly_price.min' => 'Yıllık fiyat 0 veya daha büyük olmalıdır.',
            'features.required' => 'Paket özellikleri gereklidir.',
            'features.array' => 'Paket özellikleri dizi formatında olmalıdır.',
            'features.min' => 'En az bir paket özelliği eklemelisiniz.',
            'limits.required' => 'Paket limitleri gereklidir.',
            'limits.array' => 'Paket limitleri dizi formatında olmalıdır.',
            'is_popular.boolean' => 'Popüler durumu doğru/yanlış olmalıdır.',
            'is_active.boolean' => 'Aktif durumu doğru/yanlış olmalıdır.',
            'sort_order.integer' => 'Sıralama tam sayı olmalıdır.',
            'sort_order.min' => 'Sıralama 0 veya daha büyük olmalıdır.',
        ];

        try {
            $this->validate($rules, $messages);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->isLoading = false;

            // Validation hataları için SweetAlert
            $errors = collect($e->errors())->flatten()->implode('<br>');
            $this->dispatch('showAlert', [
                'type' => 'error',
                'title' => 'Eksik Bilgiler!',
                'message' => $errors
            ]);
            return; // throw yerine return kullan
        }

        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'monthly_price' => $this->monthly_price,
            'yearly_price' => $this->yearly_price,
            'features' => $this->features,
            'limits' => $this->limits,
            'is_popular' => $this->is_popular,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
        ];

        try {
            if ($this->editMode && $this->packageId) {
                Package::findOrFail($this->packageId)->update($data);
            } else {
                Package::create($data);
            }

            // Başarılı mesajı göster ve modalı kapat
            $this->isLoading = false;
            $this->dispatch('packageSaved');
            $this->dispatch('showAlert', [
                'type' => 'success',
                'title' => $this->editMode ? 'Başarılı!' : 'Başarılı!',
                'message' => $this->editMode ? 'Paket başarıyla güncellendi.' : 'Yeni paket başarıyla oluşturuldu.'
            ]);
            $this->closeModal();
                } catch (\Exception $e) {
            $this->isLoading = false;
            $this->dispatch('showAlert', [
                'type' => 'error',
                'title' => 'Hata!',
                'message' => 'Bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
        $this->resetValidation();

        // Loading state'i temizlemek için dispatch
        $this->dispatch('modalClosed');
    }

    private function resetForm()
    {
        $this->isLoading = false;
        $this->packageId = null;
        $this->name = '';
        $this->slug = '';
        $this->description = '';
        $this->monthly_price = 0;
        $this->yearly_price = 0;
        $this->features = [];
        $this->limits = [];
        $this->is_popular = false;
        $this->is_active = true;
        $this->sort_order = 0;
        $this->newFeature = '';

        // Reset limit fields
        $this->max_staff = 0;
        $this->max_properties = 0;
        $this->max_storage_gb = 0;
        $this->max_photo_per_property = 0;
        $this->has_api_access = false;
        $this->has_custom_domain = false;
        $this->has_advanced_reports = false;
        $this->has_crm = false;

        $this->resetValidation();
    }

    public function render()
    {
        return view('livewire.backend.package-editor');
    }
}
