<x-backend-layout>
    @section('content')
        <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-20">
                <div
                    class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob">
                </div>
                <div
                    class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000">
                </div>
                <div
                    class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000">
                </div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
                <!-- Header Section -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        <div>
                            <h1
                                class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                                Şirket Ayarları
                            </h1>
                            <p class="text-gray-600 text-lg mt-2">Şirket bilgilerinizi ve sistem ayarlarınızı yönetin</p>
                        </div>

                        <!-- Company Status -->
                        <div class="flex items-center gap-4">
                            <div
                                class="flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 shadow-sm">
                                <div
                                    class="w-3 h-3 {{ auth()->user()->company->is_active ? 'bg-green-500' : 'bg-red-500' }} rounded-full {{ auth()->user()->company->is_active ? 'animate-pulse' : '' }}">
                                </div>
                                <span
                                    class="text-sm font-semibold {{ auth()->user()->company->is_active ? 'text-green-700' : 'text-red-700' }}">
                                    {{ auth()->user()->company->is_active ? 'Aktif' : 'Pasif' }}
                                </span>
                            </div>
                            <div
                                class="flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 shadow-sm">
                                <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z" />
                                </svg>
                                <span class="text-sm font-semibold text-blue-700">{{ auth()->user()->company->name }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
                    <!-- Company Info Sidebar -->
                    <div class="xl:col-span-1">
                        <div
                            class="group relative bg-white border border-gray-200 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden sticky top-8">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                            </div>

                            <div class="relative z-10">
                                <!-- Company Logo -->
                                <div class="text-center mb-6">
                                    <div class="relative inline-block">
                                        <div class="w-24 h-24 mx-auto mb-4 relative">
                                            @if (auth()->user()->company->logo_url)
                                                <img src="{{ auth()->user()->company->logo_url }}"
                                                    alt="{{ auth()->user()->company->name }}"
                                                    class="w-full h-full rounded-2xl object-cover shadow-lg border-4 border-white">
                                            @else
                                                <div
                                                    class="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg border-4 border-white">
                                                    <span class="text-white text-2xl font-bold">
                                                        {{ substr(auth()->user()->company->name, 0, 2) }}
                                                    </span>
                                                </div>
                                            @endif
                                        </div>

                                        <h2 class="text-xl font-bold text-gray-900 mb-1">{{ auth()->user()->company->name }}
                                        </h2>
                                        <p class="text-gray-600 text-sm">
                                            {{ auth()->user()->company->subdomain ?? 'subdomain' }}.emlaksis.net</p>
                                    </div>
                                </div>

                                <!-- Company Stats -->
                                <div class="space-y-4">
                                    <div
                                        class="flex items-center justify-between p-3 bg-white/50 rounded-xl border border-white/30">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24"
                                                    stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">Kullanıcılar</div>
                                                <div class="text-xs text-gray-600">
                                                    {{ auth()->user()->company->users->count() }} aktif</div>
                                            </div>
                                        </div>
                                        <div class="text-lg font-bold text-green-600">
                                            {{ auth()->user()->company->users->count() }}</div>
                                    </div>

                                    @if (auth()->user()->company->created_at)
                                        <div
                                            class="flex items-center justify-between p-3 bg-white/50 rounded-xl border border-white/30">
                                            <div class="flex items-center gap-3">
                                                <div
                                                    class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">Kuruluş</div>
                                                    <div class="text-xs text-gray-600">
                                                        {{ auth()->user()->company->created_at->format('d.m.Y') }}</div>
                                                </div>
                                            </div>
                                            <div class="text-sm font-bold text-blue-600">
                                                {{ auth()->user()->company->created_at->diffInDays() }}g</div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Quick Actions -->
                                <div class="mt-6 space-y-3">
                                    <button
                                        class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-3 rounded-2xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                        <div class="flex items-center justify-center gap-2">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                            </svg>
                                            <span>Yedek Al</span>
                                        </div>
                                    </button>

                                    <button
                                        class="w-full bg-white border border-gray-300 text-gray-700 px-4 py-3 rounded-2xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                                        <div class="flex items-center justify-center gap-2">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                                            </svg>
                                            <span>Raporlar</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Settings Area -->
                    <div class="xl:col-span-3">
                        <form action="{{ route('company.update-settings') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')

                            <!-- Tab Navigation -->
                            <div class="mb-8">
                                <div
                                    class="flex flex-wrap gap-2 p-2 bg-gray-50 rounded-2xl border border-gray-200">
                                    <button type="button"
                                        class="tab-button active px-6 py-3 rounded-xl font-medium transition-all duration-300"
                                        data-tab="general">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            <span>Genel</span>
                                        </div>
                                    </button>
                                    <button type="button"
                                        class="tab-button px-6 py-3 rounded-xl font-medium transition-all duration-300"
                                        data-tab="company">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                            </svg>
                                            <span>Şirket</span>
                                        </div>
                                    </button>
                                    <button type="button"
                                        class="tab-button px-6 py-3 rounded-xl font-medium transition-all duration-300"
                                        data-tab="security">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                            </svg>
                                            <span>Güvenlik</span>
                                        </div>
                                    </button>
                                    <button type="button"
                                        class="tab-button px-6 py-3 rounded-xl font-medium transition-all duration-300"
                                        data-tab="integrations">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                                            </svg>
                                            <span>Entegrasyonlar</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <!-- Tab Contents -->
                            <div class="space-y-8">
                                <!-- General Settings Tab -->
                                <div class="tab-content active" id="general-tab">
                                    <div
                                        class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                                        <div
                                            class="absolute inset-0 bg-gradient-to-br from-green-500/5 via-transparent to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        </div>

                                        <div class="relative z-10">
                                            <div class="flex items-center gap-4 mb-8">
                                                <div
                                                    class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h2 class="text-2xl font-bold text-gray-900">Genel Ayarlar</h2>
                                                    <p class="text-gray-600 text-sm">Sistem geneli ayarlarını yönetin</p>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <!-- System Settings -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-green-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M4.5 12a7.5 7.5 0 0015 0m-15 0a7.5 7.5 0 1115 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077l1.41-.513m14.095-5.13l1.41-.513M5.106 17.785l1.15-.964m11.49-9.642l1.149-.964M7.501 19.795l.75-1.3m7.5-12.99l.75-1.3m-6.063 16.658l.26-1.477m2.605-14.772l.26-1.477m0 17.726l-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205L12 12m6.894 5.785l-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864l-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495" />
                                                        </svg>
                                                        Sistem Ayarları
                                                    </h3>

                                                    <!-- Auto Backup -->
                                                    <div
                                                        class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
                                                        <div class="flex items-center gap-3">
                                                            <div
                                                                class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                                <svg class="w-5 h-5 text-blue-600" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
                                                                </svg>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-semibold text-gray-900">Otomatik
                                                                    Yedekleme</div>
                                                                <div class="text-xs text-gray-600">Günlük otomatik yedek
                                                                    oluştur</div>
                                                            </div>
                                                        </div>
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox" name="settings[auto_backup]"
                                                                value="1"
                                                                {{ auth()->user()->company->settings['auto_backup'] ?? false ? 'checked' : '' }}
                                                                class="sr-only peer">
                                                            <div
                                                                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600">
                                                            </div>
                                                        </label>
                                                    </div>

                                                    <!-- Email Notifications -->
                                                    <div
                                                        class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
                                                        <div class="flex items-center gap-3">
                                                            <div
                                                                class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                                <svg class="w-5 h-5 text-green-600" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                                                </svg>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-semibold text-gray-900">E-posta
                                                                    Bildirimleri</div>
                                                                <div class="text-xs text-gray-600">Sistem bildirimlerini
                                                                    e-posta ile gönder</div>
                                                            </div>
                                                        </div>
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox" name="settings[email_notifications]"
                                                                value="1"
                                                                {{ auth()->user()->company->settings['email_notifications'] ?? true ? 'checked' : '' }}
                                                                class="sr-only peer">
                                                            <div
                                                                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600">
                                                            </div>
                                                        </label>
                                                    </div>

                                                    <!-- Maintenance Mode -->
                                                    <div
                                                        class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
                                                        <div class="flex items-center gap-3">
                                                            <div
                                                                class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                                <svg class="w-5 h-5 text-yellow-600" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z" />
                                                                </svg>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-semibold text-gray-900">Bakım Modu
                                                                </div>
                                                                <div class="text-xs text-gray-600">Sistemi bakım moduna al
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox" name="settings[maintenance_mode]"
                                                                value="1"
                                                                {{ auth()->user()->company->settings['maintenance_mode'] ?? false ? 'checked' : '' }}
                                                                class="sr-only peer">
                                                            <div
                                                                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600">
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>

                                                <!-- Language & Timezone -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-green-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802" />
                                                        </svg>
                                                        Dil & Zaman
                                                    </h3>

                                                    <!-- Default Language -->
                                                    <div class="form-group">
                                                        <label for="default_language"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Varsayılan Dil
                                                        </label>
                                                        <select name="settings[default_language]" id="default_language"
                                                            class="form-select">
                                                            <option value="tr"
                                                                {{ (auth()->user()->company->settings['default_language'] ?? 'tr') == 'tr' ? 'selected' : '' }}>
                                                                🇹🇷 Türkçe</option>
                                                            <option value="en"
                                                                {{ (auth()->user()->company->settings['default_language'] ?? 'tr') == 'en' ? 'selected' : '' }}>
                                                                🇺🇸 English</option>
                                                        </select>
                                                    </div>

                                                    <!-- Timezone -->
                                                    <div class="form-group">
                                                        <label for="timezone"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Zaman Dilimi
                                                        </label>
                                                        <select name="settings[timezone]" id="timezone"
                                                            class="form-select">
                                                            <option value="Europe/Istanbul"
                                                                {{ (auth()->user()->company->settings['timezone'] ?? 'Europe/Istanbul') == 'Europe/Istanbul' ? 'selected' : '' }}>
                                                                İstanbul (UTC+3)</option>
                                                            <option value="UTC"
                                                                {{ (auth()->user()->company->settings['timezone'] ?? 'Europe/Istanbul') == 'UTC' ? 'selected' : '' }}>
                                                                UTC (UTC+0)</option>
                                                            <option value="Europe/London"
                                                                {{ (auth()->user()->company->settings['timezone'] ?? 'Europe/Istanbul') == 'Europe/London' ? 'selected' : '' }}>
                                                                London (UTC+0)</option>
                                                            <option value="America/New_York"
                                                                {{ (auth()->user()->company->settings['timezone'] ?? 'Europe/Istanbul') == 'America/New_York' ? 'selected' : '' }}>
                                                                New York (UTC-5)</option>
                                                        </select>
                                                    </div>

                                                    <!-- Public Registration -->
                                                    <div
                                                        class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
                                                        <div class="flex items-center gap-3">
                                                            <div
                                                                class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                                                <svg class="w-5 h-5 text-purple-600" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM3 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 019.374 21c-2.331 0-4.512-.645-6.374-1.766z" />
                                                                </svg>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-semibold text-gray-900">Açık Kayıt
                                                                </div>
                                                                <div class="text-xs text-gray-600">Herkese açık kullanıcı
                                                                    kaydı</div>
                                                            </div>
                                                        </div>
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox" name="settings[public_registration]"
                                                                value="1"
                                                                {{ auth()->user()->company->settings['public_registration'] ?? false ? 'checked' : '' }}
                                                                class="sr-only peer">
                                                            <div
                                                                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600">
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Company Settings Tab -->
                                <div class="tab-content" id="company-tab">
                                    <div
                                        class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                                        <div
                                            class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        </div>

                                        <div class="relative z-10">
                                            <div class="flex items-center gap-4 mb-8">
                                                <div
                                                    class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h2 class="text-2xl font-bold text-gray-900">Şirket Bilgileri</h2>
                                                    <p class="text-gray-600 text-sm">Şirket detaylarını ve iletişim
                                                        bilgilerini güncelleyin</p>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                                <!-- Basic Company Info -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-blue-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                                        </svg>
                                                        Temel Bilgiler
                                                    </h3>

                                                    <!-- Company Name -->
                                                    <div class="form-group">
                                                        <label for="company_name"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Şirket Adı <span class="text-red-500">*</span>
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                                                </svg>
                                                            </div>
                                                            <input type="text" id="company_name" name="name"
                                                                value="{{ old('name', auth()->user()->company->name) }}"
                                                                required class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Description -->
                                                    <div class="form-group">
                                                        <label for="description"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Açıklama
                                                        </label>
                                                        <textarea id="description" name="description" rows="4" placeholder="Şirketiniz hakkında kısa bir açıklama..."
                                                            class="form-textarea">{{ old('description', auth()->user()->company->description) }}</textarea>
                                                    </div>

                                                    <!-- Website -->
                                                    <div class="form-group">
                                                        <label for="website"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Web Sitesi
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9z" />
                                                                </svg>
                                                            </div>
                                                            <input type="url" id="website" name="website"
                                                                value="{{ old('website', auth()->user()->company->website) }}"
                                                                placeholder="https://example.com" class="form-input">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Contact Info -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-blue-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                                        </svg>
                                                        İletişim Bilgileri
                                                    </h3>

                                                    <!-- Phone -->
                                                    <div class="form-group">
                                                        <label for="phone"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Telefon
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                                                </svg>
                                                            </div>
                                                            <input type="tel" id="phone" name="phone"
                                                                value="{{ old('phone', auth()->user()->company->phone) }}"
                                                                placeholder="0(555)123-45-67" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Email -->
                                                    <div class="form-group">
                                                        <label for="company_email"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            E-posta
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                                                </svg>
                                                            </div>
                                                            <input type="email" id="company_email" name="email"
                                                                value="{{ old('email', auth()->user()->company->email) }}"
                                                                placeholder="<EMAIL>" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- WhatsApp -->
                                                    <div class="form-group">
                                                        <label for="whatsapp"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            WhatsApp
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path
                                                                        d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                                                                </svg>
                                                            </div>
                                                            <input type="tel" id="whatsapp" name="whatsapp"
                                                                value="{{ old('whatsapp', auth()->user()->company->whatsapp) }}"
                                                                placeholder="0(555)123-45-67" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Address -->
                                                    <div class="form-group">
                                                        <label for="address"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Adres
                                                        </label>
                                                        <textarea id="address" name="address" rows="3" placeholder="Şirket adresinizi girin..."
                                                            class="form-textarea">{{ old('address', auth()->user()->company->address) }}</textarea>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Logo Upload Section -->
                                            <div
                                                class="mt-8 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-200/50">
                                                <h3
                                                    class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                                    <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M6.827 6.175A2.31 2.31 0 015.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 00-1.134-.175 2.31 2.31 0 01-1.64-1.055l-.822-1.316a2.192 2.192 0 00-1.736-1.039 48.774 48.774 0 00-5.232 0 2.192 2.192 0 00-1.736 1.039l-.821 1.316z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M16.5 12.75a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0zM18.75 10.5h.008v.008h-.008V10.5z" />
                                                    </svg>
                                                    Şirket Logosu
                                                </h3>

                                                <div class="flex items-center gap-6">
                                                    <!-- Current Logo -->
                                                    <div class="flex-shrink-0">
                                                        <div class="w-20 h-20 relative">
                                                            @if (auth()->user()->company->logo_url)
                                                                <img src="{{ auth()->user()->company->logo_url }}"
                                                                    alt="Current Logo"
                                                                    class="w-full h-full rounded-xl object-cover shadow-lg border-2 border-white">
                                                            @else
                                                                <div
                                                                    class="w-full h-full bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg border-2 border-white">
                                                                    <span class="text-white text-lg font-bold">
                                                                        {{ substr(auth()->user()->company->name, 0, 2) }}
                                                                    </span>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <!-- Upload Button -->
                                                    <div class="flex-1">
                                                        <label for="logo" class="group cursor-pointer">
                                                            <div
                                                                class="flex items-center gap-3 p-4 border-2 border-dashed border-blue-300 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all duration-300">
                                                                <svg class="w-8 h-8 text-blue-500 group-hover:scale-110 transition-transform duration-300"
                                                                    fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                                                                </svg>
                                                                <div>
                                                                    <div class="text-sm font-semibold text-gray-900">Logo
                                                                        Yükle</div>
                                                                    <div class="text-xs text-gray-600">PNG, JPG veya SVG
                                                                        (Max: 2MB)</div>
                                                                </div>
                                                            </div>
                                                        </label>
                                                        <input type="file" id="logo" name="logo"
                                                            accept="image/*" class="hidden">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Company Settings Tab -->
                                <div class="tab-content" id="company-tab">
                                    <div
                                        class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                                        <div
                                            class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        </div>

                                        <div class="relative z-10">
                                            <div class="flex items-center gap-4 mb-8">
                                                <div
                                                    class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h2 class="text-2xl font-bold text-gray-900">Şirket Bilgileri</h2>
                                                    <p class="text-gray-600 text-sm">Şirket detayları ve iletişim bilgileri
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                                <!-- Company Details -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-blue-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                                        </svg>
                                                        Şirket Detayları
                                                    </h3>

                                                    <!-- Company Name -->
                                                    <div class="form-group">
                                                        <label for="company_name"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Şirket Adı
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                                                </svg>
                                                            </div>
                                                            <input type="text" id="company_name" name="name"
                                                                value="{{ old('name', auth()->user()->company->name) }}"
                                                                placeholder="Şirket adınızı girin" class="form-input"
                                                                required>
                                                        </div>
                                                    </div>

                                                    <!-- Tax Number -->
                                                    <div class="form-group">
                                                        <label for="tax_number"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Vergi Numarası
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                                                </svg>
                                                            </div>
                                                            <input type="text" id="tax_number" name="tax_number"
                                                                value="{{ old('tax_number', auth()->user()->company->tax_number) }}"
                                                                placeholder="1234567890" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Tax Office -->
                                                    <div class="form-group">
                                                        <label for="tax_office"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Vergi Dairesi
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18M6.75 7.5h2.25m-2.25 3h2.25m-2.25 3h2.25m13.5-9L15.75 4.5 12 7.5l-3.75-3L4.5 7.5" />
                                                                </svg>
                                                            </div>
                                                            <input type="text" id="tax_office" name="tax_office"
                                                                value="{{ old('tax_office', auth()->user()->company->tax_office) }}"
                                                                placeholder="Kadıköy Vergi Dairesi" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Website -->
                                                    <div class="form-group">
                                                        <label for="website"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Website
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9z" />
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M2 12h20" />
                                                                </svg>
                                                            </div>
                                                            <input type="url" id="website" name="website"
                                                                value="{{ old('website', auth()->user()->company->website) }}"
                                                                placeholder="https://www.company.com" class="form-input">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Contact Info -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-blue-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                                        </svg>
                                                        İletişim Bilgileri
                                                    </h3>

                                                    <!-- Phone -->
                                                    <div class="form-group">
                                                        <label for="phone"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Telefon
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                                                </svg>
                                                            </div>
                                                            <input type="tel" id="phone" name="phone"
                                                                value="{{ old('phone', auth()->user()->company->phone) }}"
                                                                placeholder="0(555)123-45-67" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Email -->
                                                    <div class="form-group">
                                                        <label for="company_email"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            E-posta
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                                                </svg>
                                                            </div>
                                                            <input type="email" id="company_email" name="email"
                                                                value="{{ old('email', auth()->user()->company->email) }}"
                                                                placeholder="<EMAIL>" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- WhatsApp -->
                                                    <div class="form-group">
                                                        <label for="whatsapp"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            WhatsApp
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path
                                                                        d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                                                                </svg>
                                                            </div>
                                                            <input type="tel" id="whatsapp" name="whatsapp"
                                                                value="{{ old('whatsapp', auth()->user()->company->whatsapp) }}"
                                                                placeholder="0(555)123-45-67" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Address -->
                                                    <div class="form-group">
                                                        <label for="address"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Adres
                                                        </label>
                                                        <textarea id="address" name="address" rows="3" placeholder="Şirket adresinizi girin..."
                                                            class="form-textarea">{{ old('address', auth()->user()->company->address) }}</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Security Settings Tab -->
                                <div class="tab-content" id="security-tab">
                                    <div
                                        class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                                        <div
                                            class="absolute inset-0 bg-gradient-to-br from-red-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        </div>

                                        <div class="relative z-10">
                                            <div class="flex items-center gap-4 mb-8">
                                                <div
                                                    class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h2 class="text-2xl font-bold text-gray-900">Güvenlik Ayarları</h2>
                                                    <p class="text-gray-600 text-sm">Sistem güvenliği ve erişim kontrolü
                                                        ayarları</p>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                                <!-- Authentication Settings -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-red-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                                                        </svg>
                                                        Kimlik Doğrulama
                                                    </h3>

                                                    <!-- Two Factor Auth -->
                                                    <div
                                                        class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
                                                        <div class="flex items-center gap-3">
                                                            <div
                                                                class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                                <svg class="w-5 h-5 text-green-600" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                                                                </svg>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-semibold text-gray-900">İki
                                                                    Faktörlü Doğrulama</div>
                                                                <div class="text-xs text-gray-600">Ekstra güvenlik katmanı
                                                                    ekle</div>
                                                            </div>
                                                        </div>
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox" name="settings[two_factor_auth]"
                                                                value="1"
                                                                {{ auth()->user()->company->settings['two_factor_auth'] ?? false ? 'checked' : '' }}
                                                                class="sr-only peer">
                                                            <div
                                                                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600">
                                                            </div>
                                                        </label>
                                                    </div>

                                                    <!-- Strong Password -->
                                                    <div
                                                        class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
                                                        <div class="flex items-center gap-3">
                                                            <div
                                                                class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                                <svg class="w-5 h-5 text-yellow-600" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                                                                </svg>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-semibold text-gray-900">Güçlü
                                                                    Şifre Zorunlu</div>
                                                                <div class="text-xs text-gray-600">Karmaşık şifre kuralları
                                                                    uygula</div>
                                                            </div>
                                                        </div>
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox"
                                                                name="settings[strong_password_required]" value="1"
                                                                {{ auth()->user()->company->settings['strong_password_required'] ?? true ? 'checked' : '' }}
                                                                class="sr-only peer">
                                                            <div
                                                                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600">
                                                            </div>
                                                        </label>
                                                    </div>

                                                    <!-- Session Timeout -->
                                                    <div class="form-group">
                                                        <label for="session_timeout"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Oturum Zaman Aşımı (dakika)
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                            </div>
                                                            <input type="number" id="session_timeout"
                                                                name="settings[session_timeout]"
                                                                value="{{ auth()->user()->company->settings['session_timeout'] ?? 120 }}"
                                                                min="30" max="480" class="form-input">
                                                        </div>
                                                        <p class="text-xs text-gray-600 mt-1">30-480 dakika arası değer
                                                            giriniz</p>
                                                    </div>

                                                    <!-- Max Login Attempts -->
                                                    <div class="form-group">
                                                        <label for="max_login_attempts"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Maksimum Giriş Denemesi
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M12 9v3.75m-9.75 0a9 9 0 1119.5 0 9 9 0 01-19.5 0zm9.75 3.75h.008v.008H12v-.008z" />
                                                                </svg>
                                                            </div>
                                                            <input type="number" id="max_login_attempts"
                                                                name="settings[max_login_attempts]"
                                                                value="{{ auth()->user()->company->settings['max_login_attempts'] ?? 5 }}"
                                                                min="3" max="10" class="form-input">
                                                        </div>
                                                        <p class="text-xs text-gray-600 mt-1">3-10 arası değer giriniz</p>
                                                    </div>
                                                </div>

                                                <!-- Access Control -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-red-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                                                        </svg>
                                                        Erişim Kontrolü
                                                    </h3>

                                                    <!-- IP Whitelist -->
                                                    <div class="form-group">
                                                        <label for="ip_whitelist"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            IP Beyaz Listesi
                                                        </label>
                                                        <textarea id="ip_whitelist" name="settings[ip_whitelist]" rows="6"
                                                            placeholder="***********&#10;********&#10;Her satıra bir IP adresi..." class="form-textarea">{{ auth()->user()->company->settings['ip_whitelist'] ?? '' }}</textarea>
                                                        <p class="text-xs text-gray-600 mt-1">Sadece bu IP adreslerinden
                                                            giriş yapılabilir (boş bırakılırsa tüm IP'ler)</p>
                                                    </div>

                                                    <!-- Security Alert -->
                                                    <div class="p-4 bg-red-50 border border-red-200 rounded-xl">
                                                        <div class="flex items-start gap-3">
                                                            <svg class="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0"
                                                                fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                                stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    d="M12 9v3.75m-9.75 0a9 9 0 1119.5 0 9 9 0 01-19.5 0zm9.75 3.75h.008v.008H12v-.008z" />
                                                            </svg>
                                                            <div>
                                                                <h4 class="text-sm font-semibold text-red-800">Güvenlik
                                                                    Uyarısı</h4>
                                                                <p class="text-sm text-red-700 mt-1">Güvenlik ayarlarını
                                                                    değiştirmeden önce mevcut kullanıcıların
                                                                    etkilenebileceğini unutmayın. IP beyaz listesi
                                                                    kullanırken dikkatli olun.</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Integrations Tab -->
                                <div class="tab-content" id="integrations-tab">
                                    <div
                                        class="group relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
                                        <div
                                            class="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        </div>

                                        <div class="relative z-10">
                                            <div class="flex items-center gap-4 mb-8">
                                                <div
                                                    class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h2 class="text-2xl font-bold text-gray-900">Entegrasyonlar</h2>
                                                    <p class="text-gray-600 text-sm">Harici servisler ve API ayarları</p>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                                <!-- API Settings -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-purple-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5" />
                                                        </svg>
                                                        API Ayarları
                                                    </h3>

                                                    <!-- API Enabled -->
                                                    <div
                                                        class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
                                                        <div class="flex items-center gap-3">
                                                            <div
                                                                class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                                                <svg class="w-5 h-5 text-purple-600" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5" />
                                                                </svg>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-semibold text-gray-900">API
                                                                    Erişimi</div>
                                                                <div class="text-xs text-gray-600">REST API'yi etkinleştir
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <label class="relative inline-flex items-center cursor-pointer">
                                                            <input type="checkbox" name="settings[api_enabled]"
                                                                value="1"
                                                                {{ auth()->user()->company->settings['api_enabled'] ?? false ? 'checked' : '' }}
                                                                class="sr-only peer">
                                                            <div
                                                                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600">
                                                            </div>
                                                        </label>
                                                    </div>

                                                    <!-- Webhook URL -->
                                                    <div class="form-group">
                                                        <label for="webhook_url"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Webhook URL
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                                                                </svg>
                                                            </div>
                                                            <input type="url" id="webhook_url"
                                                                name="settings[webhook_url]"
                                                                value="{{ auth()->user()->company->settings['webhook_url'] ?? '' }}"
                                                                placeholder="https://example.com/webhook"
                                                                class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Google Maps API -->
                                                    <div class="form-group">
                                                        <label for="google_maps_api_key"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            Google Maps API Key
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                                                                </svg>
                                                            </div>
                                                            <input type="text" id="google_maps_api_key"
                                                                name="settings[google_maps_api_key]"
                                                                value="{{ auth()->user()->company->settings['google_maps_api_key'] ?? '' }}"
                                                                placeholder="AIza..." class="form-input">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Email Settings -->
                                                <div class="space-y-6">
                                                    <h3
                                                        class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                                        <svg class="w-5 h-5 text-purple-600" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                                        </svg>
                                                        E-posta Ayarları
                                                    </h3>

                                                    <!-- SMTP Host -->
                                                    <div class="form-group">
                                                        <label for="smtp_host"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            SMTP Sunucu
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                                                                </svg>
                                                            </div>
                                                            <input type="text" id="smtp_host"
                                                                name="settings[smtp_host]"
                                                                value="{{ auth()->user()->company->settings['smtp_host'] ?? '' }}"
                                                                placeholder="smtp.gmail.com" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- SMTP Username -->
                                                    <div class="form-group">
                                                        <label for="smtp_username"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            SMTP Kullanıcı Adı
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                                                </svg>
                                                            </div>
                                                            <input type="email" id="smtp_username"
                                                                name="settings[smtp_username]"
                                                                value="{{ auth()->user()->company->settings['smtp_username'] ?? '' }}"
                                                                placeholder="<EMAIL>" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- SMTP Password -->
                                                    <div class="form-group">
                                                        <label for="smtp_password"
                                                            class="block text-sm font-semibold text-gray-700 mb-2">
                                                            SMTP Şifre
                                                        </label>
                                                        <div class="relative">
                                                            <div
                                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                                <svg class="h-5 w-5 text-gray-400" fill="none"
                                                                    viewBox="0 0 24 24" stroke-width="1.5"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                                                </svg>
                                                            </div>
                                                            <input type="password" id="smtp_password"
                                                                name="settings[smtp_password]"
                                                                value="{{ auth()->user()->company->settings['smtp_password'] ?? '' }}"
                                                                placeholder="••••••••" class="form-input">
                                                        </div>
                                                    </div>

                                                    <!-- Integration Info -->
                                                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-xl">
                                                        <div class="flex items-start gap-3">
                                                            <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"
                                                                fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                                stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                                                            </svg>
                                                            <div>
                                                                <h4 class="text-sm font-semibold text-blue-800">Entegrasyon
                                                                    Bilgisi</h4>
                                                                <p class="text-sm text-blue-700 mt-1">Entegrasyon ayarları
                                                                    değiştirildikten sonra sistem yeniden başlatılabilir.
                                                                    Test işlemlerini önce geliştirme ortamında yapmanız
                                                                    önerilir.</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-4 justify-end mt-8">
                                <button type="button"
                                    class="group bg-white border border-gray-300 text-gray-700 px-8 py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                                    <div class="flex items-center justify-center gap-2">
                                        <svg class="w-5 h-5 group-hover:-rotate-12 transition-transform duration-300"
                                            fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                        </svg>
                                        <span>Sıfırla</span>
                                    </div>
                                </button>

                                <button type="submit"
                                    class="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                                    <div class="flex items-center justify-center gap-2">
                                        <svg class="w-5 h-5 group-hover:rotate-12 transition-transform duration-300"
                                            fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span>Ayarları Kaydet</span>
                                    </div>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endsection

    @push('scripts')
        <script>
            // Tab functionality
            document.addEventListener('DOMContentLoaded', function() {
                const tabButtons = document.querySelectorAll('.tab-button');
                const tabContents = document.querySelectorAll('.tab-content');

                tabButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const targetTab = this.getAttribute('data-tab');

                        // Remove active class from all buttons and contents
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        tabContents.forEach(content => content.classList.remove('active'));

                        // Add active class to clicked button and corresponding content
                        this.classList.add('active');
                        document.getElementById(targetTab + '-tab').classList.add('active');
                    });
                });

                // Logo preview
                document.getElementById('logo').addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const logoContainer = document.querySelector('.w-20.h-20 img, .w-20.h-20 div');
                            if (logoContainer.tagName === 'IMG') {
                                logoContainer.src = e.target.result;
                            } else {
                                // Replace div with img
                                const newImg = document.createElement('img');
                                newImg.src = e.target.result;
                                newImg.alt = 'New Logo';
                                newImg.className =
                                    'w-full h-full rounded-xl object-cover shadow-lg border-2 border-white';
                                logoContainer.parentNode.replaceChild(newImg, logoContainer);
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });

                // Form validation
                document.querySelector('form').addEventListener('submit', function(e) {
                    const requiredFields = this.querySelectorAll('input[required], select[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            isValid = false;
                            field.classList.add('error');
                            field.focus();
                        } else {
                            field.classList.remove('error');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert('Lütfen tüm gerekli alanları doldurun.');
                    }
                });

                // Reset button functionality
                document.querySelector('button[type="button"]').addEventListener('click', function() {
                    if (confirm('Tüm değişiklikleri sıfırlamak istediğinizden emin misiniz?')) {
                        document.querySelector('form').reset();
                    }
                });

                // Toggle switches animation
                document.querySelectorAll('input[type="checkbox"]').forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        const label = this.closest('label') || this.parentElement;
                        if (label) {
                            label.style.transform = 'scale(0.98)';
                            setTimeout(() => {
                                label.style.transform = 'scale(1)';
                            }, 150);
                        }
                    });
                });
            });
        </script>
    @endpush

</x-backend-layout>
