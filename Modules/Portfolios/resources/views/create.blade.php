<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 lg:px-6 py-8 relative z-10">
            <!-- Enhanced Header Section -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0012 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75z" />
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-600 bg-clip-text text-transparent">
                                Yeni Portföy Ekle
                            </h1>
                            <p class="mt-2 text-lg text-gray-600 flex items-center gap-2">
                                <svg class="w-5 h-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                                </svg>
                                Portföyünüze yeni bir emlak ekleyin ve detaylarını profesyonelce yönetin
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center gap-4">
                        <a href="{{ route('portfolios.index') }}"
                           class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200">
                            <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                            </svg>
                            Geri Dön
                        </a>
                    </div>
                </div>
            </div>

            <!-- Enhanced Form Container -->
            <div class="bg-white/80 border border-white/20 rounded-3xl shadow-2xl overflow-hidden">
                <form x-data="portfolioForm" action="{{ route('portfolios.store') }}" method="POST" enctype="multipart/form-data"
                      @submit.prevent="if(validateAllRequiredFields()) $el.submit()">
                    @csrf

                    <!-- Progress Bar -->
                    <div class="px-8 pt-8">
                        <div class="w-full bg-gray-200/80 rounded-full h-2.5 mb-4 shadow-inner">
                            <div
                                 x-bind:style="{ width: progress + '%' }"
                                 class="bg-gradient-to-r from-blue-500 to-purple-600 h-2.5 rounded-full transition-all duration-500">
                            </div>
                        </div>
                        <p class="text-center text-sm font-medium text-gray-600 mb-8">
                            Form İlerlemesi: <span x-text="`${Math.round(progress)}%`"></span>
                        </p>
                    </div>

                    <!-- Step Navigation -->
                    <div class="px-8 mb-8">
                        <nav class="flex justify-center">
                            <ol class="flex items-center space-x-2 md:space-x-8">
                                <li class="flex items-center">
                                    <button type="button"
                                            @click="setActiveTab('basic')"
                                            :class="activeTab === 'basic' ? 'bg-blue-600 text-white' : (completedSteps.includes('basic') ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600')"
                                            class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-colors duration-200">
                                        <span x-show="!completedSteps.includes('basic')">1</span>
                                        <svg x-show="completedSteps.includes('basic')" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                        </svg>
                                    </button>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden md:block">Temel Bilgiler</span>
                                </li>

                                <li class="flex items-center">
                                    <div class="w-8 h-0.5 bg-gray-300 mx-2"></div>
                                    <button type="button"
                                            @click="setActiveTab('price')"
                                            :class="activeTab === 'price' ? 'bg-blue-600 text-white' : (completedSteps.includes('price') ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600')"
                                            class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-colors duration-200">
                                        <span x-show="!completedSteps.includes('price')">2</span>
                                        <svg x-show="completedSteps.includes('price')" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                        </svg>
                                    </button>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden md:block">Fiyat & Detaylar</span>
                                </li>

                                <li class="flex items-center">
                                    <div class="w-8 h-0.5 bg-gray-300 mx-2"></div>
                                    <button type="button"
                                            @click="setActiveTab('location')"
                                            :class="activeTab === 'location' ? 'bg-blue-600 text-white' : (completedSteps.includes('location') ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600')"
                                            class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-colors duration-200">
                                        <span x-show="!completedSteps.includes('location')">3</span>
                                        <svg x-show="completedSteps.includes('location')" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                        </svg>
                                    </button>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden md:block">Konum</span>
                                </li>

                                <li class="flex items-center">
                                    <div class="w-8 h-0.5 bg-gray-300 mx-2"></div>
                                    <button type="button"
                                            @click="setActiveTab('features')"
                                            :class="activeTab === 'features' ? 'bg-blue-600 text-white' : (completedSteps.includes('features') ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600')"
                                            class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-colors duration-200">
                                        <span x-show="!completedSteps.includes('features')">4</span>
                                        <svg x-show="completedSteps.includes('features')" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                        </svg>
                                    </button>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden md:block">Özellikler</span>
                                </li>

                                <li class="flex items-center">
                                    <div class="w-8 h-0.5 bg-gray-300 mx-2"></div>
                                    <button type="button"
                                            @click="setActiveTab('media')"
                                            :class="activeTab === 'media' ? 'bg-blue-600 text-white' : (completedSteps.includes('media') ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600')"
                                            class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-colors duration-200">
                                        <span x-show="!completedSteps.includes('media')">5</span>
                                        <svg x-show="completedSteps.includes('media')" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                        </svg>
                                    </button>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden md:block">Medya</span>
                                </li>

                                <li class="flex items-center">
                                    <div class="w-8 h-0.5 bg-gray-300 mx-2"></div>
                                    <button type="button"
                                            @click="setActiveTab('documents')"
                                            :class="activeTab === 'documents' ? 'bg-blue-600 text-white' : (completedSteps.includes('documents') ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600')"
                                            class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-colors duration-200">
                                        <span x-show="!completedSteps.includes('documents')">6</span>
                                        <svg x-show="completedSteps.includes('documents')" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                        </svg>
                                    </button>
                                    <span class="ml-2 text-sm font-medium text-gray-700 hidden md:block">Belgeler</span>
                                </li>
                            </ol>
                        </nav>
                    </div>

                    <!-- Form Content -->
                    <div class="px-8 pb-8">
                        <!-- Temel Bilgiler Adımı -->
                        @include('portfolios::create_step_basic')

                        <!-- Fiyat & Detaylar Adımı -->
                        @include('portfolios::create_step_price')

                        <!-- Konum Bilgileri Adımı -->
                        @include('portfolios::create_step_location')

                        <!-- Özellikler Adımı -->
                        @include('portfolios::create_step_features')

                        <!-- Medya Adımı -->
                        @include('portfolios::create_step_media')

                        <!-- Belgeler Adımı -->
                        @include('portfolios::create_step_documents')
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
                        <!-- Önceki Buton -->
                        <button type="button"
                                @click="previousStep()"
                                x-show="activeTab !== 'basic'"
                                class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200 cursor-pointer">
                            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                            </svg>
                            Önceki
                        </button>

                        <div x-show="activeTab === 'basic'"></div>

                        <!-- İlerleme Bilgisi -->
                        <div class="flex items-center gap-4 text-sm text-gray-600">
                            <span x-text="`Adım ${getCurrentStepNumber()} / 6`"></span>
                            <div class="w-px h-4 bg-gray-300"></div>
                            <span x-text="`${Math.round(progress)}% Tamamlandı`"></span>
                        </div>

                        <!-- Sonraki/Kaydet Buton -->
                        <div class="flex items-center gap-3">
                            <!-- Sonraki Buton -->
                            <button type="button"
                                    @click="nextStep()"
                                    x-show="activeTab !== 'documents'"
                                    :disabled="!canProceedToNext()"
                                    :class="canProceedToNext() ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'"
                                    class="flex items-center gap-2 px-6 py-3 text-white rounded-xl transition-colors duration-200 cursor-pointer">
                                Sonraki
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                                </svg>
                            </button>

                            <!-- Kaydet Buton -->
                            <button type="submit"
                                    x-show="activeTab === 'documents'"
                                    :disabled="!validateAllRequiredFields()"
                                    :class="validateAllRequiredFields() ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400 cursor-not-allowed'"
                                    class="flex items-center gap-2 px-8 py-3 text-white rounded-xl transition-colors duration-200 cursor-pointer">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                </svg>
                                Portföyü Kaydet
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endsection
    @push('scripts')
    <!-- Alpine.js Portfolio Form Component -->
    <script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('portfolioForm', () => ({
            activeTab: 'basic',
            completedSteps: [],
            formData: {
                title: '',
                type_id: '',
                main_category_id: '',
                category_id: '',
                city_id: '',
                district_id: '',
                neighborhood_id: '',
                price: '',
                gross_area: '',
                net_area: '',
                latitude: '',
                longitude: ''
            },
            errors: {},
            isLoading: {
                subcategories: false,
                districts: false,
                neighborhoods: false,
                location: false
            },
            subCategoryOptions: [],
            districtOptions: [],
            neighborhoodOptions: [],

            get progress() {
                const totalSteps = 6;
                const completedCount = this.completedSteps.length;
                const currentStepProgress = this.getCurrentStepProgress();
                return ((completedCount + currentStepProgress) / totalSteps) * 100;
            },

            getCurrentStepNumber() {
                const steps = ['basic', 'price', 'location', 'features', 'media', 'documents'];
                return steps.indexOf(this.activeTab) + 1;
            },

            getCurrentStepProgress() {
                // Her adım için tamamlanma yüzdesini hesapla
                switch(this.activeTab) {
                    case 'basic':
                        let basicFields = ['title', 'type_id', 'main_category_id'];
                        let filledBasic = basicFields.filter(field => this.formData[field]).length;
                        return filledBasic / basicFields.length;
                    case 'price':
                        return this.formData.price ? 1 : 0;
                    case 'location':
                        return this.formData.city_id ? 0.5 : 0;
                    default:
                        return 0;
                }
            },

            setActiveTab(tab) {
                this.activeTab = tab;
            },

            nextStep() {
                const steps = ['basic', 'price', 'location', 'features', 'media', 'documents'];
                const currentIndex = steps.indexOf(this.activeTab);

                if (this.validateCurrentStep()) {
                    if (!this.completedSteps.includes(this.activeTab)) {
                        this.completedSteps.push(this.activeTab);
                    }

                    if (currentIndex < steps.length - 1) {
                        this.activeTab = steps[currentIndex + 1];
                    }
                }
            },

            previousStep() {
                const steps = ['basic', 'price', 'location', 'features', 'media', 'documents'];
                const currentIndex = steps.indexOf(this.activeTab);

                if (currentIndex > 0) {
                    this.activeTab = steps[currentIndex - 1];
                }
            },

            canProceedToNext() {
                return this.validateCurrentStep();
            },

            validateCurrentStep() {
                switch(this.activeTab) {
                    case 'basic':
                        return this.formData.title && this.formData.type_id && this.formData.main_category_id;
                    case 'price':
                        return this.formData.price;
                    case 'location':
                        return this.formData.city_id;
                    default:
                        return true; // Diğer adımlar opsiyonel
                }
            },

            validateAllRequiredFields() {
                return this.formData.title &&
                       this.formData.type_id &&
                       this.formData.main_category_id &&
                       this.formData.price &&
                       this.formData.city_id;
            },

            validateField(fieldName) {
                this.errors[fieldName] = '';

                switch(fieldName) {
                    case 'title':
                        if (!this.formData.title) {
                            this.errors.title = 'Portföy başlığı gereklidir';
                        }
                        break;
                    case 'type_id':
                        if (!this.formData.type_id) {
                            this.errors.type_id = 'İşlem tipi seçilmelidir';
                        }
                        break;
                    case 'main_category_id':
                        if (!this.formData.main_category_id) {
                            this.errors.main_category_id = 'Ana kategori seçilmelidir';
                        }
                        break;
                    case 'price':
                        if (!this.formData.price) {
                            this.errors.price = 'Fiyat girilmelidir';
                        }
                        break;
                    case 'city_id':
                        if (!this.formData.city_id) {
                            this.errors.city_id = 'Şehir seçilmelidir';
                        }
                        break;
                }
            },

            // Alt kategori yükleme
            async fetchSubCategories() {
                if (!this.formData.main_category_id) {
                    this.subCategoryOptions = [];
                    this.formData.category_id = '';
                    return;
                }

                this.isLoading.subcategories = true;
                try {
                    const response = await fetch(`{{ url('/api/categories') }}/${this.formData.main_category_id}/subcategories`);
                    if (response.ok) {
                        this.subCategoryOptions = await response.json();
                    }
                } catch (error) {
                    console.error('Alt kategoriler yüklenirken hata:', error);
                } finally {
                    this.isLoading.subcategories = false;
                }
            },

            // İlçe yükleme
            async fetchDistricts() {
                if (!this.formData.city_id) {
                    this.districtOptions = [];
                    this.neighborhoodOptions = [];
                    this.formData.district_id = '';
                    this.formData.neighborhood_id = '';
                    return;
                }

                this.isLoading.districts = true;
                try {
                    const response = await fetch(`{{ url('/api/cities') }}/${this.formData.city_id}/districts`);
                    if (response.ok) {
                        this.districtOptions = await response.json();
                    }
                } catch (error) {
                    console.error('İlçeler yüklenirken hata:', error);
                } finally {
                    this.isLoading.districts = false;
                }
            },

            // Mahalle yükleme
            async fetchNeighborhoods() {
                if (!this.formData.district_id) {
                    this.neighborhoodOptions = [];
                    this.formData.neighborhood_id = '';
                    return;
                }

                this.isLoading.neighborhoods = true;
                try {
                    const response = await fetch(`{{ url('/api/districts') }}/${this.formData.district_id}/neighborhoods`);
                    if (response.ok) {
                        this.neighborhoodOptions = await response.json();
                    }
                } catch (error) {
                    console.error('Mahalleler yüklenirken hata:', error);
                } finally {
                    this.isLoading.neighborhoods = false;
                }
            },

            // Placeholder metinleri
            subCategoryPlaceholder() {
                if (!this.formData.main_category_id) return 'Önce ana kategori seçin';
                if (this.isLoading.subcategories) return 'Yükleniyor...';
                if (this.subCategoryOptions.length === 0) return 'Alt kategori bulunamadı';
                return 'Alt kategori seçin';
            },

            districtPlaceholder() {
                if (!this.formData.city_id) return 'Önce şehir seçin';
                if (this.isLoading.districts) return 'Yükleniyor...';
                if (this.districtOptions.length === 0) return 'İlçe bulunamadı';
                return 'İlçe seçin';
            },

            neighborhoodPlaceholder() {
                if (!this.formData.district_id) return 'Önce ilçe seçin';
                if (this.isLoading.neighborhoods) return 'Yükleniyor...';
                if (this.neighborhoodOptions.length === 0) return 'Mahalle bulunamadı';
                return 'Mahalle seçin';
            },

            // Fiyat hesaplamaları
            calculatePricePerM2() {
                if (this.formData.price && this.formData.net_area) {
                    const pricePerM2 = parseFloat(this.formData.price) / parseFloat(this.formData.net_area);
                    return new Intl.NumberFormat('tr-TR', {
                        style: 'currency',
                        currency: 'TRY',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(pricePerM2) + '/m²';
                }
                return 'Hesaplanacak';
            },

            get pricePerM2() {
                return this.calculatePricePerM2();
            },

            formatPrice(price) {
                if (!price) return '₺0';
                return new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: 'TRY',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(price);
            },

            // Konum işlemleri
            async getCurrentLocation() {
                if (!navigator.geolocation) {
                    alert('Tarayıcınız konum özelliğini desteklemiyor.');
                    return;
                }

                this.isLoading.location = true;

                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        this.formData.latitude = position.coords.latitude.toFixed(8);
                        this.formData.longitude = position.coords.longitude.toFixed(8);
                        this.isLoading.location = false;
                    },
                    (error) => {
                        console.error('Konum alınırken hata:', error);
                        alert('Konum alınırken bir hata oluştu.');
                        this.isLoading.location = false;
                    }
                );
            },

            showOnMap() {
                if (this.formData.latitude && this.formData.longitude) {
                    const url = `https://www.google.com/maps?q=${this.formData.latitude},${this.formData.longitude}`;
                    window.open(url, '_blank');
                }
            },

            // Seçilen değerlerin isimlerini getir
            getSelectedCityName() {
                const citySelect = document.getElementById('city_id');
                return citySelect.options[citySelect.selectedIndex]?.text || '';
            },

            getSelectedDistrictName() {
                const district = this.districtOptions.find(d => d.id == this.formData.district_id);
                return district ? district.ilce_adi : '';
            },

            getSelectedNeighborhoodName() {
                const neighborhood = this.neighborhoodOptions.find(n => n.id == this.formData.neighborhood_id);
                return neighborhood ? neighborhood.mahalle_adi : '';
            }
        }))
    })
    </script>
    @endpush
</x-backend-layout>
