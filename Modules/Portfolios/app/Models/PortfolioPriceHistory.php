<?php

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
// use Modules\Portfolios\Database\Factories\PortfolioPriceHistoryFactory;

class PortfolioPriceHistory extends Model
{
    use HasFactory;

    protected $table = 'portfolio_price_history';

    protected $fillable = [
        'portfolio_id',
        'company_id',
        'old_price',
        'new_price',
        'changed_by',
    ];

    protected $casts = [
        'old_price' => 'decimal:2',
        'new_price' => 'decimal:2',
    ];

    /**
     * Get the portfolio that owns the price history.
     */
    public function portfolio(): BelongsTo
    {
        return $this->belongsTo(Portfolio::class, 'portfolio_id', 'id');
    }

    // protected static function newFactory(): PortfolioPriceHistoryFactory
    // {
    //     // return PortfolioPriceHistoryFactory::new();
    // }
}
