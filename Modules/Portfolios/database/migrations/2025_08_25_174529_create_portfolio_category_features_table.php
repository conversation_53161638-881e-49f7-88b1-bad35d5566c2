<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_category_features', function (Blueprint $table) {
            $table->id();
            $table->foreignId('feature_group_id')->constrained('portfolio_category_feature_groups')->cascadeOnDelete();
            $table->string('name'); // 'ADSL', 'Barbeku', 'Şömine', vs.
            $table->string('slug');
            $table->string('icon')->nullable(); // Font awesome icon class
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_category_features');
    }
};
