<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Modules\Portfolios\App\Models\PortfolioCategory;
use Illuminate\Http\JsonResponse;

class CategoryController extends Controller
{
    /**
     * Get main categories (parent_id is null).
     */
    public function getMainCategories(): JsonResponse
    {
        $categories = PortfolioCategory::whereNull('parent_id')
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json($categories);
    }

    /**
     * Get subcategories by parent category ID.
     */
    public function getSubCategories(int $categoryId): JsonResponse
    {
        $subcategories = PortfolioCategory::where('parent_id', $categoryId)
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json($subcategories);
    }
}
