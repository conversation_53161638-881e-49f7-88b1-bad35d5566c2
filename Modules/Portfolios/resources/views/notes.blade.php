<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Notlar & Yorumlar</h1>
                        <p class="text-gray-600 mt-2">{{ $portfolio->title }} - Not Geçmişi</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <a href="{{ route('portfolios.index') }}" 
                           class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                            ← Geri Dön
                        </a>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            + Yeni Not
                        </button>
                    </div>
                </div>
            </div>

            <!-- Add Note Form -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20 shadow-xl">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Yeni Not Ekle</h3>
                <form>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Not Başlığı</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Not başlığı...">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Not İçeriği</label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Not içeriği..."></textarea>
                    </div>
                    <div class="flex items-center gap-3">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            Not Ekle
                        </button>
                        <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>Genel Not</option>
                            <option>Önemli</option>
                            <option>Hatırlatma</option>
                            <option>Müşteri Notu</option>
                        </select>
                    </div>
                </form>
            </div>

            <!-- Notes List -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-xl">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Tüm Notlar</h3>
                
                <div class="space-y-4">
                    <div class="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-2 mb-2">
                                    <h4 class="font-semibold text-gray-900">Müşteri Görüşmesi</h4>
                                    <span class="px-2 py-1 bg-yellow-200 text-yellow-800 rounded-full text-xs">Önemli</span>
                                </div>
                                <p class="text-gray-700 mb-2">Ahmet Bey ile görüştük. Fiyat konusunda 50.000 TL indirim talep ediyor. Nakit ödeme yapacağını belirtti.</p>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span>Ali Veli</span>
                                    <span class="mx-2">•</span>
                                    <span>2 gün önce</span>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-2 mb-2">
                                    <h4 class="font-semibold text-gray-900">Teknik İnceleme</h4>
                                    <span class="px-2 py-1 bg-blue-200 text-blue-800 rounded-full text-xs">Genel</span>
                                </div>
                                <p class="text-gray-700 mb-2">Evin elektrik tesisatı yenilenmiş. Banyo ve mutfak tadilat görmüş. Genel durumu çok iyi.</p>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span>Mehmet Yılmaz</span>
                                    <span class="mx-2">•</span>
                                    <span>1 hafta önce</span>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="p-4 bg-green-50 rounded-xl border border-green-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-2 mb-2">
                                    <h4 class="font-semibold text-gray-900">İlk Değerlendirme</h4>
                                    <span class="px-2 py-1 bg-green-200 text-green-800 rounded-full text-xs">Hatırlatma</span>
                                </div>
                                <p class="text-gray-700 mb-2">Portföye yeni eklendi. Fotoğraflar çekildi, ölçümler alındı. Pazarlama materyalleri hazırlanacak.</p>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span>Fatma Demir</span>
                                    <span class="mx-2">•</span>
                                    <span>2 hafta önce</span>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endsection
</x-backend-layout>
