<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PortfolioCategoryFeature extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_category_features';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'feature_group_id',
        'name',
        'slug',
        'icon',
        'sort_order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the feature group this feature belongs to.
     */
    public function featureGroup(): BelongsTo
    {
        return $this->belongsTo(PortfolioCategoryFeatureGroup::class, 'feature_group_id', 'id');
    }

    /**
     * Get the category through the feature group.
     */
    public function category(): BelongsTo
    {
        return $this->featureGroup()->getRelated()->category();
    }

    /**
     * Get the portfolio features (pivot records).
     */
    public function portfolioFeatures(): HasMany
    {
        return $this->hasMany(PortfolioFeature::class, 'feature_id', 'id');
    }

    /**
     * Get the portfolios that have this feature.
     */
    public function portfolios(): BelongsToMany
    {
        return $this->belongsToMany(
            Portfolio::class,
            'portfolio_features',
            'feature_id',
            'portfolio_id'
        )->withTimestamps();
    }

    /**
     * Scope a query to only include active features.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope a query to filter by feature group.
     */
    public function scopeForGroup($query, int $groupId)
    {
        return $query->where('feature_group_id', $groupId);
    }

    /**
     * Get features formatted for select dropdown.
     */
    public static function getSelectOptions(int $groupId = null): array
    {
        $query = static::active()->orderBy('name');

        if ($groupId) {
            $query->where('feature_group_id', $groupId);
        }

        return $query->pluck('name', 'id')->toArray();
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set sort order when creating
        static::creating(function ($feature) {
            if (is_null($feature->sort_order)) {
                $maxOrder = static::where('feature_group_id', $feature->feature_group_id)->max('sort_order');
                $feature->sort_order = ($maxOrder ?? 0) + 1;
            }
        });

        // Prevent deletion if feature is used by portfolios
        static::deleting(function ($feature) {
            if ($feature->portfolioFeatures()->exists()) {
                throw new \Exception('Bu özellik portföylerde kullanıldığı için silinemez.');
            }
        });
    }
}
