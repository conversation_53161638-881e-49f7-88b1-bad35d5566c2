<?php

namespace Modules\UserManagement\App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\District;
use App\Models\Profile;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Auth;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;

class ProfileController extends Controller
{
    /**
     * Profil düzenleme sayfası.
     */
    public function edit(Request $request): View
    {
        $cities = City::all();

        return view('usermanagement::profile', [
            'user' => Auth::user(),
            'cities' => $cities,
        ]);
    }

    /**
     * Profil güncelleme.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = Auth::user();

        // Kullanıcı bilgilerini güncelle
        $userData = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        // Tema ayarları varsa ekle
        if ($request->has('theme_color')) {
            $userData['theme_color'] = $request->theme_color;
        }
        if ($request->has('sidebar_type')) {
            $userData['sidebar_type'] = $request->sidebar_type;
        }

        // Şifre değişikliği varsa
        if ($request->filled('current_password') && $request->filled('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return Redirect::route('profile')->with('status', 'error')->with('responseTitle', 'Hata')->with('responseMessage', 'Mevcut şifreniz hatalı.');
            }

            $userData['password'] = Hash::make($request->password);
        }

        $user->update($userData);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
            $user->save();
        }

        // Profile bilgilerini al veya oluştur
        $profile = Profile::firstOrCreate(
            ['user_id' => $user->id],
            []
        );

        // Profile verilerini güncelle
        $profileData = [
            'phone' => $request->phone,
            'whatsapp' => $request->whatsapp,
            'address' => $request->address,
            'city_id' => $request->city_id,
            'district_id' => $request->district_id,
            'bio' => $request->bio,
            'title' => $request->title,
            'birth_date' => $request->birth_date,
            'gender' => $request->gender,
        ];

        $profile->update($profileData);

        // Resim yükleme işlemi
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Klasör yapısı: Yıl-Ay
            $folderName = date('Y-m'); // örn: 2025-06
            $relativePath = 'images/users/' . $folderName;
            $destinationPath = public_path($relativePath);

            if (!File::exists($destinationPath)) {
                File::makeDirectory($destinationPath, 0755, true);
            }

            // Eski avatar varsa sil
            if ($profile->avatar_url && File::exists(public_path($profile->avatar_url))) {
                File::delete(public_path($profile->avatar_url));
            }

            // Benzersiz dosya adı
            $fileName = Str::uuid() . '.webp';
            $filePath = $destinationPath . '/' . $fileName;

            // Intervention v3 kullanımı
            $manager = new ImageManager(new Driver());

            $image = $manager->read($file->getPathname());

            $image->scale(width: 300); // oranı koruyarak genişliği 300px yapar

            $image->toWebp(quality: 90)->save($filePath);

            // Veritabanına kaydedilecek path
            $profile->avatar_url = $relativePath . '/' . $fileName;
            $profile->save();
        }

        return Redirect::route('profile')->with('status', 'success')->with('responseTitle', 'Başarılı')->with('responseMessage', 'Profiliniz başarıyla güncellendi.');
    }

    /**
     * İlçeleri AJAX ile getir.
     */
    public function getDistricts(Request $request)
    {
        $districts = District::where('il_id', $request->city_id)->get();
        $html = '<option value="">İlçe Seçiniz</option>';
        foreach ($districts as $district) {
            $selected = (Auth::user()->profile && Auth::user()->profile->district_id == $district->ilce_id) ? 'selected' : '';
            $html .= '<option value="' . $district->ilce_id . '" ' . $selected . '>' . $district->ilce_adi . '</option>';
        }
        return response($html);
    }
}
