<x-backend-layout>
    @section('content')
        <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-20">
                <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
                <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
                <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
                <!-- Header Section -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        <div>
                            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                                Kullanıcı Detayları
                            </h1>
                            <p class="text-gray-600 text-lg mt-2">{{ $targetUser->name }} kullanıcısının detay bilgileri</p>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center gap-4">
                            <a href="{{ route('users.edit', $targetUser->id) }}"
                               class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                </svg>
                                Düzenle
                            </a>
                            <a href="{{ route('users.index') }}"
                               class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                                </svg>
                                Geri Dön
                            </a>
                        </div>
                    </div>
                </div>

                <!-- User Profile Card -->
                <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl mb-8">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-6">
                        <div class="relative">
                            <div class="w-24 h-24 rounded-full ring-4 ring-blue-500/20 ring-offset-2 overflow-hidden bg-gray-100">
                                <img src="{{ $targetUser->profile?->avatar_url ? asset($targetUser->profile?->avatar_url) : 'https://ui-avatars.com/api/?name='.urlencode($targetUser->name).'&color=7c3aed&background=e0e7ff' }}"
                                     alt="{{ $targetUser->name }}"
                                     class="w-full h-full object-cover" />
                            </div>
                            <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-3 border-white flex items-center justify-center">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                        </div>

                        <div class="flex-1">
                            <h2 class="text-2xl font-bold text-gray-900">{{ $targetUser->name }}</h2>
                            <p class="text-gray-500 mb-2">{{ $targetUser->email }}</p>
                            @if($targetUser->profile && $targetUser->profile->title)
                                <p class="text-blue-600 font-medium mb-3">{{ $targetUser->profile->title }}</p>
                            @endif

                            <!-- Roles -->
                            <div class="flex flex-wrap gap-2">
                                @if($targetUser->companyRoles->count() > 0)
                                    @foreach($targetUser->companyRoles as $role)
                                        <span class="inline-block px-3 py-1 text-sm font-semibold bg-blue-100 text-blue-800 rounded-full">
                                            {{ $role->name }}
                                        </span>
                                    @endforeach
                                @else
                                    <span class="inline-block px-3 py-1 text-sm font-semibold bg-gray-100 text-gray-600 rounded-full">
                                        Rol atanmamış
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="text-right">
                            <div class="text-sm text-gray-500">Üyelik Tarihi</div>
                            <div class="font-semibold text-gray-900">{{ $targetUser->created_at->format('d.m.Y') }}</div>
                        </div>
                    </div>
                </div>

                <!-- User Details -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Personal Information -->
                    <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl">
                        <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                            <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Kişisel Bilgiler
                        </h3>

                        <div class="space-y-4">
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600 font-medium">Ad Soyad:</span>
                                <span class="text-gray-900">{{ $targetUser->name }}</span>
                            </div>

                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600 font-medium">Email:</span>
                                <span class="text-gray-900">{{ $targetUser->email }}</span>
                            </div>

                            @if($targetUser->profile && $targetUser->profile->title)
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Pozisyon:</span>
                                    <span class="text-gray-900">{{ $targetUser->profile->title }}</span>
                                </div>
                            @endif

                            @if($targetUser->profile && $targetUser->profile->birth_date)
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Doğum Tarihi:</span>
                                    <span class="text-gray-900">{{ date('d.m.Y', strtotime($targetUser->profile->birth_date)) }}</span>
                                </div>
                            @endif

                            @if($targetUser->profile && $targetUser->profile->gender)
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Cinsiyet:</span>
                                    <span class="text-gray-900">
                                        @if($targetUser->profile->gender == 'male') Erkek
                                        @elseif($targetUser->profile->gender == 'female') Kadın
                                        @else Diğer
                                        @endif
                                    </span>
                                </div>
                            @endif

                            <div class="flex justify-between items-center py-3">
                                <span class="text-gray-600 font-medium">Kayıt Tarihi:</span>
                                <span class="text-gray-900">{{ $targetUser->created_at->format('d.m.Y H:i') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl">
                        <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                            <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                            </svg>
                            İletişim Bilgileri
                        </h3>

                        <div class="space-y-4">
                            @if($targetUser->profile && $targetUser->profile->phone)
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Telefon:</span>
                                    <span class="text-gray-900">{{ $targetUser->profile->phone }}</span>
                                </div>
                            @endif

                            @if($targetUser->profile && $targetUser->profile->whatsapp)
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">WhatsApp:</span>
                                    <span class="text-gray-900">{{ $targetUser->profile->whatsapp }}</span>
                                </div>
                            @endif

                            @if($targetUser->profile && $targetUser->profile->city)
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">Şehir:</span>
                                    <span class="text-gray-900">{{ $targetUser->profile->city->il_adi ?? 'Belirtilmemiş' }}</span>
                                </div>
                            @endif

                            @if($targetUser->profile && $targetUser->profile->district)
                                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium">İlçe:</span>
                                    <span class="text-gray-900">{{ $targetUser->profile->district->ilce_adi ?? 'Belirtilmemiş' }}</span>
                                </div>
                            @endif

                            @if($targetUser->profile && $targetUser->profile->address)
                                <div class="py-3 border-b border-gray-100">
                                    <span class="text-gray-600 font-medium block mb-2">Adres:</span>
                                    <span class="text-gray-900">{{ $targetUser->profile->address }}</span>
                                </div>
                            @endif

                            @if($targetUser->profile && $targetUser->profile->bio)
                                <div class="py-3">
                                    <span class="text-gray-600 font-medium block mb-2">Hakkında:</span>
                                    <span class="text-gray-900">{{ $targetUser->profile->bio }}</span>
                                </div>
                            @endif

                            @if(!$targetUser->profile || (!$targetUser->profile->phone && !$targetUser->profile->whatsapp && !$targetUser->profile->address && !$targetUser->profile->bio))
                                <div class="text-center py-8">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                                    </svg>
                                    <p class="text-gray-500">İletişim bilgileri henüz tamamlanmamış</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Permissions Section -->
                @if($targetUser->companyRoles->count() > 0)
                    <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl mt-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                            <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                            </svg>
                            Roller ve İzinler
                        </h3>

                        <div class="space-y-6">
                            @foreach($targetUser->companyRoles as $role)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-lg font-semibold text-gray-900">{{ $role->name }}</h4>
                                        <span class="px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full">
                                            {{ $role->permissions->count() }} İzin
                                        </span>
                                    </div>

                                    @if($role->description)
                                        <p class="text-gray-600 mb-4">{{ $role->description }}</p>
                                    @endif

                                    @if($role->permissions->count() > 0)
                                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                                            @foreach($role->permissions as $permission)
                                                <div class="flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-lg">
                                                    <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                                    </svg>
                                                    <span class="text-sm text-gray-700">{{ $permission->label }}</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endsection
</x-backend-layout>
