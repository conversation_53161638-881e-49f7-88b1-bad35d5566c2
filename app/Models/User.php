<?php

namespace App\Models;

 use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Traits\HasCompanyPermissions;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasCompanyPermissions;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'company_id',
        'theme_color',
        'sidebar_type',
        'sidebar_collapsed',
        'avatar_url',
        'is_superadmin'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'sidebar_collapsed' => 'boolean',
            'is_superadmin' => 'boolean',
        ];
    }

    /**
     * Get available theme colors.
     */
    public static function getThemeColors(): array
    {
        return [
            'blue' => ['name' => 'Mavi', 'primary' => '#3b82f6', 'dark' => '#1e40af'],
            'green' => ['name' => 'Yeşil', 'primary' => '#10b981', 'dark' => '#059669'],
            'purple' => ['name' => 'Mor', 'primary' => '#8b5cf6', 'dark' => '#7c3aed'],
            'red' => ['name' => 'Kırmızı', 'primary' => '#ef4444', 'dark' => '#dc2626'],
            'orange' => ['name' => 'Turuncu', 'primary' => '#f59e0b', 'dark' => '#d97706'],
            'teal' => ['name' => 'Deniz Yeşili', 'primary' => '#14b8a6', 'dark' => '#0d9488'],
        ];
    }

    /**
     * Get the current theme configuration.
     */
    public function getThemeConfig(): array
    {
        $colors = self::getThemeColors();
        $themeColor = $colors[$this->theme_color] ?? $colors['blue'];

        return [
            'color' => $this->theme_color,
            'sidebar_type' => $this->sidebar_type,
            'sidebar_collapsed' => $this->sidebar_collapsed,
            'primary_color' => $themeColor['primary'],
            'primary_dark' => $themeColor['dark'],
        ];
    }

    /**
     * Get the user's profile.
     */
    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    /**
     * Get the company that the user belongs to.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get all staff invitations sent by this user.
     */
    public function sentInvitations()
    {
        return $this->hasMany(StaffInvitation::class, 'invited_by_user_id');
    }

    public function sendEmailVerificationNotification()
    {
        $this->notify(new \App\Notifications\CustomVerifyEmail());
    }

    /**
     * Check if user is a superadmin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->is_superadmin;
    }

    /**
     * Make user a superadmin.
     */
    public function makeSuperAdmin(): void
    {
        $this->update(['is_superadmin' => true]);
    }

    /**
     * Remove superadmin privileges.
     */
    public function removeSuperAdmin(): void
    {
        $this->update(['is_superadmin' => false]);
    }

    /**
     * Scope to get only superadmins.
     */
    public function scopeSuperAdmins($query)
    {
        return $query->where('is_superadmin', true);
    }

    /**
     * Scope to get only regular users (non-superadmins).
     */
    public function scopeRegularUsers($query)
    {
        return $query->where('is_superadmin', false);
    }
}
