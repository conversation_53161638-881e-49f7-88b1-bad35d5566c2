<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use App\Models\Company;

class PortfolioDocument extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_documents';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'company_id',
        'documentable_type',
        'documentable_id',
        'path',
        'name',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the company that owns the document.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /**
     * Get the parent documentable model (portfolio, etc.).
     */
    public function documentable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to filter by company.
     */
    public function scopeForCompany($query, string $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Get the full URL of the document.
     */
    public function getUrlAttribute(): string
    {
        return asset('storage/' . $this->path);
    }

    /**
     * Get the filename from path.
     */
    public function getFilenameAttribute(): string
    {
        return $this->name ?: basename($this->path);
    }

    /**
     * Get the file extension.
     */
    public function getExtensionAttribute(): string
    {
        return pathinfo($this->path, PATHINFO_EXTENSION);
    }

    /**
     * Get the file size in bytes.
     */
    public function getSizeAttribute(): ?int
    {
        $fullPath = storage_path('app/public/' . $this->path);
        return file_exists($fullPath) ? filesize($fullPath) : null;
    }

    /**
     * Get the formatted file size.
     */
    public function getFormattedSizeAttribute(): string
    {
        $size = $this->size;
        if (!$size) return 'Bilinmiyor';

        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;

        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }

        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Check if the document is an image.
     */
    public function isImage(): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return in_array(strtolower($this->extension), $imageExtensions);
    }

    /**
     * Check if the document is a PDF.
     */
    public function isPdf(): bool
    {
        return strtolower($this->extension) === 'pdf';
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Delete physical file when model is deleted
        static::deleting(function ($document) {
            if (file_exists(storage_path('app/public/' . $document->path))) {
                unlink(storage_path('app/public/' . $document->path));
            }
        });
    }
}
