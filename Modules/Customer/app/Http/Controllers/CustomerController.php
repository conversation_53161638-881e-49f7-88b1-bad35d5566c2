<?php

namespace Modules\Customer\App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Traits\HasDropdownActions;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Modules\Customer\App\Models\Customer;
use Modules\Customer\App\Models\CustomerGroup;
use Modules\Customer\App\Models\CustomerType;
use Modules\Customer\App\Models\CustomerSource;
use Modules\Customer\App\Models\CustomerOccupations;
use App\Models\User;
use Yajra\DataTables\Facades\DataTables;

class CustomerController extends Controller
{
    use HasDropdownActions;
    /**
     * Müşteri işlemleri dropdown menüsü oluştur
     */
    private function buildCustomerActionsDropdown($customer, $user)
    {
        $dropdownId = 'dropdown-' . $customer->id;

        // İzin kontrolleri
        $canViewNotes = $this->checkPermission($user, 'view_customers');
        $canCreatePresentation = $this->checkPermission($user, 'manage_customers');
        $canManageActions = $this->checkPermission($user, 'manage_customers');
        $canViewReports = $this->checkPermission($user, 'view_reports');
        $canDelete = $this->checkRole($user, 'Şirket Sahibi');

        // Dropdown menü öğeleri
        $menuItems = [];

        if ($canViewNotes) {
            $menuItems[] = [
                'type' => 'link',
                'action' => "openCustomerNotes('{$customer->id}')",
                'icon' => 'notes',
                'title' => 'Müşteri Notları',
                'description' => 'Notları görüntüle ve ekle',
                'color' => 'blue'
            ];
        }

        if ($canCreatePresentation) {
            $menuItems[] = [
                'type' => 'link',
                'action' => "createPresentation('{$customer->id}')",
                'icon' => 'presentation',
                'title' => 'Sunum Oluştur',
                'description' => 'Müşteriye özel sunum hazırla',
                'color' => 'purple'
            ];
        }

        if ($canManageActions) {
            $menuItems[] = [
                'type' => 'link',
                'action' => "customerActions('{$customer->id}')",
                'icon' => 'actions',
                'title' => 'Müşteri Aksiyonları',
                'description' => 'Görevler ve takip işlemleri',
                'color' => 'green'
            ];

            $menuItems[] = [
                'type' => 'link',
                'action' => "customerRequests('{$customer->id}')",
                'icon' => 'requests',
                'title' => 'Müşteri Talepleri',
                'description' => 'Talep ve istekleri yönet',
                'color' => 'orange'
            ];
        }

        if ($canViewReports) {
            $menuItems[] = [
                'type' => 'link',
                'action' => "customerReports('{$customer->id}')",
                'icon' => 'reports',
                'title' => 'Müşteri Raporları',
                'description' => 'Detaylı analiz ve raporlar',
                'color' => 'indigo'
            ];
        }

        if ($canDelete) {
            $menuItems[] = [
                'type' => 'separator'
            ];

            $menuItems[] = [
                'type' => 'button',
                'action' => "deleteCustomer('{$customer->id}')",
                'icon' => 'delete',
                'title' => 'Müşteriyi Sil',
                'description' => 'Kalıcı olarak kaldır',
                'color' => 'red'
            ];
        }

        return $this->renderDropdownMenu($dropdownId, $menuItems, 'Müşteri İşlemleri');
    }



    /**
     * DataTable AJAX endpoint
     */
    public function datatable(Request $request)
    {
        $user = Auth::user();

        // Şirket müşterilerini getir
        $query = Customer::where('company_id', $user->company_id)
            ->with(['customerGroup', 'customerTypes', 'agent']);

        // Filtreleri uygula
        if ($request->filled('status_filter')) {
            $query->where('status', $request->status_filter);
        }

        if ($request->filled('group_filter')) {
            $query->where('customer_group_id', $request->group_filter);
        }

        if ($request->filled('type_filter')) {
            $query->whereHas('customerTypes', function ($q) use ($request) {
                $q->where('customer_type_id', $request->type_filter);
            });
        }

        if ($request->filled('agent_filter')) {
            $query->where('agent_id', $request->agent_filter);
        }

        // URL parametrelerinden gelen filtreler
        if ($request->filled('type')) {
            $types = is_array($request->type) ? $request->type : explode(',', $request->type);
            $query->whereHas('customerTypes', function ($q) use ($types) {
                $q->whereIn('customer_type_id', $types);
            });
        }

        if ($request->filled('agent')) {
            $query->where('agent_id', $request->agent);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        return DataTables::eloquent($query)
            ->filter(function ($query) use ($request) {
                // DataTable'ın kendi arama parametresini kullan
                if ($request->filled('search.value')) {
                    $search = $request->input('search.value');
                    $query->where(function ($q) use ($search) {
                        $q->where('first_name', 'like', "%{$search}%")
                          ->orWhere('last_name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('phone', 'like', "%{$search}%")
                          ->orWhere('company_name', 'like', "%{$search}%");
                    });
                }

                // Ek custom arama terimi (eğer varsa)
                if ($request->filled('search_term')) {
                    $search = $request->search_term;
                    $query->where(function ($q) use ($search) {
                        $q->where('first_name', 'like', "%{$search}%")
                          ->orWhere('last_name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('phone', 'like', "%{$search}%")
                          ->orWhere('company_name', 'like', "%{$search}%");
                    });
                }
            })
                        ->addColumn('customer_info', function ($customer) {
                $initials = strtoupper(substr($customer->first_name, 0, 1) . substr($customer->last_name, 0, 1));
                $avatar = '<div class="customer-avatar flex items-center justify-center w-12 h-12 text-white rounded-full font-bold text-sm">' . $initials . '</div>';

                $name = '<div class="font-bold text-gray-900 text-base">' . $customer->first_name . ' ' . $customer->last_name . '</div>';
                $company = $customer->company_name ? '<div class="text-sm text-gray-500 font-medium">' . $customer->company_name . '</div>' : '';

                return '<div class="flex items-center gap-4">' . $avatar . '<div>' . $name . $company . '</div></div>';
            })
            ->addColumn('contact_info', function ($customer) {
                $email = $customer->email ? '<div class="flex items-center gap-2 text-sm"><svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 21.75 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" /></svg>' . $customer->email . '</div>' : '';
                $phone = '<div class="flex items-center gap-2 text-sm"><svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" /></svg>' . $customer->phone . '</div>';

                return '<div class="space-y-1">' . $email . $phone . '</div>';
            })
            ->addColumn('group_type', function ($customer) {
                $group = $customer->customerGroup ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' . $customer->customerGroup->name . '</span>' : '';

                $types = '';
                if ($customer->customerTypes->count() > 0) {
                    $typesList = $customer->customerTypes->map(function ($type) {
                        return '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">' . $type->name . '</span>';
                    })->implode(' ');
                    $types = '<div class="flex flex-wrap gap-1 mt-1">' . $typesList . '</div>';
                }

                return '<div>' . $group . $types . '</div>';
            })
            ->addColumn('agent_info', function ($customer) {
                if ($customer->agent) {
                    $initial = strtoupper(substr($customer->agent->name, 0, 1));
                    return '<div class="flex items-center gap-2"><div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-xs font-semibold text-gray-600">' . $initial . '</div><span class="text-sm">' . $customer->agent->name . '</span></div>';
                }
                return '<span class="text-gray-400 text-sm">Atanmamış</span>';
            })
                        ->addColumn('status_badge', function ($customer) {
                $statusConfig = [
                    'active' => ['bg-green-100', 'text-green-800', 'Aktif', '🟢'],
                    'inactive' => ['bg-red-100', 'text-red-800', 'Pasif', '🔴'],
                    'potential' => ['bg-yellow-100', 'text-yellow-800', 'Potansiyel', '🟡']
                ];

                $config = $statusConfig[$customer->status] ?? ['bg-gray-100', 'text-gray-800', 'Bilinmiyor', '⚫'];

                return '<span class="status-badge inline-flex items-center ' . $config[0] . ' ' . $config[1] . '">' . $config[3] . ' ' . $config[2] . '</span>';
            })
            ->addColumn('registration_date', function ($customer) {
                return '<div class="flex flex-col"><span class="text-sm font-medium">' . $customer->created_at->format('d.m.Y') . '</span><span class="text-xs text-gray-400">' . $customer->created_at->format('H:i') . '</span></div>';
            })
                        ->addColumn('actions', function ($customer) {
                $user = Auth::user();

                $actions = '<div class="flex items-center gap-2">';

                // Ana işlem butonları (izin kontrolü ile)
                if ($this->checkPermission($user, 'view_customers')) {
                $actions .= '<a href="' . route('customers.show', $customer->id) . '" class="action-btn action-link inline-flex items-center justify-center w-9 h-9 bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 rounded-lg transition-all duration-200" title="Görüntüle" data-action="navigate">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                    </svg>
                </a>';
                }

                if ($this->checkPermission($user, 'edit_customers')) {
                $actions .= '<a href="' . route('customers.edit', $customer->id) . '" class="action-btn action-link inline-flex items-center justify-center w-9 h-9 bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 rounded-lg transition-all duration-200" title="Düzenle" data-action="navigate">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
                    </svg>
                </a>';
                }

                // Dropdown menü (modüler method ile)
                $actions .= $this->buildCustomerActionsDropdown($customer, $user);

                $actions .= '</div>';

                return $actions;
            })
            ->rawColumns(['customer_info', 'contact_info', 'group_type', 'agent_info', 'status_badge', 'registration_date', 'actions'])
            ->make(true);
    }

    /**
     * Müşteri listesi
     */
    public function index(Request $request): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'manage_customers')) {
            abort(403, 'Bu sayfaya erişim yetkiniz yok.');
        }

        // Filtre dropdown'ları için veri
        $customerTypes = CustomerType::all();
        $agents = User::where('company_id', $user->company_id)->get();

        return view('customer::index', compact('customerTypes', 'agents'));
    }

    /**
     * Yeni müşteri formu
     */
    public function create(): View
    {
        $user = Auth::user();
        if (!$user->hasCompanyPermission($user->company_id, 'create_customers')) {
            abort(403, 'Bu sayfaya erişim yetkiniz yok.');
        }

        $customerGroups = CustomerGroup::where('company_id', $user->company_id)->get();
        $customerTypes = CustomerType::all();
        $agents = User::where('company_id', $user->company_id)->get();
        $customerSources = CustomerSource::all();
        $customerOccupations = CustomerOccupations::all();

        return view('customer::create', compact('customerGroups', 'customerTypes', 'agents', 'customerSources', 'customerOccupations'));
    }

    /**
     * Müşteri kaydet
     */
    public function store(Request $request): RedirectResponse
    {
        $user = Auth::user();

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'required|string|max:20',
            'customer_group_id' => 'nullable|exists:customer_groups,id',
            'customer_types' => 'array',
            'customer_types.*' => 'exists:customer_types,id',
            'agent_id' => 'nullable|integer|exists:users,id',
            'status' => 'required|in:active,inactive,potential',
            'gender' => 'nullable|in:male,female,other',
            'birth_date' => 'nullable|date',
            'company_name' => 'nullable|string|max:255',
            'whatsapp' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'source' => 'nullable',
            'notes' => 'nullable|string',
            'wants_sms' => 'boolean',
            'wants_email' => 'boolean',
            'minimum_income' => 'nullable|numeric|min:0',
            'investment_budget' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:3',
            'nationality' => 'nullable|string|max:255',
            'occupation' => 'nullable',
        ]);

        // Pivot ile alakalı olmayan verileri ayıklıyoruz
    $data = collect($validated)->except('customer_types')->toArray();
    $data['company_id'] = $user->company_id;

    // Müşteriyi oluştur
    $customer = Customer::create($data);

    // Pivot tablosuna bağla
    if (!empty($validated['customer_types'])) {
        $customer->customerTypes()->sync($validated['customer_types']);
    }

        return redirect()->route('customers.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Müşteri başarıyla oluşturuldu.'
            ]);
    }

    /**
     * Müşteri detayı
     */
    public function show($id): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'view_customers')) {
            abort(403, 'Bu sayfaya erişim yetkiniz yok.');
        }

        $customer = Customer::where('company_id', $user->company_id)
            ->with(['customerGroup', 'customerTypes', 'agent'])
            ->findOrFail($id);

        return view('customer::show', compact('customer'));
    }

    /**
     * Müşteri düzenleme formu
     */
    public function edit($id): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'edit_customers')) {
            abort(403, 'Bu sayfaya erişim yetkiniz yok.');
        }

        $customer = Customer::where('company_id', $user->company_id)
            ->with(['customerGroup', 'customerTypes', 'agent'])
            ->findOrFail($id);

        $customerGroups = CustomerGroup::where('company_id', $user->company_id)->get();
        $customerTypes = CustomerType::all();
        $agents = User::where('company_id', $user->company_id)->get();
        $customerSources = CustomerSource::all();
        $customerOccupations = CustomerOccupations::all();

        return view('customer::edit', compact('customer', 'customerGroups', 'customerTypes', 'agents', 'customerSources', 'customerOccupations'));
    }

    /**
     * Müşteri güncelle
     */
    public function update(Request $request, $id): RedirectResponse
    {
        $user = Auth::user();

        $customer = Customer::where('company_id', $user->company_id)->findOrFail($id);

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'required|string|max:20',
            'customer_group_id' => 'nullable|exists:customer_groups,id',
            'customer_types' => 'array',
            'customer_types.*' => 'exists:customer_types,id',
            'agent_id' => 'nullable|integer|exists:users,id',
            'status' => 'required|in:active,inactive,potential',
            'gender' => 'nullable|in:male,female,other',
            'birth_date' => 'nullable|date',
            'company_name' => 'nullable|string|max:255',
            'whatsapp' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'source' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'wants_sms' => 'boolean',
            'wants_email' => 'boolean',
            'minimum_income' => 'nullable|numeric|min:0',
            'investment_budget' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:3',
            'nationality' => 'nullable|string|max:255',
            'occupation' => 'nullable|string|max:255',
        ]);

        // Pivot ile alakalı olmayan verileri ayıklıyoruz
        $data = collect($validated)->except('customer_types')->toArray();

        //dd($data);

        $customer->update($data);

        // Müşteri tiplerini güncelle
        if ($request->has('customer_types')) {
            $customer->customerTypes()->sync($request->customer_types ?? []);
        } else {
            $customer->customerTypes()->sync([]);
        }

        return redirect()->route('customers.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Müşteri başarıyla güncellendi.'
            ]);
    }

    /**
     * Müşteri sil
     */
    public function destroy($id): RedirectResponse
    {
        $user = Auth::user();

        // Sadece şirket sahibi müşteri silebilir
        if (!$user->hasCompanyRole($user->company_id, 'Şirket Sahibi')) {
            abort(403, 'Müşteri silme yetkiniz yok.');
        }

        $customer = Customer::where('company_id', $user->company_id)->findOrFail($id);

        $customer->delete();

        return redirect()->route('customers')
            ->with('success', 'Müşteri başarıyla silindi.');
    }
}
