<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // 1 - Global İzinler
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // Örn: manage_users
            $table->string('label')->nullable(); // Görsel isim: Kullanıcı Yönetimi
            $table->timestamps();
        });

        // 2 - Global Roller (opsiyonel)
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // Örn: super_admin
            $table->string('label')->nullable();
            $table->timestamps();
        });

        // 3 - Şirketlere Özel Roller
        Schema::create('company_roles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('company_id');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->string('name'); // Örn: Satış Müdürü
            $table->string('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['company_id', 'name']);
        });

        // 4 - Şirket Rolleri ↔ İzinler
        Schema::create('company_role_permissions', function (Blueprint $table) {
            $table->uuid('company_role_id');
            $table->foreign('company_role_id')->references('id')->on('company_roles')->onDelete('cascade');

            $table->unsignedBigInteger('permission_id');
            $table->foreign('permission_id')->references('id')->on('permissions')->onDelete('cascade');

            $table->primary(['company_role_id', 'permission_id']);
        });

        // 5 - Şirket Kullanıcı Rolleri
        Schema::create('company_user_roles', function (Blueprint $table) {
            $table->uuid('company_id');
            $table->unsignedBigInteger('user_id');
            $table->uuid('company_role_id');

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('company_role_id')->references('id')->on('company_roles')->onDelete('cascade');

            $table->timestamps();
            $table->primary(['company_id', 'user_id', 'company_role_id']);
        });

        // 6 - Şirket Kullanıcı Özel İzinleri
        Schema::create('company_user_permissions', function (Blueprint $table) {
            $table->uuid('company_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('permission_id');

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('permission_id')->references('id')->on('permissions')->onDelete('cascade');

            $table->timestamps();
            $table->primary(['company_id', 'user_id', 'permission_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company_user_permissions');
        Schema::dropIfExists('company_user_roles');
        Schema::dropIfExists('company_role_permissions');
        Schema::dropIfExists('company_roles');
        Schema::dropIfExists('roles');
        Schema::dropIfExists('permissions');
    }
};
