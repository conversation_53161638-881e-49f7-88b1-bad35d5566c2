{"aria": {"paginate": {"first": "İlk", "last": "Son", "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON>"}}, "autoFill": {"cancel": "İptal", "fill": "<PERSON><PERSON><PERSON><PERSON><PERSON> <i>%d</i> ile doldur", "fillHorizontal": "Hücreleri yatay o<PERSON>ak doldur", "fillVertical": "Hücreleri dikey olarak doldur", "info": ""}, "buttons": {"collection": "Koleksiyon <span class=\"ui-button-icon-primary ui-icon ui-icon-triangle-1-s\"></span>", "colvis": "<PERSON><PERSON>tun görünürlüğü", "colvisRestore": "Görünürlüğü eski haline getir", "copy": "Kopyala", "copyKeys": "Tablodaki veriyi kopyalamak için CTRL veya u2318 + C tuşlarına basınız. İptal etmek için bu mesaja tıklayın veya escape tuşuna basın.", "copySuccess": {"_": "%ds satır panoya kopyalandı", "1": "1 satır panoya kopyalandı"}, "copyTitle": "<PERSON><PERSON> k<PERSON>", "createState": "Şuanki Görünümü Kaydet", "csv": "CSV", "excel": "Excel", "pageLength": {"_": "%d <PERSON>ı<PERSON>", "-1": "Bütün satırları göster"}, "pdf": "PDF", "print": "Yazdır", "removeAllStates": "<PERSON><PERSON><PERSON> Görünümleri Sil", "removeState": "Aktif <PERSON>l", "renameState": "Aktif <PERSON>ü<PERSON>ün Adını Değiştir", "savedStates": "<PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>", "stateRestore": "Görünüm -&gt; %d", "updateState": "Aktif <PERSON><PERSON><PERSON> Gü<PERSON>"}, "datetime": {"amPm": {"0": "öö", "1": "ös"}, "hours": "Saat", "minutes": "Dakika", "months": {"0": "Ocak", "1": "Ş<PERSON><PERSON>", "10": "Kasım", "11": "Aralık", "2": "Mart", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "Haziran", "6": "Temmuz", "7": "<PERSON><PERSON><PERSON><PERSON>", "8": "<PERSON><PERSON><PERSON><PERSON>", "9": "<PERSON><PERSON>"}, "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "unknown": "Bilinmeyen", "weekdays": {"0": "Pzt", "1": "Sal", "2": "Çar", "3": "Per", "4": "Cum", "5": "Cmt", "6": "Paz"}}, "decimal": "", "editor": {"close": "Ka<PERSON><PERSON>", "create": {"button": "<PERSON><PERSON>", "submit": "<PERSON><PERSON>", "title": "<PERSON><PERSON> kayıt o<PERSON>"}, "edit": {"button": "<PERSON><PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "Kaydı düzenle"}, "error": {"system": "Bir sistem hatası oluştu (Ayrıntılı bilgi)"}, "multi": {"info": "Seçili kayıtlar bu alanda farklı değerler içeriyor. Seçili kayıtların hepsinde bu alana aynı değeri atamak için buraya tıklayın; a<PERSON><PERSON> halde her kayıt bu alanda kendi değerini koruyacak.", "noMulti": "Bu alan bir grup olarak değil ancak tekil olarak düzenlenebilir.", "restore": "Değişiklikleri geri al", "title": "<PERSON><PERSON><PERSON> değer"}, "remove": {"button": "Sil", "confirm": {"_": "%d adet kaydı silmek istediğinize emin misiniz?", "1": "Bu kaydı silmek istediğinizden emin misiniz?"}, "submit": "Sil", "title": "Kayıtları sil"}}, "emptyTable": "Tabloda veri bulun<PERSON>yor", "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor", "infoEmpty": "Kayıt yok", "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)", "infoPostFix": "", "infoThousands": ".", "lengthMenu": "Sayfada _MENU_ kayıt göster", "loadingRecords": "Yükleniyor...", "processing": "İşleniyor...", "search": "Ara:", "searchBuilder": {"add": "Ko<PERSON><PERSON> Ekle", "button": {"_": "Arama <PERSON>ucu (%d)", "0": "<PERSON><PERSON>"}, "clearAll": "<PERSON><PERSON><PERSON><PERSON>", "condition": "Koşul", "conditions": {"array": {"contains": "İçerir", "empty": "Boş", "equals": "Eş<PERSON><PERSON>", "not": "<PERSON><PERSON><PERSON><PERSON>", "notEmpty": "Do<PERSON>", "without": "<PERSON><PERSON>"}, "date": {"after": "Sonra", "before": "Önce", "between": "Arasında", "empty": "Boş", "equals": "Eş<PERSON><PERSON>", "not": "<PERSON><PERSON><PERSON><PERSON>", "notBetween": "Dışında", "notEmpty": "Do<PERSON>"}, "number": {"between": "Arasında", "empty": "Boş", "equals": "Eş<PERSON><PERSON>", "gt": "Büyüktür", "gte": "Büyük eşittir", "lt": "Küçüktür", "lte": "Küçük eşittir", "not": "<PERSON><PERSON><PERSON><PERSON>", "notBetween": "Dışında", "notEmpty": "Do<PERSON>"}, "string": {"contains": "İçerir", "empty": "Boş", "endsWith": "<PERSON>le biter", "equals": "Eş<PERSON><PERSON>", "not": "<PERSON><PERSON><PERSON><PERSON>", "notContains": "İçermeyen", "notEmpty": "Do<PERSON>", "notEndsWith": "Bitmeyen", "notStartsWith": "Başlamayan", "startsWith": "<PERSON><PERSON>"}}, "data": "<PERSON><PERSON>", "deleteTitle": "Filtreleme kuralını silin", "leftTitle": "Kriteri dışarı çı<PERSON>t", "logicAnd": "ve", "logicOr": "veya", "rightTitle": "K<PERSON>ri i<PERSON> al", "title": {"_": "Arama <PERSON>ucu (%d)", "0": "<PERSON><PERSON>"}, "value": "<PERSON><PERSON><PERSON>"}, "searchPanes": {"clearMessage": "<PERSON><PERSON><PERSON>", "collapse": {"_": "<PERSON><PERSON> (%d)", "0": "<PERSON><PERSON>"}, "collapseMessage": "Tümünü <PERSON>", "count": "{total}", "countFiltered": "{shown}/{total}", "emptyPanes": "<PERSON><PERSON>k", "loadMessage": "<PERSON><PERSON> ...", "showMessage": "Tümünü <PERSON>ö<PERSON>", "title": "<PERSON><PERSON><PERSON> filtreler - %d"}, "searchPlaceholder": "", "select": {"cells": {"_": "%d hücre seçildi", "0": "", "1": "1 hücre seçildi"}, "columns": {"_": "%d s<PERSON><PERSON>", "0": "", "1": "1 sütun seçildi"}, "rows": {"_": "%d kayıt seçildi", "0": "", "1": "1 kayıt seçildi"}}, "stateRestore": {"creationModal": {"button": "<PERSON><PERSON>", "columns": {"search": "Kolon Araması", "visible": "Kolon Görünümü"}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "Sıralama", "paging": "<PERSON><PERSON><PERSON><PERSON>", "scroller": "<PERSON><PERSON><PERSON><PERSON> (Scrool)", "search": "<PERSON><PERSON>", "searchBuilder": "<PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "toggleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "duplicateError": "Bu Görünüm Daha Önce Tanı<PERSON>lanmış", "emptyError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emptyStates": "Herhangi Bir Görünüm <PERSON>", "removeConfirm": "Görünümü silmek istediğinize emin misiniz?", "removeError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removeJoiner": "ve", "removeSubmit": "Sil", "removeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renameButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renameLabel": "Görünüme Yeni İsim Ver -&gt; %s:", "renameTitle": "Görünüm İsmini <PERSON>tir"}, "thousands": ".", "zeroRecords": "Eşleşen kayıt bulunamadı"}