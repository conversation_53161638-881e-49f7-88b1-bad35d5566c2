<?php

namespace Modules\UserManagement\App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\City;
use App\Models\District;
use App\Models\Profile;
use App\Models\CompanyRole;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class UserManagementController extends Controller
{
    /**
     * Kullanıcı listesi (şirket çalışanları).
     */
    public function index(Request $request): View
    {
        $user = Auth::user();

        // Sadece kullanıcı yönetimi iznine sahip olanlar görebilir
        if (!$user->hasCompanyPermission($user->company_id, 'manage_users')) {
            abort(403, 'Kullanıcı yönetimi yetkiniz yok.');
        }

        // Şirket rollerini getir (filtreleme için)
        $companyRoles = CompanyRole::where('company_id', $user->company_id)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Kullanıcı sorgusu oluştur
        $query = User::where('company_id', $user->company_id)
            ->with(['profile', 'companyRoles']);

        // Arama filtresi
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Rol filtresi
        if ($request->filled('role_filter')) {
            $roleId = $request->get('role_filter');
            $query->whereHas('companyRoles', function ($q) use ($roleId, $user) {
                $q->where('company_roles.id', $roleId)
                  ->where('company_user_roles.company_id', $user->company_id);
            });
        }

        // Kullanıcıları getir (kayıt tarihine göre sırala)
        $users = $query->orderBy('created_at', 'desc')->paginate(15);

        // Mevcut kullanıcının şirket sahibi olup olmadığını kontrol et
        $isCompanyOwner = $user->hasCompanyRole($user->company_id, 'Şirket Sahibi');

        return view('usermanagement::index', compact('users', 'companyRoles', 'isCompanyOwner'));
    }

    /**
     * Yeni kullanıcı oluşturma sayfası.
     */
    public function create(): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'create_users')) {
            abort(403, 'Kullanıcı oluşturma yetkiniz yok.');
        }

        $cities = City::orderBy('il_adi')->get();
        $companyRoles = CompanyRole::where('company_id', $user->company_id)
            ->where('is_active', true)
            ->get();

        return view('usermanagement::create', compact('cities', 'companyRoles'));
    }

    /**
     * Yeni kullanıcı kaydetme.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'create_users')) {
            abort(403, 'Kullanıcı oluşturma yetkiniz yok.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'title' => 'nullable|string|max:100',
            'role_id' => 'required|exists:company_roles,id',
        ]);

        // Kullanıcı oluştur
        $newUser = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'company_id' => $user->company_id,
        ]);

        // Profil oluştur
        Profile::create([
            'user_id' => $newUser->id,
            'phone' => $request->phone,
            'title' => $request->title,
            'city_id' => $request->city_id,
            'district_id' => $request->district_id,
        ]);

        // Rol ata
        $newUser->assignCompanyRole($user->company_id, $request->role_id);

        return redirect()->route('users.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Kullanıcı başarıyla oluşturuldu.'
            ]);
    }

    /**
     * Kullanıcı detayları.
     */
    public function show($id): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'view_users')) {
            abort(403, 'Kullanıcı görüntüleme yetkiniz yok.');
        }

        $targetUser = User::where('company_id', $user->company_id)
            ->with(['profile', 'companyRoles'])
            ->findOrFail($id);

        return view('usermanagement::show', compact('targetUser'));
    }

    /**
     * Kullanıcı düzenleme sayfası.
     */
    public function edit($id): View
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'edit_users')) {
            abort(403, 'Kullanıcı düzenleme yetkiniz yok.');
        }

        $targetUser = User::where('company_id', $user->company_id)
            ->with(['profile', 'companyRoles'])
            ->findOrFail($id);

        $cities = City::orderBy('il_adi')->get();
        $companyRoles = CompanyRole::where('company_id', $user->company_id)
            ->where('is_active', true)
            ->get();

        return view('usermanagement::edit', compact('targetUser', 'cities', 'companyRoles'));
    }

    /**
     * Kullanıcı güncelleme.
     */
    public function update(Request $request, $id): RedirectResponse
    {
        $user = Auth::user();

        if (!$user->hasCompanyPermission($user->company_id, 'edit_users')) {
            abort(403, 'Kullanıcı düzenleme yetkiniz yok.');
        }

        $targetUser = User::where('company_id', $user->company_id)->findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $targetUser->id,
            'password' => 'nullable|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'title' => 'nullable|string|max:100',
            'role_id' => 'required|exists:company_roles,id',
        ]);

        // Kullanıcı bilgilerini güncelle
        $userData = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        if ($request->filled('password')) {
            $userData['password'] = Hash::make($request->password);
        }

        $targetUser->update($userData);

        // Profil güncelle
        $targetUser->profile()->updateOrCreate(
            ['user_id' => $targetUser->id],
            [
                'phone' => $request->phone,
                'title' => $request->title,
                'city_id' => $request->city_id,
                'district_id' => $request->district_id,
            ]
        );

        // Rol güncelle
        $targetUser->syncCompanyRoles($user->company_id, [$request->role_id]);

        return redirect()->route('users.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Kullanıcı başarıyla güncellendi.'
            ]);
    }

    /**
     * Kullanıcı silme.
     */
    public function destroy($id): RedirectResponse
    {
        $user = Auth::user();

        // Sadece şirket sahibi kullanıcı silebilir
        if (!$user->hasCompanyRole($user->company_id, 'Şirket Sahibi')) {
            abort(403, 'Sadece şirket sahibi kullanıcı silebilir.');
        }

        $targetUser = User::where('company_id', $user->company_id)->findOrFail($id);

        // Kendi kendini silmeyi engelle
        if ($targetUser->id === $user->id) {
            return redirect()->route('users.index')
                ->with([
                    'status' => 'error',
                    'responseTitle' => 'Hata!',
                    'responseMessage' => 'Kendi hesabınızı silemezsiniz.'
                ]);
        }

        // Başka şirket sahibini silmeyi engelle
        if ($targetUser->hasCompanyRole($user->company_id, 'Şirket Sahibi')) {
            return redirect()->route('users.index')
                ->with([
                    'status' => 'error',
                    'responseTitle' => 'Hata!',
                    'responseMessage' => 'Başka bir şirket sahibini silemezsiniz.'
                ]);
        }

        // Profil ve ilişkili verileri sil
        $targetUser->profile()->delete();

        // Şirket rol ilişkilerini sil
        \Illuminate\Support\Facades\DB::table('company_user_roles')
            ->where('user_id', $targetUser->id)
            ->where('company_id', $user->company_id)
            ->delete();

        $targetUser->delete();

        return redirect()->route('users.index')
            ->with([
                'status' => 'success',
                'responseTitle' => 'Başarılı!',
                'responseMessage' => 'Kullanıcı başarıyla silindi.'
            ]);
    }

    /**
     * İlçeleri AJAX ile getir.
     */
    public function getDistricts(Request $request)
    {
        $districts = District::where('il_id', $request->city_id)->get();
        $html = '<option value="">İlçe Seçiniz</option>';
        foreach ($districts as $district) {
            $html .= '<option value="' . $district->ilce_id . '">' . $district->ilce_adi . '</option>';
        }
        return response($html);
    }
}
