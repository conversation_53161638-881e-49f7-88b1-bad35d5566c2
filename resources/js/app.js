import './bootstrap';

import Alpine from 'alpinejs';

Alpine.data('phoneMask', () => ({
    userPhone: '',
    formatPhone() {
        let numbers = this.userPhone.replace(/\D/g, '');

        if (numbers.startsWith('0')) {
            numbers = numbers.substring(1);
        }

        numbers = numbers.substring(0, 10);

        let masked = '';
        if (numbers.length > 0) {
            masked = '0(' + numbers.substring(0, 3);
        }
        if (numbers.length > 3) {
            masked += ')' + numbers.substring(3, 6);
        }
        if (numbers.length > 6) {
            masked += '-' + numbers.substring(6, 8);
        }
        if (numbers.length > 8) {
            masked += '-' + numbers.substring(8, 10);
        }
        this.userPhone = masked;
    },
}));

window.Alpine = Alpine;

Alpine.start();
