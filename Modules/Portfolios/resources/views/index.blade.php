<x-backend-layout>
    @section('content')
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
            <!-- Enhanced Header Section -->
            <div class="mb-8">
                <!-- Header Card: Title + Search + Filters -->
                <div class="bg-white/95 rounded-2xl shadow-xl border border-white/20 p-6 mb-8">
                    <!-- Title Section -->
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-6">
                        <div>
                            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                                Portföy Yönetimi
                            </h1>
                            <p class="text-gray-600 mt-1 flex items-center gap-2">
                                <svg class="w-4 h-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6" />
                                </svg>
                                Emlak portföyünüzü profesyonelce yönetin ve analiz edin
                            </p>
                        </div>

                        <!-- Add New Button -->
                        <a href="{{ route('portfolios.create') }}"
                           class="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                            </svg>
                            Yeni İlan
                        </a>
                    </div>
                    <!-- Search and Filter Section -->
                    <div class="space-y-4">
                        <!-- Search Row -->
                        <div class="flex flex-col lg:flex-row gap-4 items-center">
                            <!-- Search Input -->
                            <div class="flex-1 relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                                    </svg>
                                </div>
                                <input type="text"
                                       id="search-input"
                                       class="block w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-sm"
                                       placeholder="Portföy ara... (başlık, konum, danışman)">
                            </div>

                            <!-- Results Info & Export -->
                            <div class="flex items-center gap-4">
                                <div id="results-info" class="text-sm text-gray-600 whitespace-nowrap bg-gray-100 px-3 py-2 rounded-lg">
                                    <span id="results-count">Yükleniyor...</span>
                                </div>

                                <button class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-medium">
                                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Dışa Aktar
                                </button>
                            </div>
                        </div>

                        <!-- Quick Filter Pills + Advanced Filters -->
                        <div class="flex flex-wrap items-center justify-between gap-4">
                            <!-- Quick Filter Pills -->
                            <div class="flex flex-wrap gap-2">
                                <button class="filter-pill active" data-filter-type="status" data-filter-value="">
                                    <span class="pill-text">Tümü</span>
                                    <span class="pill-count" id="count-all">0</span>
                                </button>
                                <button class="filter-pill" data-filter-type="status" data-filter-value="active">
                                    <span class="pill-text">Aktif İlanlar</span>
                                    <span class="pill-count" id="count-active">0</span>
                                </button>
                                <button class="filter-pill" data-filter-type="status" data-filter-value="sold">
                                    <span class="pill-text">Satıldı</span>
                                    <span class="pill-count" id="count-sold">0</span>
                                </button>
                                <button class="filter-pill" data-filter-type="status" data-filter-value="rented">
                                    <span class="pill-text">Kiralandı</span>
                                    <span class="pill-count" id="count-rented">0</span>
                                </button>
                                <button class="filter-pill" data-filter-type="status" data-filter-value="inactive">
                                    <span class="pill-text">Pasif</span>
                                    <span class="pill-count" id="count-inactive">0</span>
                                </button>
                            </div>

                            <!-- Advanced Filters Button -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open"
                                        class="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 cursor-pointer text-sm">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5" />
                                    </svg>
                                    Gelişmiş Filtreler
                                    <svg class="h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                                    </svg>
                                </button>

                                <!-- Advanced Filters Dropdown -->
                                <div x-show="open"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0 scale-95"
                                     x-transition:enter-end="opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-150"
                                     x-transition:leave-start="opacity-100 scale-100"
                                     x-transition:leave-end="opacity-0 scale-95"
                                     @click.away="open = false"
                                     class="absolute right-0 top-full mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                    <div class="p-4">
                                        <div class="grid grid-cols-2 gap-4">
                                            <!-- Danışman Filter -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">Danışman</label>
                                                <select id="agent-filter" class="filter-select w-full">
                                                    <option value="">Tüm Danışmanlar</option>
                                                    @if(isset($agents))
                                                        @foreach($agents as $agent)
                                                        <option value="{{ $agent->id }}">{{ $agent->name }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>

                                            <!-- Şehir Filter -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">Şehir</label>
                                                <select id="city-filter" class="filter-select w-full">
                                                    <option value="">Tüm Şehirler</option>
                                                    @if(isset($cities))
                                                        @foreach($cities as $city)
                                                        <option value="{{ $city->il_id }}">{{ $city->il_adi }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>

                                            <!-- İlçe Filter -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">İlçe</label>
                                                <select id="district-filter" class="filter-select w-full">
                                                    <option value="">Tüm İlçeler</option>
                                                </select>
                                            </div>

                                            <!-- Kategori Filter -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">Kategori</label>
                                                <select id="category-filter" class="filter-select w-full">
                                                    <option value="">Tüm Kategoriler</option>
                                                    @if(isset($portfolioCategories))
                                                        @foreach($portfolioCategories as $category)
                                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>

                                            <!-- Min Fiyat -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">Min Fiyat</label>
                                                <input type="number" id="min-price" placeholder="₺ 0" class="filter-input w-full">
                                            </div>

                                            <!-- Max Fiyat -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">Max Fiyat</label>
                                                <input type="number" id="max-price" placeholder="₺ 999.999.999" class="filter-input w-full">
                                            </div>

                                            <!-- Tarih Başlangıç -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">Başlangıç Tarihi</label>
                                                <input type="date" id="date-from" class="filter-input w-full">
                                            </div>

                                            <!-- Tarih Bitiş -->
                                            <div class="space-y-1">
                                                <label class="text-xs font-medium text-gray-700">Bitiş Tarihi</label>
                                                <input type="date" id="date-to" class="filter-input w-full">
                                            </div>
                                        </div>

                                        <!-- Filter Actions -->
                                        <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                                            <button id="clear-all-filters" class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors">
                                                <svg class="w-4 h-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                Temizle
                                            </button>

                                            <button @click="open = false" class="px-4 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                                                Uygula
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Card-Based Results Section -->
            <div class="bg-white/95 rounded-2xl shadow-xl border border-white/20 p-6 mb-8">
                <div class="space-y-4" id="portfolios-container">
                    <!-- Portfolios will be loaded here -->
                </div>
            </div>

            <!-- Pagination -->
            <div class="flex justify-center mt-8" id="pagination-container">
                <!-- Pagination will be loaded here -->
            </div>
        </div>
    </div>
    @endsection

    @push('styles')
    <!-- Enhanced Modern Styles -->
    <style>
        /* Modern Card View Styles */
        .portfolio-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .portfolio-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #d1d5db;
        }

        /* Card Grid Layout - Yatay Kartlar */
        #portfolios-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .portfolio-card {
            display: flex;
            min-height: 160px;
        }

        .portfolio-card .card-image {
            width: 200px;
            flex-shrink: 0;
            position: relative;
        }

        .portfolio-card .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .portfolio-card .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1rem;
            justify-content: space-between;
        }

        /* Status Badge */
        .status-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            z-index: 10;
        }

        /* Price Badge */
        .price-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            z-index: 10;
        }

        @media (max-width: 768px) {
            .portfolio-card {
                flex-direction: column;
                min-height: auto;
            }

            .portfolio-card .card-image {
                width: 100%;
                height: 200px;
            }
        }

        /* Line clamp utility */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Filter Pills */
        .filter-pill {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .filter-pill:hover {
            background: #e2e8f0;
            color: #475569;
        }

        .filter-pill.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .pill-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.125rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .filter-pill.active .pill-count {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Filter Inputs */
        .filter-select, .filter-input {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            background: white;
            transition: all 0.2s ease;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Action Button Styles */
        .action-btn {
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Minimal card spacing */
        .portfolio-card .card-content {
            padding: 1rem;
        }

        @media (max-width: 768px) {
            .portfolio-card .card-content {
                padding: 0.75rem;
            }
        }

        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .status-sold {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }

        .status-rented {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }

        .status-inactive {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        /* Quick Access Styles */
        .quick-access-btn {
            transition: all 0.2s ease;
        }

        .quick-access-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Dropdown Menu Animation */
        .dropdown-menu {
            animation: dropdownSlideIn 0.2s ease-out;
        }

        @keyframes dropdownSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Quick Menu Hover Effects */
        .quick-menu-item {
            transition: all 0.15s ease;
        }

        .quick-menu-item:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transform: translateX(4px);
        }

        /* Responsive Quick Access */
        @media (max-width: 768px) {
            .quick-access-btn {
                padding: 4px 6px;
                font-size: 10px;
            }

            .quick-access-btn svg {
                width: 10px;
                height: 10px;
            }
        }

        /* Row Styling */
        #portfolios-table tbody tr {
            background: rgba(255, 255, 255, 0.95) !important;
            border: none !important;
            transition: all 0.2s ease !important;
        }

        #portfolios-table tbody tr:nth-child(even) {
            background: rgba(248, 250, 252, 0.95) !important;
        }

        #portfolios-table tbody tr:hover {
            background: rgba(239, 246, 255, 0.95) !important;
            box-shadow:
                4px 0 0 0 #3b82f6,
                0 4px 12px -2px rgba(59, 130, 246, 0.15) !important;
        }

        /* Cell Styling */
        #portfolios-table tbody td {
            padding: 16px !important;
            border: none !important;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
            vertical-align: middle !important;
        }

        .dt-layout-row {
            padding: 0 18px;
        }

        /* Modern Buttons */
        .dt-buttons {
            margin-bottom: 0 !important;
        }


        .dt-button {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 10px !important;
            padding: 10px 12px !important;
            font-weight: 600 !important;
            font-size: 14px !important;
            margin-right: 8px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
        }

        .dt-button:hover {
            transform: translateY(0) !important;
            --tw-translate-y: 0;

        }

        .dt-button.buttons-collection {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3) !important;
        }
        /* Dropdown Collection */
        .dt-button-collection {
            background: #ffffff !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 16px !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
            padding: 8px !important;
        }

        .dt-button-collection .dt-button {
            background: transparent !important;
            color: #374151 !important;
            border-radius: 8px !important;
            margin: 2px !important;
            box-shadow: none !important;
        }

        .dt-button-collection .dt-button:hover {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
            transform: none !important;
        }
        /* Pagination */
        .dataTables_paginate {
            padding: 16px 24px 20px 24px !important;
            background: #f8fafc !important;
            border-top: 1px solid #e2e8f0 !important;
            border-radius: 0 0 20px 20px !important;
        }

        .paginate_button {
            background: #ffffff !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 10px !important;
            margin: 0 4px !important;
            padding: 10px 16px !important;
            transition: all 0.3s ease !important;
        }

        .paginate_button:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            color: white !important;
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3) !important;
        }

        .paginate_button.current {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
            color: white !important;
            border-color: transparent !important;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;
        }

        /* Search and Length */
        .dataTables_filter input,
        .dataTables_length select {
            background: #ffffff !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 12px !important;
            padding: 10px 16px !important;
            transition: all 0.3s ease !important;
        }

        .dataTables_filter input:focus,
        .dataTables_length select:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
            outline: none !important;
        }

        /* Filter Dropdown Animations */
        #quick-filter-dropdown {
            transform-origin: top right;
        }

        #quick-filter-dropdown.opacity-0 {
            opacity: 0;
        }

        #quick-filter-dropdown.opacity-100 {
            opacity: 1;
        }

        #quick-filter-dropdown.scale-95 {
            transform: scale(0.95);
        }

        #quick-filter-dropdown.scale-100 {
            transform: scale(1);
        }

        #filter-arrow.rotate-180 {
            transform: rotate(180deg);
        }
    </style>
    @endpush

    @push('scripts')
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
    // Global variables
    let currentPage = 1;
    let currentFilters = {};

    $(document).ready(function() {
        // Load portfolios on page load
        loadPortfolios();

        // Search functionality
        $('#search-input').on('input', debounce(function() {
            currentPage = 1;
            loadPortfolios();
        }, 500));

        // Filter functionality
        $('.filter-option').on('click', function(e) {
            e.preventDefault();
            const filterType = $(this).data('filter-type');
            const filterValue = $(this).data('filter-value');

            // Update active filter
            setActiveFilter(filterType, filterValue);
            currentPage = 1;
            loadPortfolios();
        });

        // Clear filters
        $('#clear-filters').on('click', function() {
            currentFilters = {};
            $('.filter-option').removeClass('active');
            $('#search-input').val('');
            currentPage = 1;
            loadPortfolios();
        });
    });

    // Load portfolios function
    function loadPortfolios() {
        const searchValue = $('#search-input').val();

        // Show loading
        $('#portfolios-container').html(`
            <div class="flex justify-center items-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <span class="ml-3 text-gray-600">Yükleniyor...</span>
            </div>
        `);

        // Prepare data
        const data = {
            page: currentPage,
            search: searchValue,
            ...currentFilters
        };

        // AJAX request
        $.ajax({
            url: '{{ route("portfolios.cards") }}',
            method: 'GET',
            data: data,
            success: function(response) {
                renderCards(response.cards);
                renderPagination(response.pagination);
                updateResultsInfo(response.pagination);
            },
            error: function(xhr, status, error) {
                console.error('Error loading portfolios:', error);
                $('#portfolios-container').html(`
                    <div class="text-center py-12">
                        <div class="text-red-500 mb-2">
                            <svg class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <p class="text-gray-600">Veriler yüklenirken bir hata oluştu.</p>
                        <button onclick="loadPortfolios()" class="mt-3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                            Tekrar Dene
                        </button>
                    </div>
                `);
            }
        });
    }

    // Update results info
    function updateResultsInfo(pagination) {
        const resultsText = `${pagination.from || 0}-${pagination.to || 0} / ${pagination.total || 0} portföy`;
        $('#results-count').text(resultsText);
    }

    // Render cards function
    function renderCards(cards) {
        if (!cards || cards.length === 0) {
            $('#portfolios-container').html(`
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Portföy bulunamadı</h3>
                    <p class="text-gray-500">Arama kriterlerinize uygun portföy bulunmamaktadır.</p>
                </div>
            `);
            return;
        }

        let cardsHtml = '';
        cards.forEach(card => {
            cardsHtml += renderSingleCard(card);
        });

        $('#portfolios-container').html(cardsHtml);
    }

    // Render single card function
    function renderSingleCard(card) {
        return `
            <div class="portfolio-card">
                <!-- Card Image with Overlays -->
                <div class="card-image">
                    <img src="${card.image_url}" alt="${card.title}" onerror="this.src='/images/noimage.jpg'">

                    <!-- Status Badge -->
                    <div class="status-badge">
                        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${card.status_color}">
                            ${card.status_badge}
                        </span>
                    </div>

                    <!-- Price Badge -->
                    <div class="price-badge">
                        ${card.price}
                    </div>

                    <!-- Images Count -->
                    ${card.images_count > 1 ? `
                        <div class="absolute bottom-2 left-2">
                            <span class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                ${card.images_count}
                            </span>
                        </div>
                    ` : ''}
                </div>

                <!-- Card Content -->
                <div class="card-content">
                    <!-- Header Section -->
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex-1">
                            <h3 class="text-base font-semibold text-gray-900 mb-1 line-clamp-1">${card.title}</h3>
                            <p class="text-sm text-blue-600 font-medium">${card.property_category}</p>
                        </div>
                        <div class="text-right">
                            <span class="text-xs text-gray-500">#${card.portfolio_no}</span>
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="flex items-center text-gray-600 mb-3">
                        <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span class="text-sm text-gray-700">${card.location}</span>
                    </div>

                    <!-- Property Details - Inline -->
                    <div class="flex items-center gap-4 mb-2 text-sm text-gray-600">
                        ${card.area_gross ? `
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                </svg>
                                <span>Brüt: ${card.area_gross}</span>
                            </div>
                        ` : ''}
                        ${card.area_net ? `
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                </svg>
                                <span>Net: ${card.area_net}</span>
                            </div>
                        ` : ''}

                        ${card.rooms ? `
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                                </svg>
                                <span>${card.rooms}</span>
                            </div>
                        ` : ''}

                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <span>${card.view_count}</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                            </svg>
                            <span>${card.created_at}</span>
                        </div>
                    </div>

                    <!-- Agent Info -->
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs font-semibold">${card.agent_initial}</span>
                            </div>
                            <div class="ml-2">
                                <p class="text-xs font-medium text-gray-900">${card.agent_name}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-xl md:text-2xl font-bold text-green-600">${card.price}</div>
                            ${card.price_history ? `
                                <div class="text-base text-gray-500 flex items-center">
                                    ${card.price_change > 0 ? `
                                        <svg class="w-3 h-3 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l9.2-9.2M17 17V7H7" />
                                        </svg>
                                        <span class="text-green-600">+${card.price_change}%</span>
                                    ` : card.price_change < 0 ? `
                                        <svg class="w-3 h-3 mr-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 7l-9.2 9.2M7 7v10h10" />
                                        </svg>
                                        <span class="text-red-600">${card.price_change}%</span>
                                    ` : `
                                        <span class="text-gray-500">Değişim yok</span>
                                    `}
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- Action Buttons Section -->
                    <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                        <!-- Left: Quick Actions -->
                        <div class="flex items-center space-x-1">
                            <a href="${card.view_url}" class="p-1.5 text-blue-600 hover:bg-blue-50 rounded transition-colors" title="Görüntüle">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </a>

                            <a href="${card.edit_url}" class="p-1.5 text-green-600 hover:bg-green-50 rounded transition-colors" title="Düzenle">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </a>

                            <button onclick="duplicatePortfolio(${card.id})" class="p-1.5 text-purple-600 hover:bg-purple-50 rounded transition-colors" title="Kopyala">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                            </button>

                            <button onclick="deletePortfolio(${card.id})" class="p-1.5 text-red-600 hover:bg-red-50 rounded transition-colors" title="Sil">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>

                        <!-- Right: Feature Buttons + Quick Actions Dropdown -->
                        <div class="flex items-center space-x-1">
                            <a href="/portfolios/${card.id}/notes" class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs font-medium hover:bg-yellow-200 transition-colors">Notlar</a>
                            <a href="/portfolios/${card.id}/customers" class="px-2 py-1 bg-indigo-100 text-indigo-700 rounded text-xs font-medium hover:bg-indigo-200 transition-colors">Müşteri</a>
                            <a href="/portfolios/${card.id}/offers" class="px-2 py-1 bg-orange-100 text-orange-700 rounded text-xs font-medium hover:bg-orange-200 transition-colors">Teklif</a>

                            <!-- Hızlı İşlemler Dropdown -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open"
                                        class="flex items-center px-2 py-1 bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 rounded text-xs font-medium hover:from-slate-200 hover:to-slate-300 transition-all duration-200 shadow-sm hover:shadow-md"
                                        title="Hızlı İşlemler">
                                    <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                    Hızlı İşlemler
                                    <svg class="w-3 h-3 ml-1 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- Dropdown Menu -->
                                <div x-show="open"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0 scale-95"
                                     x-transition:enter-end="opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-150"
                                     x-transition:leave-start="opacity-100 scale-100"
                                     x-transition:leave-end="opacity-0 scale-95"
                                     @click.away="open = false"
                                     class="absolute right-0 top-full mt-1 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-50 dropdown-menu">
                                    <div class="py-1">
                                        <!-- Müşteriye İlişkilendir -->
                                        <button onclick="linkToCustomer(${card.id})" class="quick-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                            <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            Müşteriye İlişkilendir
                                        </button>

                                        <!-- Uygun Talepler -->
                                        <button onclick="findMatchingRequests(${card.id})" class="quick-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                            <svg class="w-4 h-4 mr-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Uygun Talepler
                                        </button>

                                        <!-- Satışı Kapat -->
                                        <button onclick="closeSale(${card.id})" class="quick-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                            <svg class="w-4 h-4 mr-3 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                            </svg>
                                            Satışı Kapat
                                        </button>

                                        <div class="border-t border-gray-100 my-1"></div>

                                        <!-- Sözleşmeler -->
                                        <button onclick="manageContracts(${card.id})" class="quick-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                            <svg class="w-4 h-4 mr-3 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            Sözleşmeler
                                        </button>

                                        <!-- Belgeler -->
                                        <button onclick="manageDocuments(${card.id})" class="quick-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                            <svg class="w-4 h-4 mr-3 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                            Belgeler
                                        </button>

                                        <!-- Randevular -->
                                        <button onclick="manageAppointments(${card.id})" class="quick-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                            <svg class="w-4 h-4 mr-3 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            Randevular
                                        </button>

                                        <!-- Fiyat Geçmişi -->
                                        <button onclick="managePriceHistory(${card.id})" class="quick-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                            <svg class="w-4 h-4 mr-3 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                            Fiyat Geçmişi
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Render pagination function
    function renderPagination(pagination) {
        if (pagination.last_page <= 1) {
            $('#pagination-container').html('');
            return;
        }

        let paginationHtml = `
            <nav class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    ${pagination.current_page > 1 ? `
                        <button onclick="changePage(${pagination.current_page - 1})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Önceki
                        </button>
                    ` : ''}
                    ${pagination.current_page < pagination.last_page ? `
                        <button onclick="changePage(${pagination.current_page + 1})" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Sonraki
                        </button>
                    ` : ''}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            <span class="font-medium">${pagination.from || 0}</span>
                            -
                            <span class="font-medium">${pagination.to || 0}</span>
                            arası, toplam
                            <span class="font-medium">${pagination.total}</span>
                            sonuç
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
        `;

        // Previous button
        if (pagination.current_page > 1) {
            paginationHtml += `
                <button onclick="changePage(${pagination.current_page - 1})" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

        if (startPage > 1) {
            paginationHtml += `<button onclick="changePage(1)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</button>`;
            if (startPage > 2) {
                paginationHtml += `<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === pagination.current_page;
            paginationHtml += `
                <button onclick="changePage(${i})" class="relative inline-flex items-center px-4 py-2 border ${isActive ? 'border-blue-500 bg-blue-50 text-blue-600' : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'} text-sm font-medium">
                    ${i}
                </button>
            `;
        }

        if (endPage < pagination.last_page) {
            if (endPage < pagination.last_page - 1) {
                paginationHtml += `<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`;
            }
            paginationHtml += `<button onclick="changePage(${pagination.last_page})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">${pagination.last_page}</button>`;
        }

        // Next button
        if (pagination.current_page < pagination.last_page) {
            paginationHtml += `
                <button onclick="changePage(${pagination.current_page + 1})" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </button>
            `;
        }

        paginationHtml += `
                        </nav>
                    </div>
                </div>
            </nav>
        `;

        $('#pagination-container').html(paginationHtml);
    }

    // Change page function
    function changePage(page) {
        currentPage = page;
        loadPortfolios();
    }

    // Set active filter function
    function setActiveFilter(type, value) {
        if (value === '' || value === null) {
            delete currentFilters[type + '_filter'];
        } else {
            currentFilters[type + '_filter'] = value;
        }

        // Update UI
        $(`.filter-option[data-filter-type="${type}"]`).removeClass('active');
        if (value) {
            $(`.filter-option[data-filter-type="${type}"][data-filter-value="${value}"]`).addClass('active');
        }
    }

    // Debounce function
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Delete portfolio function
    function deletePortfolio(id) {
        if (confirm('Bu portföyü silmek istediğinizden emin misiniz?')) {
            $.ajax({
                url: `/portfolios/${id}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    loadPortfolios();
                    // Show success message
                    showNotification('Portföy başarıyla silindi.', 'success');
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting portfolio:', error);
                    showNotification('Portföy silinirken bir hata oluştu.', 'error');
                }
            });
        }
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

        const notification = $(`
            <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                ${message}
            </div>
        `);

        $('body').append(notification);

        setTimeout(() => {
            notification.removeClass('translate-x-full');
        }, 100);

        setTimeout(() => {
            notification.addClass('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }



    // Duplicate portfolio function
    function duplicatePortfolio(id) {
        if (confirm('Bu portföyü kopyalamak istediğinizden emin misiniz?')) {
            $.ajax({
                url: `/portfolios/${id}/duplicate`,
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    loadPortfolios();
                    showNotification('Portföy başarıyla kopyalandı.', 'success');
                },
                error: function(xhr, status, error) {
                    console.error('Error duplicating portfolio:', error);
                    showNotification('Portföy kopyalanırken bir hata oluştu.', 'error');
                }
            });
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick^="toggleQuickActions"]') && !event.target.closest('[id^="quick-actions-"]')) {
            const allDropdowns = document.querySelectorAll('[id^="quick-actions-"]');
            allDropdowns.forEach(dropdown => dropdown.classList.add('hidden'));
        }
    });

    // Filter dropdown event handlers
    $(document).ready(function() {
        // Hızlı filtre dropdown toggle
        $('#quick-filter-btn').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleFilterDropdown('#quick-filter-dropdown', '#filter-arrow');
        });

        // Filter dropdown dışına tıklandığında kapat
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#quick-filter-btn, #quick-filter-dropdown').length) {
                closeFilterDropdown('#quick-filter-dropdown', '#filter-arrow');
            }
        });

        // Dropdown içindeki linklere tıklandığında dropdown'ı kapat
        $('#quick-filter-dropdown a').on('click', function() {
            closeFilterDropdown('#quick-filter-dropdown', '#filter-arrow');
        });

        // ESC tuşu ile dropdown'ları kapat
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFilterDropdown('#quick-filter-dropdown', '#filter-arrow');
            }
        });
    });

    // Filter dropdown yönetimi
    function toggleFilterDropdown(dropdownId, arrowId) {
        const $dropdown = $(dropdownId);
        const $arrow = $(arrowId);
        const isHidden = $dropdown.hasClass('hidden');

        if (isHidden) {
            $dropdown.removeClass('hidden');
            setTimeout(() => {
                $dropdown.removeClass('opacity-0 scale-95').addClass('opacity-100 scale-100');
            }, 10);
            $arrow.addClass('rotate-180');
        } else {
            closeFilterDropdown(dropdownId, arrowId);
        }
    }

    function closeFilterDropdown(dropdownId, arrowId) {
        const $dropdown = $(dropdownId);
        const $arrow = $(arrowId);

        if (!$dropdown.hasClass('hidden')) {
            $dropdown.removeClass('opacity-100 scale-100').addClass('opacity-0 scale-95');
            setTimeout(() => {
                $dropdown.addClass('hidden');
            }, 200);
            $arrow.removeClass('rotate-180');
        }
    }

    // Hızlı İşlemler Functions - Şimdilik boş, gelecekte doldurulacak

    // Müşteriye İlişkilendir
    function linkToCustomer(portfolioId) {
        showNotification('Müşteriye İlişkilendir özelliği yakında eklenecek.', 'info');
        console.log('Link to customer for portfolio:', portfolioId);
        // TODO: Implement customer linking functionality
    }

    // Uygun Talepler
    function findMatchingRequests(portfolioId) {
        showNotification('Uygun Talepler özelliği yakında eklenecek.', 'info');
        console.log('Find matching requests for portfolio:', portfolioId);
        // TODO: Implement matching requests functionality
    }

    // Satışı Kapat
    function closeSale(portfolioId) {
        showNotification('Satışı Kapat özelliği yakında eklenecek.', 'info');
        console.log('Close sale for portfolio:', portfolioId);
        // TODO: Implement close sale functionality
    }

    // Sözleşmeler
    function manageContracts(portfolioId) {
        showNotification('Sözleşmeler özelliği yakında eklenecek.', 'info');
        console.log('Manage contracts for portfolio:', portfolioId);
        // TODO: Implement contracts management functionality
    }

    // Belgeler
    function manageDocuments(portfolioId) {
        showNotification('Belgeler özelliği yakında eklenecek.', 'info');
        console.log('Manage documents for portfolio:', portfolioId);
        // TODO: Implement documents management functionality
    }

    // Randevular
    function manageAppointments(portfolioId) {
        showNotification('Randevular özelliği yakında eklenecek.', 'info');
        console.log('Manage appointments for portfolio:', portfolioId);
        // TODO: Implement appointments management functionality
    }

    // Fiyat Geçmişi
    function managePriceHistory(portfolioId) {
        showNotification('Fiyat Geçmişi özelliği yakında eklenecek.', 'info');
        console.log('Manage price history for portfolio:', portfolioId);
        // TODO: Implement price history management functionality
    }

    </script>
    @endpush
</x-backend-layout>
