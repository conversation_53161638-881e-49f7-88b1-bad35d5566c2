@props([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'iconBg' => 'bg-blue-100',
    'iconColor' => 'text-blue-600',
    'actions' => null,
    'padding' => 'p-6',
    'noPadding' => false,
    'shadow' => 'shadow-sm',
    'border' => true
])

<div class="bg-white rounded-xl {{ $shadow }} {{ $border ? 'border border-gray-200' : '' }} overflow-hidden">
    @if($title || $subtitle || $icon || $actions)
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center min-w-0 flex-1">
                @if($icon)
                <div class="flex-shrink-0 mr-4">
                    <div class="flex items-center justify-center w-10 h-10 {{ $iconBg }} {{ $iconColor }} rounded-lg">
                        {!! $icon !!}
                    </div>
                </div>
                @endif

                <div class="min-w-0 flex-1">
                    @if($title)
                    <h3 class="text-lg font-semibold text-gray-900">
                        {{ $title }}
                    </h3>
                    @endif

                    @if($subtitle)
                    <p class="text-sm text-gray-500 mt-1">
                        {{ $subtitle }}
                    </p>
                    @endif
                </div>
            </div>

            @if($actions)
            <div class="flex-shrink-0 ml-4">
                {{ $actions }}
            </div>
            @endif
        </div>
    </div>
    @endif

    <div class="{{ $noPadding ? '' : $padding }}">
        {{ $slot }}
    </div>
</div>
