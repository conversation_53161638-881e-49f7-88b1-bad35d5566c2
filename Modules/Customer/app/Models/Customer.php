<?php

namespace Modules\Customer\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Models\Company;
use App\Models\User;
// use Modules\Customer\Database\Factories\CustomerFactory;

class Customer extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'company_id',
        'customer_group_id',
        'first_name',
        'last_name',
        'gender',
        'birth_date',
        'company_name',
        'email',
        'phone',
        'whatsapp',
        'address',
        'city_id',
        'district_id',
        'status',
        'wants_sms',
        'wants_email',
        'source',
        'notes',
        'minimum_income',
        'investment_budget',
        'currency',
        'nationality',
        'occupation',
        'customer_types',
        'agent_id',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'wants_sms' => 'boolean',
        'wants_email' => 'boolean',
        'minimum_income' => 'decimal:2',
        'investment_budget' => 'decimal:2',
        'customer_types' => 'array',
        'agent_id' => 'integer',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function customerGroup(): BelongsTo
    {
        return $this->belongsTo(CustomerGroup::class, 'customer_group_id');
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    public function customerTypes(): BelongsToMany
    {
        return $this->belongsToMany(CustomerType::class, 'customer_customer_type', 'customer_id', 'customer_type_id');
    }
}
