<?php

namespace Modules\Customer\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\Customer\Database\Factories\CustomerOccupationsFactory;

class CustomerOccupations extends Model
{
    use HasFactory;

    protected $table = 'customers_occupations';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];

    // protected static function newFactory(): CustomerOccupationsFactory
    // {
    //     // return CustomerOccupationsFactory::new();
    // }
}
