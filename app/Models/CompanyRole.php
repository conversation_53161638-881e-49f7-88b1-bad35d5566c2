<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class CompanyRole extends Model
{
    use HasUuids;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'company_id',
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Bu role sahip şirketi getir.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Bu rolün izinlerini getir.
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'company_role_permissions');
    }

    /**
     * Bu role sahip kullanıcıları getir.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'company_user_roles')
            ->withPivot('company_id')
            ->withTimestamps();
    }

    /**
     * Bu role izin ata.
     */
    public function givePermission(Permission|string $permission): self
    {
        if (is_string($permission)) {
            $permission = Permission::findByName($permission);
        }

        if ($permission && !$this->hasPermission($permission)) {
            $this->permissions()->attach($permission->id);
        }

        return $this;
    }

    /**
     * Bu rolden izin kaldır.
     */
    public function revokePermission(Permission|string $permission): self
    {
        if (is_string($permission)) {
            $permission = Permission::findByName($permission);
        }

        if ($permission) {
            $this->permissions()->detach($permission->id);
        }

        return $this;
    }

    /**
     * Rolün izni olup olmadığını kontrol et.
     */
    public function hasPermission(Permission|string $permission): bool
    {
        if (is_string($permission)) {
            return $this->permissions()->where('name', $permission)->exists();
        }

        return $this->permissions()->where('permission_id', $permission->id)->exists();
    }

    /**
     * Bu rolün izinlerini senkronize et.
     */
    public function syncPermissions(array $permissions): self
    {
        $permissionIds = collect($permissions)->map(function ($permission) {
            if (is_string($permission)) {
                $perm = Permission::findByName($permission);
                return $perm ? $perm->id : null;
            }
            return $permission instanceof Permission ? $permission->id : $permission;
        })->filter()->toArray();

        $this->permissions()->sync($permissionIds);

        return $this;
    }

    /**
     * Şirket için rol oluştur (yoksa).
     */
    public static function createForCompany(string $companyId, string $name, ?string $description = null): self
    {
        return static::firstOrCreate(
            ['company_id' => $companyId, 'name' => $name],
            ['description' => $description, 'is_active' => true]
        );
    }
}
