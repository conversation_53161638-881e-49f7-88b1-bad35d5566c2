<x-backend-layout>
    @section('content')
        <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-20">
                <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
                <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
                <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
                <!-- Header Section -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        <div>
                            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                                Kullanıcı Düzenle
                            </h1>
                            <p class="text-gray-600 text-lg mt-2">{{ $targetUser->name }} kullanıcısının bilgilerini düzenleyin</p>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center gap-4">
                            <a href="{{ route('users.show', $targetUser->id) }}"
                               class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Görüntüle
                            </a>
                            <a href="{{ route('users.index') }}"
                               class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                                </svg>
                                Geri Dön
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Edit User Form -->
                <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl">
                    <form action="{{ route('users.update', $targetUser->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Profile Header -->
                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-6 mb-8 pb-8 border-b border-gray-200">
                            <div class="relative">
                                <div class="w-20 h-20 rounded-full ring-4 ring-blue-500/20 ring-offset-2 overflow-hidden bg-gray-100">
                                    <img src="{{ $targetUser->profile?->avatar_url ? asset($targetUser->profile?->avatar_url) : 'https://ui-avatars.com/api/?name='.urlencode($targetUser->name).'&color=7c3aed&background=e0e7ff' }}"
                                         alt="{{ $targetUser->name }}"
                                         class="w-full h-full object-cover" />
                                </div>
                                <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-3 border-white flex items-center justify-center">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                            </div>

                            <div class="flex-1">
                                <h2 class="text-xl font-bold text-gray-900">{{ $targetUser->name }}</h2>
                                <p class="text-gray-500 mb-2">{{ $targetUser->email }}</p>
                                <div class="text-sm text-gray-500">
                                    Üyelik: {{ $targetUser->created_at->format('d.m.Y') }}
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Left Column -->
                            <div class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Temel Bilgiler
                                </h3>

                                <div>
                                    <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">Ad Soyad <span class="text-red-500">*</span></label>
                                    <input type="text" name="name" id="name" value="{{ old('name', $targetUser->name) }}"
                                           class="form-input @error('name') border-red-500 @enderror"
                                           required>
                                    @error('name')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Adresi <span class="text-red-500">*</span></label>
                                    <input type="email" name="email" id="email" value="{{ old('email', $targetUser->email) }}"
                                           class="form-input @error('email') border-red-500 @enderror"
                                           required>
                                    @error('email')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">Yeni Şifre (İsteğe Bağlı)</label>
                                    <div class="relative" x-data="{ show: false }">
                                    <input :type="show ? 'text' : 'password'" name="password" id="password"
                                           class="form-input @error('password') border-red-500 @enderror">
                                           <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center z-10" x-on:click="show = !show" aria-label="Şifreyi göster/gizle">
                                            <svg x-show="!show" class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12c2.25-4.5 6.364-7.5 9.75-7.5s7.5 3 9.75 7.5c-2.25 4.5-6.364 7.5-9.75 7.5s-7.5-3-9.75-7.5z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            <svg x-show="show" class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 3l18 18" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.5 12c2.25 4.5 6.364 7.5 9.75 7.5 1.711 0 3.348-.482 4.8-1.318" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.21 6.21C7.764 5.178 9.52 4.5 11.25 4.5c3.386 0 7.5 3 9.75 7.5a10.523 10.523 0 01-4.478 4.77" />
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Boş bırakırsanız mevcut şifre korunur</p>
                                    @error('password')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="password_confirmation" class="block text-sm font-semibold text-gray-700 mb-2">Yeni Şifre Tekrar</label>
                                    <input type="password" name="password_confirmation" id="password_confirmation"
                                           class="form-input">
                                </input>
                                @error('password_confirmation')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                                </div>

                                <div>
                                    <label for="role_id" class="block text-sm font-semibold text-gray-700 mb-2">Rol <span class="text-red-500">*</span></label>
                                    <select name="role_id" id="role_id" class="form-select @error('role_id') border-red-500 @enderror" required>
                                        <option value="">Rol Seçiniz</option>
                                        @foreach($companyRoles as $role)
                                            <option value="{{ $role->id }}"
                                                    {{ old('role_id', $targetUser->companyRoles->first()?->id) == $role->id ? 'selected' : '' }}>
                                                {{ $role->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('role_id')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="space-y-6">
                                <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                                    </svg>
                                    İletişim Bilgileri
                                </h3>

                                <div x-data="phoneMask" x-init="userPhone='{{ old('phone', $targetUser->profile?->phone) }}'; formatPhone()">
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Telefon Numarası <span class="text-red-500">*</span></label>
                                    <input type="tel" name="phone" id="phone"
                                           placeholder="0(555)123-45-67"
                                           x-model="userPhone"
                                           x-on:input="formatPhone()"
                                           maxlength="15"
                                           required
                                           class="form-input @error('phone') border-red-500 @enderror">
                                    @error('phone')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="title" class="block text-sm font-semibold text-gray-700 mb-2">Pozisyon / Ünvan</label>
                                    <input type="text" name="title" id="title" value="{{ old('title', $targetUser->profile?->title) }}"
                                           placeholder="Örn: Emlak Danışmanı"
                                           class="form-input @error('title') border-red-500 @enderror">
                                    @error('title')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="city_id" class="block text-sm font-semibold text-gray-700 mb-2">Şehir</label>
                                    <select name="city_id" id="city_id" class="form-select">
                                        <option value="">Şehir Seçiniz</option>
                                        @foreach($cities as $city)
                                            <option value="{{ $city->il_id }}"
                                                    {{ old('city_id', $targetUser->profile?->city_id) == $city->il_id ? 'selected' : '' }}>
                                                {{ $city->il_adi }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div>
                                    <label for="district_id" class="block text-sm font-semibold text-gray-700 mb-2">İlçe</label>
                                    <select name="district_id" id="district_id" class="form-select">
                                        <option value="">İlçe Seçiniz</option>
                                    </select>
                                </div>

                                <!-- Current Role Info -->
                                @if($targetUser->companyRoles->count() > 0)
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div class="flex items-start gap-3">
                                            <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <div>
                                                <p class="text-sm text-blue-800 font-medium">Mevcut Roller</p>
                                                <div class="flex flex-wrap gap-2 mt-2">
                                                    @foreach($targetUser->companyRoles as $role)
                                                        <span class="inline-block px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">
                                                            {{ $role->name }}
                                                        </span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-between pt-8 border-t border-gray-200 mt-8">
                            <div class="flex gap-4">
                                <a href="{{ route('users.show', $targetUser->id) }}"
                                   class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                    İptal
                                </a>
                            </div>

                            <div class="flex gap-4">
                                @if($targetUser->id !== auth()->id())
                                    <button type="button"
                                            onclick="if(confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz?')) { document.getElementById('delete-form').submit(); }"
                                            class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                                        Kullanıcıyı Sil
                                    </button>
                                @endif

                                <button type="submit"
                                        class="flex items-center gap-2 px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Değişiklikleri Kaydet
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Delete Form (Hidden) -->
                    @if($targetUser->id !== auth()->id())
                        <form id="delete-form" action="{{ route('users.destroy', $targetUser->id) }}" method="POST" class="hidden">
                            @csrf
                            @method('DELETE')
                        </form>
                    @endif
                </div>
            </div>
        </div>

        @push('scripts')
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // City/District AJAX
            const citySelect = document.getElementById('city_id');
            const districtSelect = document.getElementById('district_id');

            if (citySelect && districtSelect) {
                // Load districts on page load if city is selected
                if (citySelect.value) {
                    loadDistricts(citySelect.value, '{{ old('district_id', $targetUser->profile?->district_id) }}');
                }

                citySelect.addEventListener('change', function() {
                    const cityId = this.value;
                    if (cityId) {
                        loadDistricts(cityId);
                    } else {
                        districtSelect.innerHTML = '<option value="">İlçe Seçiniz</option>';
                    }
                });

                function loadDistricts(cityId, selectedDistrictId = '') {
                    fetch(`{{ route('users.getDistricts') }}?city_id=${cityId}`)
                        .then(response => response.text())
                        .then(data => {
                            districtSelect.innerHTML = data;
                            if (selectedDistrictId) {
                                districtSelect.value = selectedDistrictId;
                            }
                        })
                        .catch(error => console.error('Error:', error));
                }
            }
        });
        </script>
        @endpush
    @endsection
</x-backend-layout>
