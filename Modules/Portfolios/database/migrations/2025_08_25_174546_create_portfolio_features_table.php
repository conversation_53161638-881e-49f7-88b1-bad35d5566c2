<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_features', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('portfolio_id')->constrained('portfolios')->cascadeOnDelete();
            $table->foreignId('feature_id')->constrained('portfolio_category_features')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_features');
    }
};
