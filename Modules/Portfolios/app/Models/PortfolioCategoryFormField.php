<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PortfolioCategoryFormField extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_category_form_fields';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'category_id',
        'field_name',
        'field_label',
        'field_type',
        'field_options',
        'field_group',
        'is_required',
        'is_searchable',
        'placeholder',
        'help_text',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'field_options' => 'array',
        'is_required' => 'boolean',
        'is_searchable' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the category this form field belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(PortfolioCategory::class, 'category_id', 'id');
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeForCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope a query to filter by field group.
     */
    public function scopeForGroup($query, string $group)
    {
        return $query->where('field_group', $group);
    }

    /**
     * Scope a query to only include required fields.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope a query to only include searchable fields.
     */
    public function scopeSearchable($query)
    {
        return $query->where('is_searchable', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get field type label in Turkish.
     */
    public function getFieldTypeLabelAttribute(): string
    {
        return match($this->field_type) {
            'text' => 'Metin',
            'number' => 'Sayı',
            'select' => 'Seçim Listesi',
            'boolean' => 'Evet/Hayır',
            'textarea' => 'Uzun Metin',
            default => 'Bilinmiyor',
        };
    }

    /**
     * Check if field has options (for select type).
     */
    public function hasOptions(): bool
    {
        return $this->field_type === 'select' && !empty($this->field_options);
    }

    /**
     * Get formatted options for select field.
     */
    public function getFormattedOptionsAttribute(): array
    {
        if (!$this->hasOptions()) {
            return [];
        }

        $options = [];
        foreach ($this->field_options as $key => $value) {
            if (is_array($value)) {
                $options[$key] = $value['label'] ?? $value['value'] ?? $key;
            } else {
                $options[$key] = $value;
            }
        }

        return $options;
    }

    /**
     * Get validation rules for this field.
     */
    public function getValidationRules(): array
    {
        $rules = [];

        if ($this->is_required) {
            $rules[] = 'required';
        } else {
            $rules[] = 'nullable';
        }

        switch ($this->field_type) {
            case 'number':
                $rules[] = 'numeric';
                break;
            case 'boolean':
                $rules[] = 'boolean';
                break;
            case 'select':
                if ($this->hasOptions()) {
                    $rules[] = 'in:' . implode(',', array_keys($this->formatted_options));
                }
                break;
            case 'text':
            case 'textarea':
                $rules[] = 'string';
                if ($this->field_type === 'text') {
                    $rules[] = 'max:255';
                }
                break;
        }

        return $rules;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set sort order when creating
        static::creating(function ($field) {
            if (is_null($field->sort_order)) {
                $maxOrder = static::where('category_id', $field->category_id)->max('sort_order');
                $field->sort_order = ($maxOrder ?? 0) + 1;
            }
        });
    }
}
