@props(['messages'])

@if ($messages)
    <div class="my-2 space-y-2">
        @foreach ((array) $messages as $message)
            <div class="flex items-start space-x-2 p-3 text-sm text-red-700 bg-red-50 border border-red-200 rounded-lg shadow-sm">
                <svg class="flex-shrink-0 w-4 h-4 mt-0.5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd"></path>
                </svg>
                <span class="font-medium">{{ $message }}</span>
            </div>
        @endforeach
    </div>
@endif
