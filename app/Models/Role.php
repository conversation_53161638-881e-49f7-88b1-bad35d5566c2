<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    protected $fillable = [
        'name',
        'label',
    ];

    /**
     * Rol yoksa <PERSON>.
     */
    public static function createIfNotExists(string $name, ?string $label = null): self
    {
        return static::firstOrCreate(
            ['name' => $name],
            ['label' => $label ?? $name]
        );
    }

    /**
     * Rolü ada göre bul.
     */
    public static function findByName(string $name): ?self
    {
        return static::where('name', $name)->first();
    }
}
