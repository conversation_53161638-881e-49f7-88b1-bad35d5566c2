<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_links', function (Blueprint $table) {
            $table->id();
            $table->uuid('company_id');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade'); // firmaya ait
            $table->string('title'); // bağlantı başlığı
            $table->string('icon')->nullable(); // ikon (ör. FontAwesome class veya resim yolu)
            $table->string('url'); // yönlendirme linki
            $table->boolean('is_active')->default(true); // aktif/pasif
            $table->integer('sort_order')->default(0); // sıralama
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_links');
    }
};
