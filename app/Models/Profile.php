<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Profile extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'birth_date',
        'gender',
        'phone',
        'whatsapp',
        'city_id',
        'district_id',
        'address',
        'bio',
        'avatar_url',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'birth_date' => 'date',
    ];

    /**
     * Get the user that owns the profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the city that the profile belongs to.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id', 'il_id');
    }

    /**
     * Get the district that the profile belongs to.
     */
    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_id', 'ilce_id');
    }

    /**
     * Get the full avatar URL.
     */
    public function getAvatarUrlAttribute($value): ?string
    {
        return $value ? asset($value) : null;
    }

    /**
     * Get the formatted phone number.
     */
    public function getFormattedPhoneAttribute(): ?string
    {
        if (!$this->phone) {
            return null;
        }

        // Format: 0(555)123-45-67
        $phone = preg_replace('/\D/', '', $this->phone);

        if (strlen($phone) === 11 && $phone[0] === '0') {
            return sprintf('0(%s)%s-%s-%s',
                substr($phone, 1, 3),
                substr($phone, 4, 3),
                substr($phone, 7, 2),
                substr($phone, 9, 2)
            );
        }

        return $this->phone;
    }

    /**
     * Get the formatted WhatsApp number.
     */
    public function getFormattedWhatsappAttribute(): ?string
    {
        if (!$this->whatsapp) {
            return null;
        }

        // Format: 0(555)123-45-67
        $whatsapp = preg_replace('/\D/', '', $this->whatsapp);

        if (strlen($whatsapp) === 11 && $whatsapp[0] === '0') {
            return sprintf('0(%s)%s-%s-%s',
                substr($whatsapp, 1, 3),
                substr($whatsapp, 4, 3),
                substr($whatsapp, 7, 2),
                substr($whatsapp, 9, 2)
            );
        }

        return $this->whatsapp;
    }

    /**
     * Get the full address with city and district.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = [];

        if ($this->address) {
            $parts[] = $this->address;
        }

        if ($this->district) {
            $parts[] = $this->district->ilce_adi;
        }

        if ($this->city) {
            $parts[] = $this->city->il_adi;
        }

        return implode(', ', $parts);
    }
}
