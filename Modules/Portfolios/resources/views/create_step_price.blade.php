<!-- <PERSON><PERSON><PERSON> & <PERSON>aylar Adımı -->
<div x-show="activeTab === 'price'"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-x-8"
     x-transition:enter-end="opacity-100 transform translate-x-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-x-0"
     x-transition:leave-end="opacity-0 transform -translate-x-8"
     class="space-y-8">

    <!-- <PERSON><PERSON><PERSON> -->
    <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full mb-4">
            <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Fiyat & Detaylar</h2>
        <p class="text-gray-600">Portföyünüzün fiyat ve alan bilgilerini girin</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Fiyat -->
        <div class="lg:col-span-2">
            <label for="price" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Fiyat <span class="text-red-500">*</span>
            </label>
            <div class="relative">
                <input type="number"
                       id="price"
                       name="price"
                       x-model="formData.price"
                       @input="calculatePricePerM2"
                       @blur="validateField('price')"
                       :class="errors.price ? 'border-red-500 bg-red-50' : 'border-gray-200'"
                       class="w-full pl-4 pr-20 py-3 bg-white/50 border rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                       placeholder="0.00" required>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <select name="currency" class="border-0 bg-transparent text-gray-500 text-sm focus:ring-0 pr-8">
                        <option value="TRY" {{ old('currency', 'TRY') == 'TRY' ? 'selected' : '' }}>₺ TRY</option>
                        <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>$ USD</option>
                        <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>€ EUR</option>
                    </select>
                </div>
            </div>
            <div x-show="errors.price" class="mt-2 text-sm text-red-600 flex items-center gap-1" x-transition>
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                </svg>
                <span x-text="errors.price"></span>
            </div>
            @error('price')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- M² Fiyatı (Hesaplanmış) -->
        <div>
            <label class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 15.75V4.5a1.5 1.5 0 00-1.5-1.5h-3a1.5 1.5 0 00-1.5 1.5v11.25m6 0a2.25 2.25 0 002.25 2.25h2.25a2.25 2.25 0 002.25-2.25V6.75a2.25 2.25 0 00-2.25-2.25H15.75a2.25 2.25 0 00-2.25 2.25v9z" />
                </svg>
                M² Fiyatı
            </label>
            <div class="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                <div class="text-center">
                    <span x-text="pricePerM2" class="text-lg font-bold text-green-600"></span>
                    <p class="text-xs text-green-500 mt-1">Otomatik hesaplanır</p>
                </div>
            </div>
            <input type="hidden"
                   id="price_per_m2"
                   name="price_per_m2"
                   :value="pricePerM2.includes('₺') ? pricePerM2.replace(/[^\d]/g, '') : ''">
        </div>

        <!-- Brüt Alan -->
        <div>
            <label for="gross_area" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
                </svg>
                Brüt Alan (m²)
            </label>
            <div class="relative">
                <input type="number"
                       id="gross_area"
                       name="gross_area"
                       x-model="formData.gross_area"
                       @input="calculatePricePerM2"
                       value="{{ old('gross_area') }}"
                       step="0.01"
                       class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 @error('gross_area') border-red-500 @enderror"
                       placeholder="0.00">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span class="text-gray-400 text-sm">m²</span>
                </div>
            </div>
            @error('gross_area')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- Net Alan -->
        <div>
            <label for="net_area" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 01-1.125-1.125v-3.75zM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 01-1.125-1.125v-8.25zM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 01-1.125-1.125v-2.25z" />
                </svg>
                Net Alan (m²)
            </label>
            <div class="relative">
                <input type="number"
                       id="net_area"
                       name="net_area"
                       x-model="formData.net_area"
                       @input="calculatePricePerM2"
                       value="{{ old('net_area') }}"
                       step="0.01"
                       class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 @error('net_area') border-red-500 @enderror"
                       placeholder="0.00">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span class="text-gray-400 text-sm">m²</span>
                </div>
            </div>
            @error('net_area')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- Kimden -->
        <div>
            <label for="from_whom" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                </svg>
                Kimden
            </label>
            <select id="from_whom"
                    name="from_whom"
                    class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 @error('from_whom') border-red-500 @enderror">
                <option value="">Seçin</option>
                <option value="owner" {{ old('from_whom') == 'owner' ? 'selected' : '' }}>🏠 Sahibinden</option>
                <option value="agent" {{ old('from_whom') == 'agent' ? 'selected' : '' }}>🏢 Emlak Ofisinden</option>
                <option value="developer" {{ old('from_whom') == 'developer' ? 'selected' : '' }}>🏗️ Yapı Kooperatifinden</option>
            </select>
            @error('from_whom')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>

        <!-- Kredi Uygunluğu -->
        <div class="flex items-center space-x-3 p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <input type="checkbox"
                   id="is_loan_eligible"
                   name="is_loan_eligible"
                   value="1"
                   {{ old('is_loan_eligible') ? 'checked' : '' }}
                   class="w-5 h-5 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
            <label for="is_loan_eligible" class="flex items-center gap-2 text-sm font-medium text-blue-700">
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H4.5m2.25 0v3m0 0v.375c0 .621-.504 1.125-1.125 1.125H4.5m2.25 0H7.5m0 0v-.375c0-.621.504-1.125 1.125-1.125h.375m0 0H9.75v1.5m0 0h.375c0 .621-.504 1.125-1.125 1.125H9.75v.375c0 .621-.504 1.125-1.125 1.125H8.25v-1.5m0 0H7.5m0 0v.375c0 .621-.504 1.125-1.125 1.125H5.25m0 0v1.5m0 0v.375c0 .621-.504 1.125-1.125 1.125H3.75" />
                </svg>
                Krediye Uygun
            </label>
        </div>

        <!-- Takas -->
        <div class="flex items-center space-x-3 p-4 bg-purple-50 border border-purple-200 rounded-xl">
            <input type="checkbox"
                   id="swap"
                   name="swap"
                   value="1"
                   {{ old('swap') ? 'checked' : '' }}
                   class="w-5 h-5 text-purple-600 bg-white border-gray-300 rounded focus:ring-purple-500 focus:ring-2">
            <label for="swap" class="flex items-center gap-2 text-sm font-medium text-purple-700">
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
                </svg>
                Takasa Uygun
            </label>
        </div>

        <!-- Tapu Durumu -->
        <div class="lg:col-span-2">
            <label for="deed_status" class="flex items-center gap-2 text-sm font-semibold text-gray-700 mb-3">
                <svg class="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                </svg>
                Tapu Durumu
            </label>
            <select id="deed_status"
                    name="deed_status"
                    class="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 @error('deed_status') border-red-500 @enderror">
                <option value="">Tapu durumu seçin</option>
                <option value="clean" {{ old('deed_status') == 'clean' ? 'selected' : '' }}>✅ Temiz Tapu</option>
                <option value="shared" {{ old('deed_status') == 'shared' ? 'selected' : '' }}>👥 Hisseli Tapu</option>
                <option value="condominium" {{ old('deed_status') == 'condominium' ? 'selected' : '' }}>🏢 Kat Mülkiyeti</option>
                <option value="land_registry" {{ old('deed_status') == 'land_registry' ? 'selected' : '' }}>📋 Arsa Tapulu</option>
                <option value="other" {{ old('deed_status') == 'other' ? 'selected' : '' }}>❓ Diğer</option>
            </select>
            @error('deed_status')
                <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                    {{ $message }}
                </p>
            @enderror
        </div>
    </div>

    <!-- Fiyat Özeti Kartı -->
    <div class="mt-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl">
        <h3 class="text-lg font-semibold text-green-800 mb-4 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Fiyat Özeti
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-white rounded-xl border border-green-100">
                <p class="text-sm text-gray-600 mb-1">Toplam Fiyat</p>
                <p class="text-xl font-bold text-green-600" x-text="formatPrice(formData.price || 0)"></p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-green-100">
                <p class="text-sm text-gray-600 mb-1">Net Alan</p>
                <p class="text-lg font-semibold text-gray-800" x-text="(formData.net_area || 0) + ' m²'"></p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-green-100">
                <p class="text-sm text-gray-600 mb-1">M² Fiyatı</p>
                <p class="text-lg font-semibold text-gray-800" x-text="pricePerM2"></p>
            </div>
        </div>
    </div>
</div>
