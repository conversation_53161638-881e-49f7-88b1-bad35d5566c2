<!-- <PERSON><PERSON><PERSON> -->
<div x-show="activeTab === 'media'"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-x-8"
     x-transition:enter-end="opacity-100 transform translate-x-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-x-0"
     x-transition:leave-end="opacity-0 transform -translate-x-8"
     class="space-y-8">

    <!-- <PERSON><PERSON><PERSON> -->
    <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-full mb-4">
            <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
            </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Galeri & Medya</h2>
        <p class="text-gray-600">Portföyünüzün fotoğraflarını ve videolarını ekleyin</p>
    </div>

    <!-- Fotoğraf Yükleme Alanı -->
    <div class="bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-2xl p-6">
        <h3 class="text-lg font-semibold text-indigo-800 mb-6 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
            </svg>
            Fotoğraflar
            <span class="text-sm font-normal text-indigo-600">(Maksimum 20 fotoğraf)</span>
        </h3>

        <!-- Drag & Drop Alanı -->
        <div x-data="imageUploader()" class="space-y-6">
            <!-- Upload Area -->
            <div @drop="handleDrop($event)" 
                 @dragover.prevent 
                 @dragenter.prevent
                 @dragleave="dragLeave($event)"
                 :class="isDragging ? 'border-indigo-500 bg-indigo-100' : 'border-gray-300'"
                 class="border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer hover:border-indigo-400 hover:bg-indigo-50"
                 @click="$refs.imageInput.click()">
                
                <input type="file" 
                       x-ref="imageInput"
                       @change="handleFileSelect($event)"
                       multiple 
                       accept="image/*"
                       class="hidden">
                
                <div class="flex flex-col items-center">
                    <svg class="w-12 h-12 text-indigo-400 mb-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z" />
                    </svg>
                    <h4 class="text-lg font-semibold text-gray-700 mb-2">Fotoğrafları buraya sürükleyin</h4>
                    <p class="text-gray-500 mb-4">veya dosya seçmek için tıklayın</p>
                    <div class="flex items-center gap-4 text-sm text-gray-400">
                        <span>JPG, PNG, WEBP</span>
                        <span>•</span>
                        <span>Maksimum 5MB</span>
                        <span>•</span>
                        <span>En fazla 20 dosya</span>
                    </div>
                </div>
            </div>

            <!-- Yüklenen Fotoğraflar -->
            <div x-show="images.length > 0" class="space-y-4">
                <h4 class="text-md font-semibold text-gray-700 flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                    </svg>
                    Yüklenen Fotoğraflar (<span x-text="images.length"></span>)
                </h4>
                
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <template x-for="(image, index) in images" :key="index">
                        <div class="relative group bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                            <!-- Fotoğraf -->
                            <div class="aspect-square">
                                <img :src="image.preview" 
                                     :alt="'Fotoğraf ' + (index + 1)"
                                     class="w-full h-full object-cover">
                            </div>
                            
                            <!-- Overlay -->
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                                    <!-- Ana Fotoğraf Yap -->
                                    <button type="button"
                                            @click="setMainImage(index)"
                                            :class="image.isMain ? 'bg-yellow-500' : 'bg-blue-500'"
                                            class="p-2 text-white rounded-full hover:scale-110 transition-transform duration-200">
                                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
                                        </svg>
                                    </button>
                                    
                                    <!-- Sil -->
                                    <button type="button"
                                            @click="removeImage(index)"
                                            class="p-2 bg-red-500 text-white rounded-full hover:scale-110 transition-transform duration-200">
                                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Ana Fotoğraf Badge -->
                            <div x-show="image.isMain" 
                                 class="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                                Ana Fotoğraf
                            </div>
                            
                            <!-- Sıra Numarası -->
                            <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full">
                                <span x-text="index + 1"></span>
                            </div>
                        </div>
                    </template>
                </div>
                
                <!-- Fotoğraf Sıralama Bilgisi -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start gap-3">
                        <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                        </svg>
                        <div>
                            <h5 class="text-sm font-medium text-blue-800 mb-1">Fotoğraf Sıralama İpuçları</h5>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• İlk fotoğraf otomatik olarak ana fotoğraf olur</li>
                                <li>• Yıldız butonuna tıklayarak ana fotoğrafı değiştirebilirsiniz</li>
                                <li>• Ana fotoğraf portföy listesinde görünecek kapak fotoğrafıdır</li>
                                <li>• Fotoğrafları sürükleyerek sıralayabilirsiniz</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Yükleme Alanı -->
    <div class="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-2xl p-6">
        <h3 class="text-lg font-semibold text-purple-800 mb-6 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z" />
            </svg>
            Video
            <span class="text-sm font-normal text-purple-600">(Opsiyonel)</span>
        </h3>

        <div class="space-y-4">
            <!-- Video URL -->
            <div>
                <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">
                    Video URL (YouTube, Vimeo vb.)
                </label>
                <div class="relative">
                    <input type="url"
                           id="video_url"
                           name="video_url"
                           value="{{ old('video_url') }}"
                           class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                           placeholder="https://www.youtube.com/watch?v=...">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                        </svg>
                    </div>
                </div>
                <p class="mt-2 text-sm text-gray-500">YouTube, Vimeo veya diğer video platformlarından URL ekleyebilirsiniz</p>
            </div>

            <!-- Video Yükleme -->
            <div class="border-t border-gray-200 pt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Veya Video Dosyası Yükleyin
                </label>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 hover:bg-purple-50 transition-all duration-200 cursor-pointer"
                     onclick="document.getElementById('video_file').click()">
                    <input type="file" 
                           id="video_file"
                           name="video_file"
                           accept="video/*"
                           class="hidden">
                    
                    <svg class="w-10 h-10 text-purple-400 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M12 18.75H4.5a2.25 2.25 0 0 1-2.25-2.25v-9a2.25 2.25 0 0 1 2.25-2.25H12m0 13.5v-13.5m0 0V2.25A2.25 2.25 0 0 1 14.25 0h3a2.25 2.25 0 0 1 2.25 2.25v6.75" />
                    </svg>
                    <h4 class="text-md font-medium text-gray-700 mb-1">Video dosyası seçin</h4>
                    <p class="text-sm text-gray-500">MP4, MOV, AVI • Maksimum 100MB</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 360° Sanal Tur -->
    <div class="bg-gradient-to-r from-cyan-50 to-blue-50 border border-cyan-200 rounded-2xl p-6">
        <h3 class="text-lg font-semibold text-cyan-800 mb-6 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9Z" />
            </svg>
            360° Sanal Tur
            <span class="text-sm font-normal text-cyan-600">(Opsiyonel)</span>
        </h3>

        <div>
            <label for="virtual_tour_url" class="block text-sm font-medium text-gray-700 mb-2">
                Sanal Tur URL
            </label>
            <div class="relative">
                <input type="url"
                       id="virtual_tour_url"
                       name="virtual_tour_url"
                       value="{{ old('virtual_tour_url') }}"
                       class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-all duration-200"
                       placeholder="https://...">
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9Z" />
                    </svg>
                </div>
            </div>
            <p class="mt-2 text-sm text-gray-500">Matterport, Kuula veya diğer 360° tur platformlarından URL ekleyebilirsiniz</p>
        </div>
    </div>

    <!-- Medya Özeti -->
    <div class="mt-8 p-6 bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-2xl">
        <h3 class="text-lg font-semibold text-indigo-800 mb-4 flex items-center gap-2">
            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 0A2.25 2.25 0 0 1 4.5 3.375h15A2.25 2.25 0 0 1 21.75 5.625m-17.25 0v12.75m17.25-12.75v12.75M21.75 18.375A1.125 1.125 0 0 1 20.625 19.5h-7.5m9.75-12.75H11.25m10.5 0a2.25 2.25 0 0 0-2.25-2.25" />
            </svg>
            Medya Özeti
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-white rounded-xl border border-indigo-100">
                <p class="text-sm text-gray-600 mb-1">Fotoğraf Sayısı</p>
                <p class="text-xl font-bold text-indigo-600" x-text="images ? images.length : 0"></p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-indigo-100">
                <p class="text-sm text-gray-600 mb-1">Video</p>
                <p class="text-lg font-semibold text-gray-800">
                    <span id="video-status">Eklenmedi</span>
                </p>
            </div>
            <div class="text-center p-4 bg-white rounded-xl border border-indigo-100">
                <p class="text-sm text-gray-600 mb-1">360° Tur</p>
                <p class="text-lg font-semibold text-gray-800">
                    <span id="tour-status">Eklenmedi</span>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
function imageUploader() {
    return {
        images: [],
        isDragging: false,
        maxFiles: 20,
        maxFileSize: 5 * 1024 * 1024, // 5MB
        
        handleDrop(e) {
            e.preventDefault();
            this.isDragging = false;
            const files = Array.from(e.dataTransfer.files);
            this.processFiles(files);
        },
        
        handleFileSelect(e) {
            const files = Array.from(e.target.files);
            this.processFiles(files);
        },
        
        processFiles(files) {
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (this.images.length + imageFiles.length > this.maxFiles) {
                alert(`Maksimum ${this.maxFiles} fotoğraf yükleyebilirsiniz.`);
                return;
            }
            
            imageFiles.forEach(file => {
                if (file.size > this.maxFileSize) {
                    alert(`${file.name} dosyası çok büyük. Maksimum 5MB olmalıdır.`);
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.images.push({
                        file: file,
                        preview: e.target.result,
                        isMain: this.images.length === 0 // İlk fotoğraf ana fotoğraf olur
                    });
                };
                reader.readAsDataURL(file);
            });
        },
        
        removeImage(index) {
            const wasMain = this.images[index].isMain;
            this.images.splice(index, 1);
            
            // Eğer ana fotoğraf silindiyse, ilk fotoğrafı ana yap
            if (wasMain && this.images.length > 0) {
                this.images[0].isMain = true;
            }
        },
        
        setMainImage(index) {
            // Tüm fotoğrafları ana olmayan yap
            this.images.forEach(img => img.isMain = false);
            // Seçilen fotoğrafı ana yap
            this.images[index].isMain = true;
        },
        
        dragLeave(e) {
            if (!e.currentTarget.contains(e.relatedTarget)) {
                this.isDragging = false;
            }
        }
    }
}

// Video ve tur durumu takibi
document.addEventListener('DOMContentLoaded', function() {
    const videoUrlInput = document.getElementById('video_url');
    const videoFileInput = document.getElementById('video_file');
    const tourUrlInput = document.getElementById('virtual_tour_url');
    const videoStatus = document.getElementById('video-status');
    const tourStatus = document.getElementById('tour-status');
    
    function updateVideoStatus() {
        if (videoUrlInput.value || videoFileInput.files.length > 0) {
            videoStatus.textContent = 'Eklendi';
            videoStatus.className = 'text-green-600 font-semibold';
        } else {
            videoStatus.textContent = 'Eklenmedi';
            videoStatus.className = 'text-gray-800 font-semibold';
        }
    }
    
    function updateTourStatus() {
        if (tourUrlInput.value) {
            tourStatus.textContent = 'Eklendi';
            tourStatus.className = 'text-green-600 font-semibold';
        } else {
            tourStatus.textContent = 'Eklenmedi';
            tourStatus.className = 'text-gray-800 font-semibold';
        }
    }
    
    videoUrlInput.addEventListener('input', updateVideoStatus);
    videoFileInput.addEventListener('change', updateVideoStatus);
    tourUrlInput.addEventListener('input', updateTourStatus);
});
</script>
