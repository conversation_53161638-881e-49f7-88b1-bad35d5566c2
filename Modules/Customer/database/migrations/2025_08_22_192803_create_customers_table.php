<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            // onDelete('cascade') ile şirket silindiğinde ona bağlı tüm müşteriler de silinir.
            $table->foreignUuid('company_id')->constrained('companies')->onDelete('cascade');

            $table->foreignUuid('customer_group_id')->nullable()->constrained()->onDelete('set null');

            $table->foreignId('agent_id')->nullable()->constrained('users')->onDelete('set null');

            $table->string('first_name');
            $table->string('last_name');
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->date('birth_date')->nullable();
            $table->string('company_name')->nullable();
            $table->string('email')->nullable()->unique();
            $table->string('phone', 20)->nullable();
            $table->string('whatsapp', 20)->nullable();
            $table->text('address')->nullable();
            $table->smallInteger('city_id')->nullable();
            $table->smallInteger('district_id')->nullable();

            $table->enum('status', ['potential', 'active', 'inactive', 'archived'])->default('active');

            $table->decimal('minimum_income', 15, 2)->nullable()->comment('Minimum Aylık Gelir');
            $table->decimal('investment_budget', 15, 2)->nullable()->comment('Yatırım Bütçesi');
            $table->string('currency', 5)->default('TRY')->comment('Para Birimi Kodu (TRY, USD, EUR)');
            $table->string('nationality', 30)->nullable()->comment('Uyruk');
            $table->smallInteger('occupation')->nullable()->comment('Meslek / İş Alanı');

            $table->boolean('wants_sms')->default(false)->comment('SMS almak istiyor mu?');
            $table->boolean('wants_email')->default(false)->comment('Promosyon vb. e-posta almak istiyor mu?');

            // Müşterinin nereden geldiği (Referans, Web Sitesi, Sosyal Medya vb.)
            $table->smallInteger('source')->nullable();
            // Müşteri ile ilgili genel notlar için
            $table->text('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
