<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Company;
use App\Models\Order;
use App\Models\Package;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $company = $user->company ?? null;

        // Eğer kullanıcının şirketi yoksa, şirket oluşturma sayfasına yönlendir
        if (!$company && !$user->isSuperAdmin()) {
            return redirect()->route('company.create')->with('info', 'Lütfen önce şirket bilgilerinizi tamamlayın.');
        }

        // Dashboard istatistikleri
        $stats = $this->getDashboardStats($company);

        // Son aktiviteler
        $recentActivities = $this->getRecentActivities($company);

        // Grafik verileri
        $chartData = $this->getChartData($company);

        // Performans metrikleri
        $performance = $this->getPerformanceMetrics($company);

        // Yaklaşan görevler
        $upcomingTasks = $this->getUpcomingTasks();

        // Akıllı eşleşmeler
        $smartMatches = $this->getSmartMatches();

        // Pazar içgörüleri
        $marketInsights = $this->getMarketInsights();

        return view('dashboard', compact(
            'stats',
            'recentActivities',
            'chartData',
            'performance',
            'upcomingTasks',
            'smartMatches',
            'marketInsights'
        ));
    }

    private function getDashboardStats($company)
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        // Eğer şirket yoksa varsayılan değerler döndür
        if (!$company) {
            return $this->getDefaultStats();
        }

        // Gerçek şirket verileri burada hesaplanacak
        // Şimdilik örnek veriler döndürüyoruz
        return [
            'total_customers' => [
                'value' => 1247, // Bu değeri gerçek müşteri sayısıyla değiştirin
                'change' => 12,
                'trend' => 'up'
            ],
            'monthly_sales' => [
                'value' => 824500,
                'change' => 8.2,
                'trend' => 'up'
            ],
            'active_listings' => [
                'value' => 186, // Bu değeri gerçek ilan sayısıyla değiştirin
                'change' => -3.2,
                'trend' => 'down'
            ],
            'pending_tasks' => [
                'value' => 23, // Bu değeri gerçek bekleyen işlem sayısıyla değiştirin
                'change' => 0,
                'trend' => 'neutral'
            ],
            'monthly_target' => [
                'value' => 1200000,
                'current' => 824500,
                'percentage' => 68,
                'days_remaining' => Carbon::now()->endOfMonth()->diffInDays(Carbon::now())
            ],
            'total_views' => [
                'value' => 45200,
                'change' => 24,
                'trend' => 'up'
            ]
        ];
    }

    private function getRecentActivities($company)
    {
        if (!$company) {
            return [];
        }

        return [
            [
                'type' => 'customer_added',
                'title' => 'Yeni müşteri eklendi',
                'description' => 'Ahmet Yılmaz',
                'time' => '2 saniye önce',
                'icon' => 'user',
                'color' => 'blue'
            ],
            [
                'type' => 'sale_completed',
                'title' => 'Villa satışı tamamlandı',
                'description' => '₺850,000',
                'time' => '5 dakika önce',
                'icon' => 'home',
                'color' => 'green'
            ],
            [
                'type' => 'listing_pending',
                'title' => 'Daire ilanı onay bekliyor',
                'description' => 'Bahçelievler',
                'time' => '1 saat önce',
                'icon' => 'document',
                'color' => 'yellow'
            ],
            [
                'type' => 'customer_call',
                'title' => 'Müşteri araması',
                'description' => 'Melis Kaya',
                'time' => '2 dakika önce',
                'icon' => 'phone',
                'color' => 'purple'
            ]
        ];
    }

    private function getChartData($company)
    {
        // Son 12 ayın satış verileri (örnek)
        $months = [];
        $sales = [];
        $targets = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');

            if ($company) {
                $sales[] = rand(400000, 900000); // Gerçek verilerle değiştirin
                $targets[] = 800000; // Gerçek hedef verilerle değiştirin
            } else {
                $sales[] = 0;
                $targets[] = 0;
            }
        }

        return [
            'labels' => $months,
            'datasets' => [
                [
                    'label' => 'Satışlar',
                    'data' => $sales,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4
                ],
                [
                    'label' => 'Hedefler',
                    'data' => $targets,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'tension' => 0.4
                ]
            ]
        ];
    }

    private function getPerformanceMetrics($company)
    {
        if (!$company) {
            return [
                'efficiency' => 0,
                'growth' => 0,
                'target_completion' => 0,
                'market_trend' => 0,
                'competition_rank' => 0,
                'opportunity_score' => 0
            ];
        }

        return [
            'efficiency' => 92,
            'growth' => 18,
            'target_completion' => 68,
            'market_trend' => 12.5,
            'competition_rank' => 2,
            'opportunity_score' => 8.7
        ];
    }

    private function getDefaultStats()
    {
        return [
            'total_customers' => [
                'value' => 0,
                'change' => 0,
                'trend' => 'neutral'
            ],
            'monthly_sales' => [
                'value' => 0,
                'change' => 0,
                'trend' => 'neutral'
            ],
            'active_listings' => [
                'value' => 0,
                'change' => 0,
                'trend' => 'neutral'
            ],
            'pending_tasks' => [
                'value' => 0,
                'change' => 0,
                'trend' => 'neutral'
            ],
            'monthly_target' => [
                'value' => 0,
                'current' => 0,
                'percentage' => 0,
                'days_remaining' => Carbon::now()->endOfMonth()->diffInDays(Carbon::now())
            ],
            'total_views' => [
                'value' => 0,
                'change' => 0,
                'trend' => 'neutral'
            ]
        ];
    }

    public function getUpcomingTasks()
    {
        return [
            [
                'id' => 1,
                'title' => 'Müşteri Görüşmesi',
                'description' => 'Ahmet Yılmaz ile villa görüşmesi',
                'due_date' => Carbon::now()->addHours(2),
                'priority' => 'high',
                'type' => 'meeting',
                'customer' => 'Ahmet Yılmaz',
                'property' => 'Bahçelievler Villa'
            ],
            [
                'id' => 2,
                'title' => 'Sözleşme İmzalama',
                'description' => 'Daire satış sözleşmesi imzalanacak',
                'due_date' => Carbon::now()->addHours(4),
                'priority' => 'high',
                'type' => 'contract',
                'customer' => 'Melis Kaya',
                'property' => 'Kadıköy Daire'
            ],
            [
                'id' => 3,
                'title' => 'Emlak Fotoğraf Çekimi',
                'description' => 'Yeni eklenen villa için fotoğraf çekimi',
                'due_date' => Carbon::now()->addDay(),
                'priority' => 'medium',
                'type' => 'photography',
                'property' => 'Beşiktaş Villa'
            ],
            [
                'id' => 4,
                'title' => 'Müşteri Takibi',
                'description' => 'Geçen hafta görüşülen müşteri ile iletişim',
                'due_date' => Carbon::now()->addDays(2),
                'priority' => 'low',
                'type' => 'followup',
                'customer' => 'Can Özdemir'
            ]
        ];
    }

    public function getSmartMatches()
    {
        return [
            [
                'id' => 1,
                'customer' => [
                    'name' => 'Ayşe Demir',
                    'budget' => '800000-1200000',
                    'preferences' => ['3+1', 'Bahçelievler', 'Asansörlü']
                ],
                'property' => [
                    'title' => 'Bahçelievler 3+1 Daire',
                    'price' => 950000,
                    'features' => ['3+1', 'Asansörlü', 'Otopark']
                ],
                'match_score' => 95,
                'reasons' => ['Bütçe uyumu', 'Konum tercihi', 'Oda sayısı']
            ],
            [
                'id' => 2,
                'customer' => [
                    'name' => 'Mehmet Kaya',
                    'budget' => '1500000-2000000',
                    'preferences' => ['Villa', 'Bahçeli', 'Beşiktaş']
                ],
                'property' => [
                    'title' => 'Beşiktaş Müstakil Villa',
                    'price' => 1750000,
                    'features' => ['Villa', 'Bahçeli', 'Deniz manzarası']
                ],
                'match_score' => 88,
                'reasons' => ['Bütçe uyumu', 'Tip tercihi', 'Bahçe özelliği']
            ],
            [
                'id' => 3,
                'customer' => [
                    'name' => 'Fatma Özkan',
                    'budget' => '600000-900000',
                    'preferences' => ['2+1', 'Kadıköy', 'Metro yakını']
                ],
                'property' => [
                    'title' => 'Kadıköy 2+1 Residence',
                    'price' => 750000,
                    'features' => ['2+1', 'Metro 5dk', 'Güvenlik']
                ],
                'match_score' => 92,
                'reasons' => ['Bütçe uyumu', 'Konum tercihi', 'Ulaşım avantajı']
            ]
        ];
    }

    public function getMarketInsights()
    {
        return [
            'trending_areas' => [
                ['name' => 'Bahçelievler', 'growth' => 15.2, 'avg_price' => 850000],
                ['name' => 'Kadıköy', 'growth' => 12.8, 'avg_price' => 920000],
                ['name' => 'Beşiktaş', 'growth' => 18.5, 'avg_price' => 1250000]
            ],
            'price_predictions' => [
                'next_month' => 8.5,
                'next_quarter' => 12.3,
                'next_year' => 25.7
            ],
            'market_temperature' => 'hot', // hot, warm, cool, cold
            'best_investment_types' => ['Villa', '3+1 Daire', 'Residence']
        ];
    }
}
