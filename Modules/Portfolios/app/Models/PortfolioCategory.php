<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Collection;

class PortfolioCategory extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolio_categories';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'slug',
        'parent_id',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the parent category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(PortfolioCategory::class, 'parent_id', 'id');
    }

    /**
     * Get the child categories.
     */
    public function children(): HasMany
    {
        return $this->hasMany(PortfolioCategory::class, 'parent_id', 'id');
    }

    /**
     * Get all descendants recursively.
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get the portfolios in this category.
     */
    public function portfolios(): HasMany
    {
        return $this->hasMany(Portfolio::class, 'category_id', 'id');
    }

    /**
     * Get the feature groups for this category.
     */
    public function featureGroups(): HasMany
    {
        return $this->hasMany(PortfolioCategoryFeatureGroup::class, 'category_id', 'id')
                    ->orderBy('sort_order');
    }

    /**
     * Get the form fields for this category.
     */
    public function formFields(): HasMany
    {
        return $this->hasMany(PortfolioCategoryFormField::class, 'category_id', 'id')
                    ->orderBy('sort_order');
    }

    /**
     * Scope a query to only include root categories (no parent).
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope a query to only include child categories (has parent).
     */
    public function scopeChildren($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope a query to get categories by parent ID.
     */
    public function scopeByParent($query, ?int $parentId)
    {
        return $query->where('parent_id', $parentId);
    }

    /**
     * Get all ancestor categories.
     */
    public function getAncestors(): Collection
    {
        $ancestors = collect();
        $current = $this->parent;

        while ($current) {
            $ancestors->prepend($current);
            $current = $current->parent;
        }

        return $ancestors;
    }

    /**
     * Get the full path of the category (including ancestors).
     */
    public function getFullPathAttribute(): string
    {
        $ancestors = $this->getAncestors();
        $path = $ancestors->pluck('name')->toArray();
        $path[] = $this->name;

        return implode(' > ', $path);
    }

    /**
     * Check if this category is a root category.
     */
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Check if this category has children.
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * Check if this category is a descendant of the given category.
     */
    public function isDescendantOf(PortfolioCategory $category): bool
    {
        $current = $this->parent;

        while ($current) {
            if ($current->id === $category->id) {
                return true;
            }
            $current = $current->parent;
        }

        return false;
    }

    /**
     * Get all categories as a hierarchical tree.
     */
    public static function getTree(): Collection
    {
        return static::with('children.children.children')
                    ->whereNull('parent_id')
                    ->orderBy('name')
                    ->get();
    }

    /**
     * Get categories formatted for select dropdown.
     */
    public static function getSelectOptions(): array
    {
        $categories = static::with('parent')->orderBy('name')->get();
        $options = [];

        foreach ($categories as $category) {
            $prefix = $category->parent ? '-- ' : '';
            $options[$category->id] = $prefix . $category->name;
        }

        return $options;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Prevent deletion if category has portfolios
        static::deleting(function ($category) {
            if ($category->portfolios()->exists()) {
                throw new \Exception('Bu kategoriye ait portföyler bulunduğu için silinemez.');
            }

            if ($category->children()->exists()) {
                throw new \Exception('Alt kategorileri bulunan kategori silinemez.');
            }
        });
    }
}
