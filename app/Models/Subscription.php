<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class Subscription extends Model
{
    protected $fillable = [
        'company_id',
        'package_id',
        'status',
        'starts_at',
        'ends_at',
        'auto_renew',
    ];

    protected $casts = [
        'starts_at' => 'date',
        'ends_at' => 'date',
        'auto_renew' => 'boolean',
    ];

    /**
     * Get the company that owns the subscription.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the package for this subscription.
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' &&
               ($this->ends_at === null || $this->ends_at->isFuture());
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->ends_at !== null && $this->ends_at->isPast());
    }

    /**
     * Check if subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get days remaining until expiry.
     */
    public function getDaysRemaining(): int
    {
        if ($this->ends_at === null) {
            return -1; // Unlimited
        }

        return max(0, Carbon::now()->diffInDays($this->ends_at, false));
    }

    /**
     * Check if subscription is about to expire (within 7 days).
     */
    public function isAboutToExpire(): bool
    {
        if ($this->ends_at === null) {
            return false;
        }

        return $this->getDaysRemaining() <= 7 && $this->getDaysRemaining() > 0;
    }
}
