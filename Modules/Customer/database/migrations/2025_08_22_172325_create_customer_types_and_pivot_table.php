<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_types', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->timestamps();
        });

        Schema::create('customer_customer_type', function (Blueprint $table) {
            $table->uuid('customer_id');
            $table->uuid('customer_type_id');

            // Foreign key'ler ayrı migration'da eklenecek
            $table->primary(['customer_id', 'customer_type_id']);
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_types');
        Schema::dropIfExists('customer_customer_type');
    }
};
