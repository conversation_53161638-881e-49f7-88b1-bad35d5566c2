<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\District;
use App\Models\Neighborhoods;
use Illuminate\Http\JsonResponse;

class LocationController extends Controller
{
    /**
     * Get districts by city ID.
     */
    public function getDistricts(int $cityId): JsonResponse
    {
        $districts = District::where('il_id', $cityId)
            ->orderBy('ilce_adi')
            ->get(['ilce_id as id', 'ilce_adi']);

        return response()->json($districts);
    }

    /**
     * Get neighborhoods by district ID.
     */
    public function getNeighborhoods(int $districtId): JsonResponse
    {
        $neighborhoods = Neighborhoods::where('ilce_id', $districtId)
            ->orderBy('mahalle_adi')
            ->get(['mahalle_id as id', 'mahalle_adi']);

        return response()->json($neighborhoods);
    }
}
