<x-page-layout
    title="Profil Ayarları"
    description="Kişisel bilgilerinizi ve sistem tercihlerinizi yönetin"
    :breadcrumbs="[
        ['title' => 'Profil Ayarları']
    ]"
    :tabs="[
        'basic' => [
            'title' => 'Temel Bilgiler',
            'icon' => '<svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" /></svg>'
        ],
        'contact' => [
            'title' => 'İletişim',
            'icon' => '<svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" /></svg>'
        ],
        'theme' => [
            'title' => 'Tema Ayarları',
            'icon' => '<svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z\" /></svg>'
        ]
    ]"
    active-tab="basic"
>
    <div x-data="{
        activeTab: 'basic',
        loading: false,

        // Form data
        name: '{{ auth()->user()->name }}',
        email: '{{ auth()->user()->email }}',
        title: '{{ auth()->user()->profile?->title ?? '' }}',
        birthDate: '{{ auth()->user()->profile?->birth_date ? date('Y-m-d', strtotime(auth()->user()->profile?->birth_date)) : '' }}',
        gender: '{{ auth()->user()->profile?->gender ?? '' }}',
        phone: '{{ auth()->user()->profile?->phone ?? '' }}',
        whatsapp: '{{ auth()->user()->profile?->whatsapp ?? '' }}',
        cityId: '{{ auth()->user()->profile?->city_id ?? '' }}',
        districtId: '{{ auth()->user()->profile?->district_id ?? '' }}',
        address: '{{ auth()->user()->profile?->address ?? '' }}',
        bio: '{{ auth()->user()->profile?->bio ?? '' }}',
        themeColor: '{{ auth()->user()->theme_color }}',
        sidebarType: '{{ auth()->user()->sidebar_type ?? 'light' }}',

        // Validation states
        nameValid: true,
        emailValid: true,
        hasServerErrors: {{ $errors->any() ? 'true' : 'false' }},

        init() {
            this.validateName();
            this.validateEmail();
        },

        validateName() {
            this.nameValid = this.name.trim().length >= 2;
            return this.nameValid;
        },

        validateEmail() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            this.emailValid = emailRegex.test(this.email) && this.email.length > 0;
            return this.emailValid;
        },

        get isFormValid() {
            return this.nameValid && this.emailValid;
        },

        async submitForm() {
            this.validateName();
            this.validateEmail();

            if (this.isFormValid) {
                this.loading = true;
                await this.$nextTick();
                setTimeout(() => {
                    this.$refs.form.submit();
                }, 500);
            }
        },

        formatPhone(field) {
            let value = this[field].replace(/\D/g, '');

            if (value.startsWith('0')) {
                value = value.substring(1);
            }

            value = value.substring(0, 10);

            let masked = '';
            if (value.length > 0) {
                masked = '0(' + value.substring(0, 3);
            }
            if (value.length > 3) {
                masked += ')' + value.substring(3, 6);
            }
            if (value.length > 6) {
                masked += '-' + value.substring(6, 8);
            }
            if (value.length > 8) {
                masked += '-' + value.substring(8, 10);
            }

            this[field] = masked;
        }
    }">

        <!-- Profile Header Card -->
        <x-content-card
            title="Profil Bilgileri"
            subtitle="Profil fotoğrafınızı ve temel bilgilerinizi görüntüleyin"
            :icon="'<svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" /></svg>'"
        >
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-6">
                <div class="relative">
                    <div class="w-20 h-20 rounded-full ring-4 ring-blue-500/20 ring-offset-2 overflow-hidden bg-gray-100">
                        <img id="avatar-preview"
                             src="{{ auth()->user()->profile?->avatar_url ? asset(auth()->user()->profile?->avatar_url) : asset('assets/backend/images/user/7.jpg') }}"
                             alt="{{ auth()->user()->name }}"
                             class="w-full h-full object-cover" />
                    </div>
                    <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-3 border-white flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                </div>

                <div class="flex-1">
                    <h1 class="text-2xl font-bold text-gray-900" x-text="name"></h1>
                    <p class="text-gray-500 mb-3">{{ '@'.Str::of(auth()->user()->name)->slug('_') }}</p>
                    <div class="flex flex-wrap items-center gap-4">
                        <label for="image" class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors cursor-pointer">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            Fotoğraf Değiştir
                        </label>
                        <div class="text-xs text-gray-500">
                            Üyelik: <span class="font-medium text-gray-700">{{ optional(auth()->user()->created_at)->format('d F Y') }}</span>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 max-w-xs">
                    <div class="flex items-start gap-2">
                        <svg class="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-xs text-blue-800">Maksimum 1 MB. Büyük görseller otomatik yeniden boyutlandırılır.</p>
                    </div>
                </div>
            </div>
        </x-content-card>

        <!-- Form Content -->
        <x-content-card>
            <form x-ref="form" action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data" x-on:submit.prevent="submitForm()">
                @csrf
                @method('PUT')

                <!-- Avatar Upload (Hidden Input) -->
                <input type="file" name="image" id="image" class="hidden" accept="image/*">

                <!-- Tab Contents -->
                <div class="space-y-6">
                    <!-- Basic Info Tab -->
                    <div id="content-basic" class="tab-content">
                        <x-form-section title="Kişisel Bilgiler" columns="2">
                            <x-form-field
                                label="Ad Soyad"
                                name="name"
                                :required="true"
                                placeholder="Adınızı ve soyadınızı girin"
                                :value="auth()->user()->name"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" /></svg>'"
                            />

                            <x-form-field
                                label="Email Adresi"
                                name="email"
                                type="email"
                                :required="true"
                                placeholder="<EMAIL>"
                                :value="auth()->user()->email"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\" /></svg>'"
                            />

                            <x-form-field
                                label="Pozisyon / Ünvan"
                                name="title"
                                placeholder="Örn: Emlak Danışmanı"
                                :value="auth()->user()->profile?->title ?? ''"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V8m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v2\" /></svg>'"
                            />

                            <x-form-field
                                label="Doğum Tarihi"
                                name="birth_date"
                                type="date"
                                :value="auth()->user()->profile?->birth_date ? date('Y-m-d', strtotime(auth()->user()->profile?->birth_date)) : ''"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" /></svg>'"
                            />
                        </x-form-section>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Cinsiyet</label>
                            <div class="flex flex-wrap gap-6">
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="gender" value="male" {{ (auth()->user()->profile?->gender ?? '') == 'male' ? 'checked' : '' }}
                                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-700">Erkek</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="gender" value="female" {{ (auth()->user()->profile?->gender ?? '') == 'female' ? 'checked' : '' }}
                                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-700">Kadın</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="gender" value="other" {{ (auth()->user()->profile?->gender ?? '') == 'other' ? 'checked' : '' }}
                                           class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-700">Diğer</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Tab -->
                    <div id="content-contact" class="tab-content hidden">
                        <x-form-section title="İletişim Bilgileri" columns="2">
                            <x-form-field
                                label="Telefon Numarası"
                                name="phone"
                                type="tel"
                                placeholder="0(555)123-45-67"
                                :value="auth()->user()->profile?->phone ?? ''"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" /></svg>'"
                            />

                            <x-form-field
                                label="WhatsApp"
                                name="whatsapp"
                                type="tel"
                                placeholder="0(555)123-45-67"
                                :value="auth()->user()->profile?->whatsapp ?? ''"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" /></svg>'"
                            />

                            <x-form-field
                                label="Şehir"
                                name="city_id"
                                type="select"
                                placeholder="Şehir Seçiniz"
                                :value="auth()->user()->profile?->city_id ?? ''"
                                :options="$cities->pluck('il_adi', 'id')->toArray()"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" /><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" /></svg>'"
                            />

                            <x-form-field
                                label="İlçe"
                                name="district_id"
                                type="select"
                                placeholder="İlçe Seçiniz"
                                :value="auth()->user()->profile?->district_id ?? ''"
                                :options="[]"
                                :icon="'<svg class=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" /></svg>'"
                            />
                        </x-form-section>

                        <x-form-field
                            label="Detay Adres"
                            name="address"
                            type="textarea"
                            :rows="3"
                            placeholder="Mahalle, sokak, bina no, daire no..."
                            :value="auth()->user()->profile?->address ?? ''"
                        />

                        <x-form-field
                            label="Kişisel Açıklama"
                            name="bio"
                            type="textarea"
                            :rows="4"
                            placeholder="Kendiniz hakkında kısa bir açıklama yazın..."
                            :value="auth()->user()->profile?->bio ?? ''"
                            help="Bu açıklama müşteri profilinizde görünecektir."
                        />
                    </div>

                    <!-- Theme Tab -->
                    <div id="content-theme" class="tab-content hidden">
                        <x-form-section title="Tema Rengi">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                @php
                                    $themeColors = \App\Models\User::getThemeColors();
                                    $currentTheme = auth()->user()->theme_color;
                                @endphp
                                @foreach($themeColors as $colorKey => $colorData)
                                    <label class="cursor-pointer">
                                        <input type="radio" name="theme_color" value="{{ $colorKey }}"
                                               {{ $currentTheme == $colorKey ? 'checked' : '' }}
                                               class="sr-only peer">
                                        <div class="flex flex-col items-center gap-3 p-4 rounded-lg border-2 border-gray-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-blue-300 hover:bg-gray-50 transition-all">
                                            <div class="w-10 h-10 rounded-full shadow-lg ring-2 ring-white" style="background-color: {{ $colorData['primary'] }}"></div>
                                            <span class="text-sm font-medium text-center text-gray-700">{{ $colorData['name'] }}</span>
                                        </div>
                                    </label>
                                @endforeach
                            </div>
                        </x-form-section>

                        <x-form-section title="Header Stili">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <label class="cursor-pointer">
                                    <input type="radio" name="sidebar_type" value="light"
                                           {{ (auth()->user()->sidebar_type ?? 'light') == 'light' ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="flex items-center gap-4 p-4 rounded-lg border-2 border-gray-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-blue-300 hover:bg-gray-50 transition-all">
                                        <div class="w-10 h-10 bg-white border border-gray-300 rounded-lg shadow-sm"></div>
                                        <div>
                                            <span class="font-medium text-gray-900">Açık Tema</span>
                                            <p class="text-sm text-gray-500">Aydınlık ve temiz görünüm</p>
                                        </div>
                                    </div>
                                </label>

                                <label class="cursor-pointer">
                                    <input type="radio" name="sidebar_type" value="dark"
                                           {{ (auth()->user()->sidebar_type ?? 'light') == 'dark' ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="flex items-center gap-4 p-4 rounded-lg border-2 border-gray-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-blue-300 hover:bg-gray-50 transition-all">
                                        <div class="w-10 h-10 bg-gray-900 rounded-lg shadow-sm"></div>
                                        <div>
                                            <span class="font-medium text-gray-900">Koyu Tema</span>
                                            <p class="text-sm text-gray-500">Koyu ve modern görünüm</p>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </x-form-section>

                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start gap-3">
                                <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-sm text-blue-800">Tema ayarları tüm uygulama genelinde geçerli olacaktır.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end pt-6 border-t border-gray-200 mt-8">
                    <x-action-button
                        type="submit"
                        :loading="false"
                        :icon="'<svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" /></svg>'"
                    >
                        Profili Kaydet
                    </x-action-button>
                </div>

                <!-- Hidden Fields -->
                <input type="hidden" name="user_id" value="{{ auth()->user()->id }}">
            </form>
        </x-content-card>
    </div>

    @push('scripts')
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Avatar upload preview
        const imageInput = document.getElementById('image');
        const avatarPreview = document.getElementById('avatar-preview');

        if (imageInput && avatarPreview) {
            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        avatarPreview.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // City/District AJAX
        const citySelect = document.getElementById('city_id');
        const districtSelect = document.getElementById('district_id');

        if (citySelect && districtSelect) {
            citySelect.addEventListener('change', function() {
                const cityId = this.value;
                if (cityId) {
                    fetch(`{{ route('profile.getDistricts') }}?city_id=${cityId}`)
                        .then(response => response.text())
                        .then(data => {
                            districtSelect.innerHTML = data;
                        })
                        .catch(error => console.error('Error:', error));
                } else {
                    districtSelect.innerHTML = '<option value="">İlçe Seçiniz</option>';
                }
            });
        }
    });
    </script>
    @endpush
</x-page-layout>
