<x-backend-layout>
    @section('content')
        <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
            <!-- Animated Background Pattern -->
            <div class="absolute inset-0 opacity-20">
                <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
                <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
                <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 lg:px-6 py-8">
                <!-- Header Section -->
                <div class="mb-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        <div>
                            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                                Yeni Rol Oluştur
                            </h1>
                            <p class="text-gray-600 text-lg mt-2">Şirketiniz için yeni bir rol tanımlayın ve yetkilerini belirleyin</p>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center gap-4">
                            <a href="{{ route('roles.index') }}"
                               class="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                                </svg>
                                Geri Dön
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Create Role Form -->
                <div class="bg-white/80 border border-white/20 rounded-3xl p-8 shadow-xl">
                    <form action="{{ route('roles.store') }}" method="POST">
                        @csrf

                        <!-- Basic Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2 mb-6">
                                <svg class="w-5 h-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                                </svg>
                                Temel Bilgiler
                            </h3>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Rol Adı *</label>
                                    <input type="text" name="name" id="name" value="{{ old('name') }}"
                                           placeholder="Örn: Emlak Uzmanı"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('name') border-red-500 @enderror"
                                           required>
                                    @error('name')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Açıklama</label>
                                    <input type="text" name="description" id="description" value="{{ old('description') }}"
                                           placeholder="Bu rolün kısa açıklaması"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('description') border-red-500 @enderror">
                                    @error('description')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Permissions Section -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2 mb-6">
                                <svg class="w-5 h-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                                </svg>
                                Yetkiler
                            </h3>

                            <div class="bg-gray-50 rounded-xl p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <p class="text-sm text-gray-600">Bu role verilecek yetkileri seçin</p>
                                    <div class="flex gap-2">
                                        <button type="button" onclick="selectAllPermissions()"
                                                class="text-sm text-purple-600 hover:text-purple-700 font-medium">
                                            Tümünü Seç
                                        </button>
                                        <span class="text-gray-400">|</span>
                                        <button type="button" onclick="deselectAllPermissions()"
                                                class="text-sm text-gray-600 hover:text-gray-700 font-medium">
                                            Tümünü Kaldır
                                        </button>
                                    </div>
                                </div>

                                @php
                                    $permissionGroups = [
                                        'Kullanıcı Yönetimi' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'user');
                                        }),
                                        'Şirket Yönetimi' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'company') || str_contains($p->name, 'manage_');
                                        }),
                                        'Emlak Yönetimi' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'property') || str_contains($p->name, 'properties');
                                        }),
                                        'Müşteri Yönetimi' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'customer');
                                        }),
                                        'Raporlar' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'report');
                                        }),
                                        'Finansal' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'financial');
                                        }),
                                        'İletişim' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'message') || str_contains($p->name, 'email');
                                        }),
                                        'Sistem' => $permissions->filter(function($p) {
                                            return str_contains($p->name, 'api') || str_contains($p->name, 'webhook') || str_contains($p->name, 'template');
                                        }),
                                        'Diğer' => $permissions->filter(function($p) {
                                            return !str_contains($p->name, 'user') && !str_contains($p->name, 'company') &&
                                                   !str_contains($p->name, 'manage_') && !str_contains($p->name, 'property') &&
                                                   !str_contains($p->name, 'properties') && !str_contains($p->name, 'customer') &&
                                                   !str_contains($p->name, 'report') && !str_contains($p->name, 'financial') &&
                                                   !str_contains($p->name, 'message') && !str_contains($p->name, 'email') &&
                                                   !str_contains($p->name, 'api') && !str_contains($p->name, 'webhook') &&
                                                   !str_contains($p->name, 'template');
                                        })
                                    ];
                                @endphp

                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    @foreach($permissionGroups as $groupName => $groupPermissions)
                                        @if($groupPermissions->count() > 0)
                                            <div class="bg-white rounded-lg p-4 border border-gray-200">
                                                <h4 class="font-medium text-gray-900 mb-3 flex items-center gap-2">
                                                    <svg class="w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    {{ $groupName }}
                                                </h4>
                                                <div class="space-y-2">
                                                    @foreach($groupPermissions as $permission)
                                                        <label class="flex items-center cursor-pointer group">
                                                            <input type="checkbox" name="permissions[]" value="{{ $permission->id }}"
                                                                   {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}
                                                                   class="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2">
                                                            <span class="ml-2 text-sm text-gray-700 group-hover:text-gray-900">{{ $permission->label }}</span>
                                                        </label>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>

                                @error('permissions')
                                    <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-end gap-4 pt-8 border-t border-gray-200">
                            <a href="{{ route('roles.index') }}"
                               class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                İptal
                            </a>
                            <button type="submit"
                                    class="flex items-center gap-2 px-8 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Rol Oluştur
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        @push('scripts')
        <script>
        function selectAllPermissions() {
            const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        function deselectAllPermissions() {
            const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }
        </script>
        @endpush
    @endsection
</x-backend-layout>
