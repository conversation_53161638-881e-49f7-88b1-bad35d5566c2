<x-guest-layout>
    <div x-data="{
        name: '{{ old('name') }}',
        email: '{{ old('email') }}',
        password: '',
        passwordConfirmation: '',
        companyName: '{{ old('company_name') }}',
        cityId: '{{ old('city_id') }}',
        companyPhone: '{{ old('company_phone', '') }}',
        terms: false,
        loading: false,
        hasServerErrors: {{ $errors->any() ? 'true' : 'false' }},

        // Validation states
        nameValid: {{ old('name') ? 'true' : 'false' }},
        emailValid: {{ old('email') ? 'true' : 'false' }},
        passwordValid: false,
        passwordConfirmationValid: false,
        companyNameValid: {{ old('company_name') ? 'true' : 'false' }},
        cityIdValid: {{ old('city_id') ? 'true' : 'false' }},
        companyPhoneValid: {{ old('company_phone') ? 'true' : 'false' }},

        init() {
            if (this.hasServerErrors) {
                this.$nextTick(() => {
                    this.validateAll();
                });
            }
        },

        validateName() {
            this.nameValid = this.name.trim().length >= 2;
            return this.nameValid;
        },

        validateEmail() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            this.emailValid = emailRegex.test(this.email) && this.email.length > 0;
            return this.emailValid;
        },

        validatePassword() {
            this.passwordValid = this.password.length >= 8;
            return this.passwordValid;
        },

        validatePasswordConfirmation() {
            this.passwordConfirmationValid = this.passwordConfirmation === this.password && this.passwordConfirmation.length > 0;
            return this.passwordConfirmationValid;
        },

        validateCompanyName() {
            this.companyNameValid = this.companyName.trim().length >= 2;
            return this.companyNameValid;
        },

        validateCityId() {
            this.cityIdValid = this.cityId !== '';
            return this.cityIdValid;
        },

        validateCompanyPhone() {
            const phoneRegex = /^0\(\d{3}\)\d{3}-\d{2}-\d{2}$/;
            this.companyPhoneValid = phoneRegex.test(this.companyPhone);
            return this.companyPhoneValid;
        },

        validateAll() {
            this.validateName();
            this.validateEmail();
            this.validatePassword();
            this.validatePasswordConfirmation();
            this.validateCompanyName();
            this.validateCityId();
            this.validateCompanyPhone();
        },

        get isFormValid() {
            return this.nameValid && this.emailValid && this.passwordValid &&
                   this.passwordConfirmationValid && this.companyNameValid &&
                   this.cityIdValid && this.companyPhoneValid && this.terms;
        },

        async submitForm() {
            this.validateAll();

            if (this.isFormValid) {
                this.loading = true;
                await this.$nextTick();
                setTimeout(() => {
                    this.$refs.form.submit();
                }, 500);
            } else {
                const button = this.$refs.submitBtn;
                button.classList.add('shake');
                setTimeout(() => button.classList.remove('shake'), 500);
            }
        },

                formatPhone() {
            let numbers = this.companyPhone.replace(/\D/g, '');

            if (numbers.startsWith('0')) {
                numbers = numbers.substring(1);
            }

            numbers = numbers.substring(0, 10);

            let masked = '';
            if (numbers.length > 0) {
                masked = '0(' + numbers.substring(0, 3);
            }
            if (numbers.length > 3) {
                masked += ')' + numbers.substring(3, 6);
            }
            if (numbers.length > 6) {
                masked += '-' + numbers.substring(6, 8);
            }
            if (numbers.length > 8) {
                masked += '-' + numbers.substring(8, 10);
            }
            this.companyPhone = masked;
            this.validateCompanyPhone();
        },

        // Şifre gücü kontrolü
        strength: 0,
        strengthText: '',
        strengthColor: 'bg-gray-300',
        textColor: 'text-gray-500',

        checkStrength() {
            const pwd = this.password;
            if (!pwd) {
                this.strength = 0;
                this.strengthText = '';
                this.strengthColor = 'bg-gray-300';
                this.textColor = 'text-gray-500';
                return;
            }

            let score = 0;
            // En az 8 karakter
            if (pwd.length >= 8) score++;
            // Büyük harf
            if (/[A-Z]/.test(pwd)) score++;
            // Küçük harf
            if (/[a-z]/.test(pwd)) score++;
            // Rakam
            if (/[0-9]/.test(pwd)) score++;
            // Özel karakter
            if (/[^A-Za-z0-9]/.test(pwd)) score++;

            this.strength = (score / 5) * 100;

            if (score <= 1) {
                this.strengthText = 'Çok Zayıf';
                this.strengthColor = 'bg-red-500';
                this.textColor = 'text-red-500';
            } else if (score <= 2) {
                this.strengthText = 'Zayıf';
                this.strengthColor = 'bg-orange-500';
                this.textColor = 'text-orange-500';
            } else if (score <= 3) {
                this.strengthText = 'Orta';
                this.strengthColor = 'bg-blue-500';
                this.textColor = 'text-blue-500';
            } else if (score === 4) {
                this.strengthText = 'İyi';
                this.strengthColor = 'bg-green-500';
                this.textColor = 'text-green-500';
            } else if (score === 5) {
                this.strengthText = 'Mükemmel';
                this.strengthColor = 'bg-green-600';
                this.textColor = 'text-green-600';
            }
            else {
                this.strengthText = 'Çok Zayıf';
                this.strengthColor = 'bg-red-500';
                this.textColor = 'text-red-500';
            }
        }
    }">
        <form x-ref="form" method="POST" action="{{ route('register') }}" class="space-y-6" x-on:submit.prevent="submitForm()">
            @csrf
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">

        <!-- Name -->
        <div>
            <x-input-label for="name" value="Ad Soyad" />
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                    </svg>
                </div>
                <input type="text"
                        id="name"
                        name="name"
                        x-model="name"
                        x-on:input="validateName(); hasServerErrors = false"
                        required
                        autofocus
                        autocomplete="name"
                        placeholder="Adınız ve Soyadınız"
                        class="form-input pl-10 pr-12"
                        :class="{ 'error': (name.length > 0 && !nameValid) || (hasServerErrors && {{ $errors->has('name') ? 'true' : 'false' }}), 'success': nameValid && !hasServerErrors }" />
                <!-- Validation Icon -->
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="name.length > 0 && !hasServerErrors">
                    <svg x-show="nameValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <svg x-show="!nameValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            </div>

            <!-- Error Messages -->
            @if($errors->get('name'))
                <x-input-error :messages="$errors->get('name')" />
            @else
                <div x-show="name.length > 0 && !nameValid" class="error-message">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Ad Soyad en az 2 karakter olmalıdır
                </div>
            @endif
        </div>

        <!-- Email Address -->
        <div>
            <x-input-label for="email" value="E-posta Adresi" />
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
                      </svg>
                </div>
                <input type="email"
                    id="email"
                    name="email"
                    x-model="email"
                    x-on:input="validateEmail(); hasServerErrors = false"
                    required
                    autocomplete="username"
                    placeholder="<EMAIL>"
                    class="form-input pl-10 pr-12"
                    :class="{ 'error': (email.length > 0 && !emailValid) || (hasServerErrors && {{ $errors->has('email') ? 'true' : 'false' }}), 'success': emailValid && !hasServerErrors }" />

                <!-- Validation Icon -->
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="email.length > 0 && !hasServerErrors">
                    <svg x-show="emailValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <svg x-show="!emailValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            </div>

            <!-- Error Messages -->
            @if($errors->get('email'))
                <x-input-error :messages="$errors->get('email')" />
            @else
                <div x-show="email.length > 0 && !emailValid" class="error-message">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Geçerli bir e-posta adresi giriniz
                </div>
            @endif
        </div>

        <!-- Password -->
        <div>
            <x-input-label for="password" value="Şifre" />
            <div x-data="{ show: false }">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0V10.5m-.75 0h10.5m-10.5 0A2.25 2.25 0 006.75 12.75v6A2.25 2.25 0 009 21h6a2.25 2.25 0 002.25-2.25v-6A2.25 2.25 0 0016.5 10.5" />
                        </svg>
                    </div>
                    <input :type="show ? 'text' : 'password'"
                        id="password"
                        name="password"
                        x-model="password"
                        x-on:input="checkStrength(); validatePassword(); hasServerErrors = false"
                        required
                        autocomplete="new-password"
                        placeholder="En az 8 karakter"
                        class="form-input pl-10 pr-20"
                        :class="{ 'error': (password.length > 0 && !passwordValid) || (hasServerErrors && {{ $errors->has('password') ? 'true' : 'false' }}), 'success': passwordValid && !hasServerErrors }" />
                    <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center z-10" x-on:click="show = !show" aria-label="Şifreyi göster/gizle">
                        <svg x-show="!show" class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12c2.25-4.5 6.364-7.5 9.75-7.5s7.5 3 9.75 7.5c-2.25 4.5-6.364 7.5-9.75 7.5s-7.5-3-9.75-7.5z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <svg x-show="show" class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 3l18 18" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.5 12c2.25 4.5 6.364 7.5 9.75 7.5 1.711 0 3.348-.482 4.8-1.318" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.21 6.21C7.764 5.178 9.52 4.5 11.25 4.5c3.386 0 7.5 3 9.75 7.5a10.523 10.523 0 01-4.478 4.77" />
                        </svg>
                    </button>
                </div>

                <!-- Şifre Gücü Göstergesi -->
                <div x-show="password.length > 0" x-transition class="mt-2">
                    <div class="flex items-center justify-between text-xs mb-1">
                        <span x-text="strengthText" :class="textColor" class="font-medium"></span>
                        <span class="text-gray-500" x-text="`${password.length} karakter`"></span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="h-2 rounded-full transition-all duration-300 ease-out"
                             :style="`width: ${strength}%`"
                             :class="strengthColor"></div>
                    </div>
                </div>
            </div>

            <!-- Error Messages -->
            @if($errors->get('password'))
                <x-input-error :messages="$errors->get('password')" />
            @else
                <div x-show="password.length > 0 && !passwordValid" class="error-message">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Şifre en az 8 karakter olmalıdır
                </div>
            @endif
        </div>

        <!-- Confirm Password -->
        <div>
            <x-input-label for="password_confirmation" value="Şifre Tekrar" />
            <div class="relative" x-data="{ show: false }">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75l2.25 2.25L15.75 9.75m3 2.25a7.5 7.5 0 11-15 0 7.5 7.5 0 0115 0z" />
                    </svg>
                </div>
                <input :type="show ? 'text' : 'password'"
                    id="password_confirmation"
                    name="password_confirmation"
                    x-model="passwordConfirmation"
                    x-on:input="validatePasswordConfirmation(); hasServerErrors = false"
                    required
                    autocomplete="new-password"
                    placeholder="Şifrenizi tekrar girin"
                    class="form-input pl-10 pr-20"
                    :class="{ 'error': (passwordConfirmation.length > 0 && !passwordConfirmationValid) || (hasServerErrors && {{ $errors->has('password_confirmation') ? 'true' : 'false' }}), 'success': passwordConfirmationValid && !hasServerErrors }" />
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center z-10" x-on:click="show = !show" aria-label="Şifreyi göster/gizle">
                    <svg x-show="!show" class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12c2.25-4.5 6.364-7.5 9.75-7.5s7.5 3 9.75 7.5c-2.25 4.5-6.364 7.5-9.75 7.5s-7.5-3-9.75-7.5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <svg x-show="show" class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 3l18 18" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.5 12c2.25 4.5 6.364 7.5 9.75 7.5 1.711 0 3.348-.482 4.8-1.318" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.21 6.21C7.764 5.178 9.52 4.5 11.25 4.5c3.386 0 7.5 3 9.75 7.5a10.523 10.523 0 01-4.478 4.77" />
                    </svg>
                </button>

                <!-- Validation Icon -->
                <div class="absolute right-12 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="passwordConfirmation.length > 0 && !hasServerErrors">
                    <svg x-show="passwordConfirmationValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <svg x-show="!passwordConfirmationValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            </div>

            <!-- Error Messages -->
            @if($errors->get('password_confirmation'))
                <x-input-error :messages="$errors->get('password_confirmation')" />
            @else
                <div x-show="passwordConfirmation.length > 0 && !passwordConfirmationValid" class="error-message">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Şifreler eşleşmiyor
                </div>
            @endif
        </div>

        </div>
        <!-- Company Information -->
        <div class="space-y-4 pt-2">
            <h3 class="text-lg font-medium text-gray-900">Şirket Bilgileri</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            <!-- Company Name -->
            <div>
                <x-input-label for="company_name" value="Ofis (Firma) Kısa Adı" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z" />
                          </svg>

                    </div>
                    <input type="text"
                        id="company_name"
                        name="company_name"
                        x-model="companyName"
                        x-on:input="validateCompanyName(); hasServerErrors = false"
                        required
                        placeholder="ABC Emlak"
                        class="form-input pl-10 pr-12"
                        :class="{ 'error': (companyName.length > 0 && !companyNameValid) || (hasServerErrors && {{ $errors->has('company_name') ? 'true' : 'false' }}), 'success': companyNameValid && !hasServerErrors }" />

                    <!-- Validation Icon -->
                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="companyName.length > 0 && !hasServerErrors">
                        <svg x-show="companyNameValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <svg x-show="!companyNameValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                </div>

                <!-- Error Messages -->
                @if($errors->get('company_name'))
                    <x-input-error :messages="$errors->get('company_name')" />
                @else
                    <div x-show="companyName.length > 0 && !companyNameValid" class="error-message">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Şirket adı en az 2 karakter olmalıdır
                    </div>
                @endif
            </div>

            <!-- City -->
            <div>
                <x-input-label for="city_id" value="Şehir" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <svg class="h-5 w-5 text-base-content/80" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 10.5-7.5 10.5S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                        </svg>
                    </div>
                    <select id="city_id"
                        name="city_id"
                        x-model="cityId"
                        x-on:change="validateCityId(); hasServerErrors = false"
                        required
                        class="form-input pl-10 pr-12"
                        :class="{ 'error': (cityId !== '' && !cityIdValid) || (hasServerErrors && {{ $errors->has('city_id') ? 'true' : 'false' }}), 'success': cityIdValid && !hasServerErrors }">
                        <option value="" disabled>Şehir Seçiniz</option>
                        @foreach (\App\Models\City::all() as $city)
                            <option value="{{ $city->il_id }}" {{ old('city_id') == $city->il_id ? 'selected' : '' }}>
                                {{ $city->il_adi }}
                            </option>
                        @endforeach
                    </select>

                    <!-- Validation Icon -->
                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="cityId !== '' && !hasServerErrors">
                        <svg x-show="cityIdValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <svg x-show="!cityIdValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                </div>

                <!-- Error Messages -->
                @if($errors->get('city_id'))
                    <x-input-error :messages="$errors->get('city_id')" />
                @else
                    <div x-show="cityId === '' && cityId !== ''" class="error-message">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Lütfen bir şehir seçiniz
                    </div>
                @endif
            </div>

            <!-- Phone -->
            <div x-init="formatPhone()">
                <x-input-label for="company_phone" value="Cep Telefonu" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
                      </svg>

                    </div>
                    <input type="tel"
                        id="company_phone"
                        name="company_phone"
                        x-model="companyPhone"
                        x-on:input="formatPhone(); hasServerErrors = false"
                        required
                        placeholder="0(555)123-45-67"
                        class="form-input pl-10 pr-12"
                        :class="{ 'error': (companyPhone.length > 0 && !companyPhoneValid) || (hasServerErrors && {{ $errors->has('company_phone') ? 'true' : 'false' }}), 'success': companyPhoneValid && !hasServerErrors }"
                        maxlength="15" />

                    <!-- Validation Icon -->
                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none" x-show="companyPhone.length > 0 && !hasServerErrors">
                        <svg x-show="companyPhoneValid" class="text-green-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <svg x-show="!companyPhoneValid" class="text-red-500 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                </div>

                <!-- Error Messages -->
                @if($errors->get('company_phone'))
                    <x-input-error :messages="$errors->get('company_phone')" />
                @else
                    <div x-show="companyPhone.length > 0 && !companyPhoneValid" class="error-message">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Geçerli bir telefon numarası giriniz
                    </div>
                @endif
            </div>
            </div>
        </div>

        <!-- Terms and Conditions -->
        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="terms"
                    name="terms"
                    type="checkbox"
                    x-model="terms"
                    required
                    class="form-checkbox">
            </div>
            <div class="ml-3 text-sm">
                <label for="terms" class="text-gray-700">
                    <a href="#" class="text-primary-600 hover:text-primary-500 font-medium">Kullanım Şartları</a>
                    ve
                    <a href="#" class="text-primary-600 hover:text-primary-500 font-medium">Gizlilik Politikası</a>'nı
                    okudum ve kabul ediyorum.
                </label>
            </div>
        </div>

        <!-- Submit Button -->
        <div>
            <button x-ref="submitBtn" type="submit"
                    class="btn btn-primary w-full text-lg gap-3 py-4 shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
                    :disabled="loading || !isFormValid"
                    :class="{ 'opacity-50 cursor-not-allowed': loading || !isFormValid, 'cursor-pointer': !loading && isFormValid }"
                    x-bind:style="(loading || !isFormValid) ? 'pointer-events: none;' : 'pointer-events: auto;'">
                <svg x-show="!loading" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                <svg x-show="loading" class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span x-text="loading ? 'Hesap Oluşturuluyor...' : 'Hesap Oluştur'"></span>
                <svg x-show="!loading" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
            </button>
        </div>

        <!-- Login Link -->
        <div class="text-center">
            <p class="text-sm text-gray-600">
                Zaten hesabınız var mı?
                <a href="{{ route('login') }}" class="text-primary-600 hover:text-primary-500 font-medium">
                    Giriş Yapın
                </a>
            </p>
        </div>
        </form>
    </div>
</x-guest-layout>
