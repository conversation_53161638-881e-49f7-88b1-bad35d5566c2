<x-backend-layout>
    @section('content')
        <div class="container">
            <div class="page-title">
                <div class="grid grid-cols-12 mx-2 items-center">
                    <div class="col-span-6 sm:col-span-12">
                        <h3>Fiyatlandırma</h3>
                    </div>
                    <div class="col-span-6 sm:col-span-12">
                        <ol class="breadcrumb flex gap-2">
                            <li class="breadcrumb-item"><a href="{{ route('backend.onempanel') }}"> <svg class="stroke-icon">
                                        <use href="{{ asset('assets/backend/svg/icon-sprite.svg#stroke-home') }}"></use>
                                    </svg></a></li>
                            <li class="breadcrumb-item">Fiyatlandırma</li>
                            <li class="breadcrumb-item active">Paketler</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="grid grid-cols-12">
                <div class="col-span-12">
                    <div class="card">
                        <div class="card-header pb-0">
                            <div class="flex justify-between items-center mb-3">
                                <h5>Paket Listesi</h5>
                                <button class="btn btn-success text-white" onclick="openNewPackageModal()">
                                    <i class="fa-solid fa-plus me-2"></i>Yeni Paket Ekle
                                </button>
                            </div>
                        </div>
                        <div class="card-body pricing-content">
                            <div class="grid grid-cols-12 gap-4">
                                @foreach($packages as $package)
                                    <div class="col-span-4 xxl:col-span-6 sm:col-span-12 box-col-6">
                                        <div class="card text-center pricing-simple h-full {{ $package->is_popular ? 'border-primary shadow-lg' : '' }} position-relative">
                                            @if($package->is_popular)
                                                <div class="position-absolute top-0 end-0 m-3">
                                                    <span class="badge bg-primary">Popüler</span>
                                                </div>
                                            @endif
                                            <div class="card-body">
                                                <h4>{{ $package->name }}</h4>
                                                <h5>
                                                    @if($package->monthly_price == 0)
                                                        <span class="text-success">Ücretsiz</span>
                                                    @else
                                                        ₺{{ number_format($package->monthly_price, 0, ',', '.') }}<small class="text-muted">/ay</small>
                                                    @endif
                                                </h5>
                                                @if($package->yearly_price > 0 && $package->monthly_price > 0)
                                                    <p class="text-muted small">
                                                        Yıllık: ₺{{ number_format($package->yearly_price, 0, ',', '.') }}
                                                        <span class="text-success">({{ round((($package->monthly_price * 12) - $package->yearly_price) / ($package->monthly_price * 12) * 100) }}% indirim)</span>
                                                    </p>
                                                @endif
                                                @if($package->description)
                                                    <p class="text-muted mb-3">{{ $package->description }}</p>
                                                @endif
                                                <ul class="pricing-content">
                                                    @foreach($package->features as $feature)
                                                        <li><i class="fa-solid fa-circle-check"></i>{{ $feature }}</li>
                                                    @endforeach
                                                </ul>

                                                                                                @if($package->limits)
                                                    <div class="mt-3 pt-3 border-top">
                                                        <h6 class="text-muted mb-2">Paket Limitleri:</h6>
                                                        <ul class="list-unstyled text-sm text-start">
                                                            @if(isset($package->limits['max_staff']))
                                                                <li class="mb-1">
                                                                    <i class="fa-solid fa-users text-primary me-2"></i>
                                                                    @if($package->limits['max_staff'] == -1)
                                                                        Sınırsız personel
                                                                    @else
                                                                        Maksimum {{ $package->limits['max_staff'] }} personel
                                                                    @endif
                                                                </li>
                                                            @endif

                                                            @if(isset($package->limits['max_properties']))
                                                                <li class="mb-1">
                                                                    <i class="fa-solid fa-building text-primary me-2"></i>
                                                                    @if($package->limits['max_properties'] == -1)
                                                                        Sınırsız ilan
                                                                    @else
                                                                        Maksimum {{ $package->limits['max_properties'] }} ilan
                                                                    @endif
                                                                </li>
                                                            @endif

                                                            @if(isset($package->limits['max_storage_gb']))
                                                                <li class="mb-1">
                                                                    <i class="fa-solid fa-hard-drive text-primary me-2"></i>
                                                                    @if($package->limits['max_storage_gb'] == -1)
                                                                        Sınırsız depolama
                                                                    @else
                                                                        {{ $package->limits['max_storage_gb'] }} GB depolama
                                                                    @endif
                                                                </li>
                                                            @endif
                                                        </ul>
                                                    </div>
                                                @endif
                                            </div>
                                            <a class="btn {{ $package->is_popular ? 'btn-primary' : 'btn-outline-primary' }} btn-lg block rounded-lg {{ $package->is_popular ? 'text-white hover:text-white' : 'text-primary hover:text-white' }}"
                                                href="javascript:void(0)" onclick="editPackage({{ $package->id }})">
                                                <i class="fa-solid fa-edit me-2"></i>Paketi Düzenle
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Livewire Component -->
        @livewire('backend.package-editor')
    @endsection

    @push('scripts')
    <script>
        function editPackage(packageId) {
            // Loading göster
            Swal.fire({
                title: 'Paket bilgileri yükleniyor...',
                text: 'Lütfen bekleyiniz',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Livewire event'ini tetikle
            Livewire.dispatch('openEditModal', [packageId]);
        }

        function openNewPackageModal() {
            // Loading göster
            Swal.fire({
                title: 'Modal hazırlanıyor...',
                text: 'Lütfen bekleyiniz',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Yeni paket modalını aç
            Livewire.dispatch('openNewModal');
        }

        // Yeni paket ekleme butonu için
        document.addEventListener('DOMContentLoaded', function() {
            // Paket kaydedildiğinde sayfayı yenile
            Livewire.on('packageSaved', function() {
                // Kısa bir gecikme ile sayfayı yenile
                setTimeout(function() {
                    location.reload();
                }, 100);
            });

            // Modal kapandığında loading state'i temizle
            Livewire.on('modalClosed', function() {
                // Loading state temizleme işlemi
                Swal.close();
                console.log('Modal kapatıldı');
            });

            // Modal açıldığında loading'i kapat
            Livewire.on('modalOpened', function() {
                Swal.close();
                console.log('Modal açıldı');
            });

            // SweetAlert gösterme
            Livewire.on('showAlert', function(data) {
                console.log('SweetAlert tetiklendi:', data);
                // Kısa bir gecikme ile SweetAlert göster
                setTimeout(function() {
                    Swal.fire({
                        title: data[0].title,
                        html: data[0].message, // text yerine html kullan
                        icon: data[0].type,
                        confirmButtonText: 'Tamam',
                        confirmButtonColor: '#3085d6'
                    });
                }, 100);
            });
        });
    </script>
    @endpush
</x-backend-layout>
