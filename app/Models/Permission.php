<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    protected $fillable = [
        'name',
        'label',
    ];

    /**
     * Bu izne sahip şirket rollerini getir.
     */
    public function companyRoles(): BelongsToMany
    {
        return $this->belongsToMany(CompanyRole::class, 'company_role_permissions');
    }

    /**
     * Bu izne doğrudan sahip kullanıcıları getir (rol aracılığıyla değil).
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'company_user_permissions')
            ->withPivot('company_id')
            ->withTimestamps();
    }

    /**
     * İzni ada göre bul.
     */
    public static function findByName(string $name): ?self
    {
        return static::where('name', $name)->first();
    }

    /**
     * İzin yoksa oluştur.
     */
    public static function createIfNotExists(string $name, ?string $label = null): self
    {
        return static::firstOrCreate(
            ['name' => $name],
            ['label' => $label ?? $name]
        );
    }
}
