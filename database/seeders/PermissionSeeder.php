<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Global Permissions
        $permissions = [
            // User Management
            ['name' => 'manage_users', 'label' => 'Kullanıcı Yönetimi'],
            ['name' => 'view_users', 'label' => 'Kullanıcıları Görüntüle'],
            ['name' => 'create_users', 'label' => 'Kullanıcı Oluştur'],
            ['name' => 'edit_users', 'label' => 'Kullanıcı Düzenle'],
            ['name' => 'delete_users', 'label' => 'Kullanıcı Sil'],

            // Property Management
            ['name' => 'manage_properties', 'label' => 'Emlak Yönetimi'],
            ['name' => 'view_properties', 'label' => 'Emlakları Görüntüle'],
            ['name' => 'create_properties', 'label' => '<PERSON><PERSON> Oluştur'],
            ['name' => 'edit_properties', 'label' => 'Emlak Düzenle'],
            ['name' => 'delete_properties', 'label' => 'Emlak Sil'],
            ['name' => 'publish_properties', 'label' => 'Emlak Yayınla'],

            // Customer Management
            ['name' => 'manage_customers', 'label' => 'Müşteri Yönetimi'],
            ['name' => 'view_customers', 'label' => 'Müşterileri Görüntüle'],
            ['name' => 'create_customers', 'label' => 'Müşteri Oluştur'],
            ['name' => 'edit_customers', 'label' => 'Müşteri Düzenle'],
            ['name' => 'delete_customers', 'label' => 'Müşteri Sil'],

            // Reports
            ['name' => 'view_reports', 'label' => 'Raporları Görüntüle'],
            ['name' => 'export_reports', 'label' => 'Rapor Dışa Aktar'],
            ['name' => 'advanced_reports', 'label' => 'Gelişmiş Raporlar'],

            // Settings
            ['name' => 'manage_settings', 'label' => 'Ayar Yönetimi'],
            ['name' => 'manage_company', 'label' => 'Şirket Yönetimi'],
            ['name' => 'manage_roles', 'label' => 'Rol Yönetimi'],
            ['name' => 'manage_permissions', 'label' => 'İzin Yönetimi'],

            // Financial
            ['name' => 'view_financial', 'label' => 'Mali Bilgileri Görüntüle'],
            ['name' => 'manage_financial', 'label' => 'Mali Yönetim'],

            // Communication
            ['name' => 'send_messages', 'label' => 'Mesaj Gönder'],
            ['name' => 'send_emails', 'label' => 'E-posta Gönder'],
            ['name' => 'manage_templates', 'label' => 'Şablon Yönetimi'],

            // API Access
            ['name' => 'api_access', 'label' => 'API Erişimi'],
            ['name' => 'webhook_access', 'label' => 'Webhook Erişimi'],
        ];

        foreach ($permissions as $permission) {
            Permission::createIfNotExists($permission['name'], $permission['label']);
        }

        // Global Roles (opsiyonel - sistem yöneticileri için)
        $roles = [
            ['name' => 'super_admin', 'label' => 'Süper Yönetici'],
            ['name' => 'system_admin', 'label' => 'Sistem Yöneticisi'],
        ];

        foreach ($roles as $role) {
            Role::createIfNotExists($role['name'], $role['label']);
        }

        $this->command->info('Permissions and roles created successfully!');
    }
}
