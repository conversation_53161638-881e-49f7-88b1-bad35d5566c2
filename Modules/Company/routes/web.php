<?php

use Illuminate\Support\Facades\Route;
use Modules\Company\App\Http\Controllers\CompanyController;

/*
 *--------------------------------------------------------------------------
 * Company Module Web Routes
 *--------------------------------------------------------------------------
 */

Route::middleware(['auth', 'verified'])->group(function () {
    // Company management routes
    Route::get('/company', [CompanyController::class, 'index'])->name('company');
    Route::get('/company/create', [CompanyController::class, 'create'])->name('company.create');

    // Company settings routes
    Route::get('/company/settings', [CompanyController::class, 'settings'])->name('company.settings');
    Route::get('/settings', [CompanyController::class, 'settings'])->name('settings'); // Alias for settings

    // Company update routes
    Route::put('/company/basic-info', [CompanyController::class, 'updateBasicInfo'])->name('company.update-basic-info');
    Route::put('/company/social-media', [CompanyController::class, 'updateSocialMedia'])->name('company.update-social-media');
    Route::post('/company/upload-logo', [CompanyController::class, 'uploadLogo'])->name('company.upload-logo');
    Route::put('/company/settings-update', [CompanyController::class, 'updateSettings'])->name('company.update-settings');

    // AJAX routes
    Route::get('/company/get-districts', [CompanyController::class, 'getDistricts'])->name('company.get-districts');
});
