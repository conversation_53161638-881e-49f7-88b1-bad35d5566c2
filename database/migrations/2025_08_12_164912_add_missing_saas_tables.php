<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        /**
         * DISCOUNT COUPONS TABLE (önce oluştur - orders'da kullanılacak)
         */
        if (!Schema::hasTable('discount_coupons')) {
            Schema::create('discount_coupons', function (Blueprint $table) {
                $table->id();
                $table->string('code')->unique();
                $table->string('name');
                $table->text('description')->nullable();
                $table->enum('type', ['percentage', 'fixed_amount']);
                $table->decimal('value', 10, 2); // Yüzde veya sabit tutar
                $table->decimal('minimum_amount', 10, 2)->nullable(); // Minimum sipariş tutarı
                $table->integer('usage_limit')->nullable(); // Toplam kullanım limiti
                $table->integer('usage_limit_per_company')->nullable(); // Şirket başına kullanım limiti
                $table->integer('used_count')->default(0); // Kullanım sayısı
                $table->datetime('starts_at')->nullable();
                $table->datetime('expires_at')->nullable();
                $table->boolean('is_active')->default(true);
                $table->json('applicable_packages')->nullable(); // Hangi paketlerde geçerli
                $table->timestamps();
            });
        }

        /**
         * ORDERS TABLE (discount_coupons'dan sonra oluştur)
         */
        if (!Schema::hasTable('orders')) {
            Schema::create('orders', function (Blueprint $table) {
                $table->id();
                $table->uuid('company_id');
                $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
                $table->foreignId('package_id')->nullable()->constrained()->onDelete('set null');
                $table->foreignId('discount_coupon_id')->nullable()->constrained()->onDelete('set null');
                $table->string('order_number')->unique();
                $table->decimal('subtotal', 10, 2); // İndirim öncesi tutar
                $table->decimal('discount_amount', 10, 2)->default(0); // İndirim tutarı
                $table->decimal('amount', 10, 2); // Son tutar (subtotal - discount_amount)
                $table->string('currency_code', 3)->default('TRY');
                $table->string('status')->default('pending'); // pending, paid, cancelled
                $table->json('meta')->nullable(); // ödeme yöntemi, kampanya kodu vs.
                $table->timestamps();
            });
        }

        /**
         * PAYMENTS TABLE (orders'dan sonra oluştur)
         */
        if (!Schema::hasTable('payments')) {
            Schema::create('payments', function (Blueprint $table) {
                $table->id();
                $table->uuid('company_id');
                $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
                $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
                $table->string('payment_gateway')->nullable(); // iyzico, stripe vs.
                $table->string('transaction_id')->nullable(); // gateway'den gelen ID
                $table->decimal('amount', 10, 2);
                $table->string('currency_code', 3)->default('TRY');
                $table->enum('status', ['success', 'failed', 'refunded'])->default('success');
                $table->json('meta')->nullable();
                $table->timestamps();
            });
        }

        /**
         * SUBSCRIPTIONS TABLE
         */
        if (!Schema::hasTable('subscriptions')) {
            Schema::create('subscriptions', function (Blueprint $table) {
                $table->id();
                $table->uuid('company_id');
                $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
                $table->foreignId('package_id')->constrained()->onDelete('cascade');
                $table->string('status')->default('active'); // active, expired, cancelled
                $table->date('starts_at');
                $table->date('ends_at')->nullable();
                $table->boolean('auto_renew')->default(true);
                $table->timestamps();
            });
        }

        /**
         * INVOICES TABLE
         */
        if (!Schema::hasTable('invoices')) {
            Schema::create('invoices', function (Blueprint $table) {
                $table->id();
                $table->uuid('company_id');
                $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
                $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
                $table->string('invoice_number')->unique();
                $table->decimal('subtotal', 10, 2);
                $table->decimal('tax_rate', 5, 2)->default(18); // KDV oranı
                $table->decimal('tax_amount', 10, 2);
                $table->decimal('total_amount', 10, 2);
                $table->string('currency_code', 3)->default('TRY');
                $table->enum('status', ['draft', 'sent', 'paid', 'overdue', 'cancelled'])->default('draft');
                $table->date('issued_at');
                $table->date('due_at');
                $table->date('paid_at')->nullable();
                $table->text('notes')->nullable();
                $table->json('billing_address'); // Fatura adresi bilgileri
                $table->string('pdf_path')->nullable(); // PDF fatura dosya yolu
                $table->timestamps();
            });
        }

                /**
         * COUPON USAGES TABLE
         */
        if (!Schema::hasTable('coupon_usages')) {
            Schema::create('coupon_usages', function (Blueprint $table) {
                $table->id();
                $table->foreignId('discount_coupon_id')->constrained()->onDelete('cascade');
                $table->uuid('company_id');
                $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
                $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
                $table->decimal('discount_amount', 10, 2);
                $table->timestamps();

                $table->unique(['discount_coupon_id', 'company_id', 'order_id']);
            });
        }

        /**
         * TRANSACTION LOGS TABLE
         */
        if (!Schema::hasTable('transaction_logs')) {
            Schema::create('transaction_logs', function (Blueprint $table) {
                $table->id();
                $table->uuid('company_id');
                $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
                $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
                $table->foreignId('payment_id')->nullable()->constrained()->onDelete('set null');
                $table->string('action'); // order_created, payment_initiated, payment_success, payment_failed, subscription_created, etc.
                $table->string('status');
                $table->json('data')->nullable(); // Detaylı log verileri
                $table->string('ip_address')->nullable();
                $table->string('user_agent')->nullable();
                $table->timestamps();
            });
        }

        /**
         * Update ORDERS table to add missing columns
         */
        if (Schema::hasTable('orders')) {
            Schema::table('orders', function (Blueprint $table) {
                if (!Schema::hasColumn('orders', 'discount_coupon_id')) {
                    $table->foreignId('discount_coupon_id')->nullable()->constrained()->onDelete('set null');
                }
                if (!Schema::hasColumn('orders', 'subtotal')) {
                    $table->decimal('subtotal', 10, 2)->after('order_number');
                }
                if (!Schema::hasColumn('orders', 'discount_amount')) {
                    $table->decimal('discount_amount', 10, 2)->default(0)->after('subtotal');
                }
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('transaction_logs');
        Schema::dropIfExists('coupon_usages');
        Schema::dropIfExists('invoices');
        Schema::dropIfExists('subscriptions');
        Schema::dropIfExists('payments');

        // Orders tablosundaki eklenen kolonları kaldır
        if (Schema::hasTable('orders')) {
            Schema::table('orders', function (Blueprint $table) {
                if (Schema::hasColumn('orders', 'discount_coupon_id')) {
                    $table->dropForeign(['discount_coupon_id']);
                    $table->dropColumn('discount_coupon_id');
                }
                if (Schema::hasColumn('orders', 'subtotal')) {
                    $table->dropColumn('subtotal');
                }
                if (Schema::hasColumn('orders', 'discount_amount')) {
                    $table->dropColumn('discount_amount');
                }
            });
        }
    }
};
