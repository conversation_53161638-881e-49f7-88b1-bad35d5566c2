<!DOCTYPE html>
<html lang="tr" data-theme="light">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="robots" content="noindex,nofollow">
  <link rel="icon" href="{{ asset('assets/backend/images/favicon.png') }}" type="image/png">
  <title>{{ config('app.name', 'EmlakSis') }}</title>
  @vite(['resources/css/app.css','resources/js/app.js'])
  <link rel="preconnect" href="https://fonts.bunny.net">
  <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-100 font-inter">

    <!-- Dynamic Gradient Orb Background -->
    <div class="fixed inset-0 h-full w-full bg-gradient-to-br from-[#0A1640] via-[#0E1F5C] to-[#0A1640]">
        <div class="absolute -top-1/2 left-1/2 h-[150%] w-[150%] -translate-x-1/2 rounded-full bg-gradient-to-br from-[#0D2A63]/30 via-[#123B84]/20 to-[#1B4FCC]/10 blur-3xl"></div>

    </div>

  <div class="container mx-auto px-4 py-8 md:py-12 min-h-screen flex justify-center items-center">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-0 rounded-2xl overflow-hidden shadow-2xl bg-white border border-gray-200">

      <!-- Left Visual Panel -->
      <div class="hidden lg:block relative p-12 overflow-hidden bg-gradient-to-br from-[#0B1E3F] via-[#0D2A63] to-[#123B84]">


        <div class="relative z-10 h-full flex flex-col items-center justify-center text-center gap-8 text-white">

            <h2 class="text-4xl font-bold tracking-tight">EmlakSis'e Hoş Geldiniz</h2>
            <p class="text-xl/7 text-white/90 max-w-md font-light">Modern emlak yönetim platformu ile işinizi hızlandırın.</p>
            <div class="grid grid-cols-3 gap-6 w-full max-w-xl mt-4">
              <div class="bg-white/10 border border-white/20 rounded-xl transition-all duration-300 hover:bg-white/10 hover:scale-105 hover:rotate-2">
                <div class="flex flex-col items-center p-4">
                  <div class="p-3 rounded-full bg-white/10 mb-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10l9-7 9 7v8a2 2 0 01-2 2H5a2 2 0 01-2-2v-8z"/></svg>
                  </div>
                  <p class="text-sm font-medium">Kolay Yönetim</p>
                </div>
              </div>
              <div class="bg-white/10 border border-white/20 rounded-xl transition-all duration-300 hover:bg-white/10 hover:scale-105 hover:rotate-2">
                <div class="flex flex-col items-center p-4">
                  <div class="p-3 rounded-full bg-white/10 mb-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3v18m-7-7h18"/></svg>
                  </div>
                  <p class="text-sm font-medium">Güçlü Raporlama</p>
                </div>
              </div>
              <div class="bg-white/10 border border-white/20 rounded-xl transition-all duration-300 hover:bg-white/10 hover:scale-105 hover:rotate-2">
                <div class="flex flex-col items-center p-4">
                  <div class="p-3 rounded-full bg-white/10 mb-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M9 20H4v-2a3 3 0 015.356-1.857M15 7a3 3 0 11-6 0 3 3 0 016 0z"/></svg>
                  </div>
                  <p class="text-sm font-medium">Müşteri Yönetimi</p>
                </div>
              </div>
            </div>
          </div>
      </div>

      <!-- Right Form Slot -->
      <div class="bg-white/80">
        <div class="p-6 md:p-10 flex items-center min-h-full">
          <div class="w-full mx-auto">
            <div class="text-center mb-8">
              @php
                $currentRoute = request()->route()->getName();
                $pageConfig = [
                  'login' => [
                    'icon' => 'M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z',
                    'title' => 'Hesabınıza Giriş Yapın',
                    'subtitle' => 'Devam etmek için bilgilerinizi girin'
                  ],
                  'password.request' => [
                    'icon' => 'M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z',
                    'title' => 'Şifre Sıfırlama',
                    'subtitle' => 'E-posta adresinizi girin'
                  ],
                  'password.reset' => [
                    'icon' => 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
                    'title' => 'Yeni Şifre Belirle',
                    'subtitle' => 'Güvenli bir şifre oluşturun'
                  ],
                  'verification.notice' => [
                    'icon' => 'M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
                    'title' => 'E-posta Doğrulama',
                    'subtitle' => 'Hesabınızı doğrulayın'
                  ]
                ];
                $config = $pageConfig[$currentRoute] ?? $pageConfig['login'];
              @endphp

              <div class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $config['icon'] }}" />
                </svg>
              </div>
              <h1 class="text-2xl font-bold text-gray-900">{{ $config['title'] }}</h1>
              <p class="text-gray-600 mt-2">{{ $config['subtitle'] }}</p>
            </div>

            <div class="space-y-6">
              {{ $slot }}
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

  <style>
  @keyframes spin-slow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .animate-spin-slow {
    animation: spin-slow 60s linear infinite;
  }
  /* subtle floating shapes */
  .shape {
    position: absolute;
    opacity: 0.8;
    filter: drop-shadow(0 0 10px rgba(255,255,255,0.05));
    animation: float-shape 24s ease-in-out infinite;
  }
  .shape svg { display: block; }
  @keyframes float-shape {
    0% { transform: translate3d(0, 0, 0) rotate(0deg); }
    50% { transform: translate3d(10px, -14px, 0) rotate(3deg); }
    100% { transform: translate3d(0, 0, 0) rotate(0deg); }
  }

  /* Custom modern scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background-color: #0A1640;
  }
  ::-webkit-scrollbar-thumb {
    background-color: rgba(107, 164, 255, 0.4); /* #6BA4FF with opacity */
    border-radius: 10px;
    border: 2px solid #0A1640; /* Padding effect */
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 164, 255, 0.6);
  }
  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin; /* can be 'auto' or 'thin' */
    scrollbar-color: rgba(107, 164, 255, 0.4) #0A1640;
  }
  </style>

  <!-- SweetAlert2 for Toast Notifications -->
  @if(session('status'))
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>
  Swal.fire({
      title: @json(session('responseTitle')),
      text: @json(session('responseMessage')),
      icon: "{{ session('status') == 'success' ? 'success' : 'error' }}",
      timer: 3000,
      timerProgressBar: true,
      showConfirmButton: false,
      toast: true,
      position: 'top-end'
  });
  </script>
  @endif
</body>
</html>
